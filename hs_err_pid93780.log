#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 348127232 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3614), pid=93780, tid=38364
#
# JRE version: OpenJDK Runtime Environment Corretto-**********.1 (17.0.13+11) (build 17.0.13+11-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-**********.1 (17.0.13+11-LTS, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:63749,suspend=y,server=n -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.2\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture42380.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dfile.encoding=UTF-8 cn.coder.zj.module.collector.Collector

Host: Intel(R) Core(TM) i5-14600K, 20 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
Time: Wed Apr 23 11:56:06 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5678) elapsed time: 1331.892882 seconds (0d 0h 22m 11s)

---------------  T H R E A D  ---------------

Current thread (0x00000193b9ae3010):  VMThread "VM Thread" [stack: 0x0000002ba3f00000,0x0000002ba4000000] [id=38364]

Stack: [0x0000002ba3f00000,0x0000002ba4000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x680c19]
V  [jvm.dll+0x83888a]
V  [jvm.dll+0x83a34e]
V  [jvm.dll+0x83a9b3]
V  [jvm.dll+0x2481af]
V  [jvm.dll+0x67d959]
V  [jvm.dll+0x67242a]
V  [jvm.dll+0x30813b]
V  [jvm.dll+0x30f626]
V  [jvm.dll+0x35f94e]
V  [jvm.dll+0x35fb8f]
V  [jvm.dll+0x2df0ec]
V  [jvm.dll+0x2e2166]
V  [jvm.dll+0x2ec50a]
V  [jvm.dll+0x3205a6]
V  [jvm.dll+0x83eead]
V  [jvm.dll+0x83fbf2]
V  [jvm.dll+0x84011f]
V  [jvm.dll+0x840504]
V  [jvm.dll+0x8405d0]
V  [jvm.dll+0x7e7b2c]
V  [jvm.dll+0x67fae7]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x0000002ba3bff4b0): G1PauseRemark, mode: safepoint, requested by thread 0x0000019398659660


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000019381700eb0, length=61, elements={
0x00000193b6a15820, 0x00000193b6a163b0, 0x00000193b6a25a20, 0x00000193b9aee660,
0x00000193b9af00a0, 0x00000193b9af0970, 0x00000193b9af1240, 0x00000193b9af1b50,
0x00000193b69fcfd0, 0x00000193b9d07080, 0x00000193b9d0b340, 0x00000193b9d1ae00,
0x00000193b9d4a7f0, 0x00000193ff7e7020, 0x000001938062bcc0, 0x000001938062c1a0,
0x0000019380632d50, 0x00000193804dd570, 0x00000193804de4a0, 0x00000193804deec0,
0x00000193804de9b0, 0x00000193804df3d0, 0x00000193804df8e0, 0x00000193814b2a80,
0x00000193814b4df0, 0x00000193814b5300, 0x00000193814b34a0, 0x00000193814aedc0,
0x00000193814b48e0, 0x00000193814af7e0, 0x00000193814afcf0, 0x00000193814b0200,
0x00000193814b0710, 0x00000193814b1130, 0x0000019381874e50, 0x000001938187b390,
0x0000019381879f50, 0x00000193818767a0, 0x00000193818776d0, 0x0000019381878600,
0x0000019381876290, 0x0000019381874430, 0x0000019381875d80, 0x0000019383542570,
0x0000019383541130, 0x00000193835434a0, 0x00000193814b43d0, 0x00000193814ae3a0,
0x00000193814b39b0, 0x00000193814b5d20, 0x000001938187a970, 0x00000193818780f0,
0x000001938187ae80, 0x0000019381876cb0, 0x0000019381873f20, 0x0000019381875870,
0x00000193814b5810, 0x00000193814ae8b0, 0x00000193814b3ec0, 0x00000193814b0c20,
0x000001938032baf0
}

Java Threads: ( => current thread )
  0x00000193b6a15820 JavaThread "Reference Handler" daemon [_thread_blocked, id=108996, stack(0x0000002ba4000000,0x0000002ba4100000)]
  0x00000193b6a163b0 JavaThread "Finalizer" daemon [_thread_blocked, id=109764, stack(0x0000002ba4100000,0x0000002ba4200000)]
  0x00000193b6a25a20 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=102412, stack(0x0000002ba4200000,0x0000002ba4300000)]
  0x00000193b9aee660 JavaThread "Attach Listener" daemon [_thread_blocked, id=110508, stack(0x0000002ba4300000,0x0000002ba4400000)]
  0x00000193b9af00a0 JavaThread "Service Thread" daemon [_thread_blocked, id=1392, stack(0x0000002ba4400000,0x0000002ba4500000)]
  0x00000193b9af0970 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=110296, stack(0x0000002ba4500000,0x0000002ba4600000)]
  0x00000193b9af1240 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=35604, stack(0x0000002ba4600000,0x0000002ba4700000)]
  0x00000193b9af1b50 JavaThread "Sweeper thread" daemon [_thread_blocked, id=107776, stack(0x0000002ba4700000,0x0000002ba4800000)]
  0x00000193b69fcfd0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=100432, stack(0x0000002ba4800000,0x0000002ba4900000)]
  0x00000193b9d07080 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=71688, stack(0x0000002ba4c00000,0x0000002ba4d00000)]
  0x00000193b9d0b340 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=109464, stack(0x0000002ba4d00000,0x0000002ba4e00000)]
  0x00000193b9d1ae00 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=100080, stack(0x0000002ba4e00000,0x0000002ba4f00000)]
  0x00000193b9d4a7f0 JavaThread "Notification Thread" daemon [_thread_blocked, id=106160, stack(0x0000002ba4f00000,0x0000002ba5000000)]
  0x00000193ff7e7020 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=110256, stack(0x0000002ba5b00000,0x0000002ba5c00000)]
  0x000001938062bcc0 JavaThread "AsyncAppender-Worker-async_stdout" daemon [_thread_blocked, id=109948, stack(0x0000002ba6100000,0x0000002ba6200000)]
  0x000001938062c1a0 JavaThread "AsyncAppender-Worker-async_file" daemon [_thread_blocked, id=109020, stack(0x0000002ba6200000,0x0000002ba6300000)]
  0x0000019380632d50 JavaThread "AsyncAppender-Worker-async_business" daemon [_thread_blocked, id=107552, stack(0x0000002ba6300000,0x0000002ba6400000)]
  0x00000193804dd570 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=11808, stack(0x0000002ba6800000,0x0000002ba6900000)]
  0x00000193804de4a0 JavaThread "Catalina-utility-1" [_thread_blocked, id=41028, stack(0x0000002ba6900000,0x0000002ba6a00000)]
  0x00000193804deec0 JavaThread "Catalina-utility-2" [_thread_blocked, id=95672, stack(0x0000002ba6a00000,0x0000002ba6b00000)]
  0x00000193804de9b0 JavaThread "container-0" [_thread_blocked, id=107032, stack(0x0000002ba6b00000,0x0000002ba6c00000)]
  0x00000193804df3d0 JavaThread "http-nio-1160-exec-1" daemon [_thread_blocked, id=96484, stack(0x0000002ba6c00000,0x0000002ba6d00000)]
  0x00000193804df8e0 JavaThread "http-nio-1160-Poller" daemon [_thread_in_native, id=108576, stack(0x0000002ba6d00000,0x0000002ba6e00000)]
  0x00000193814b2a80 JavaThread "http-nio-1160-Acceptor" daemon [_thread_in_native, id=102564, stack(0x0000002ba6e00000,0x0000002ba6f00000)]
  0x00000193814b4df0 JavaThread "async-1" [_thread_blocked, id=94008, stack(0x0000002ba6f00000,0x0000002ba7000000)]
  0x00000193814b5300 JavaThread "DestroyJavaVM" [_thread_blocked, id=18980, stack(0x0000002ba3900000,0x0000002ba3a00000)]
  0x00000193814b34a0 JavaThread "nioEventLoopGroup-2-1" [_thread_in_native, id=57440, stack(0x0000002ba7000000,0x0000002ba7100000)]
  0x00000193814aedc0 JavaThread "async-2" [_thread_blocked, id=102464, stack(0x0000002ba7200000,0x0000002ba7300000)]
  0x00000193814b48e0 JavaThread "async-10" [_thread_in_native, id=98292, stack(0x0000002ba7a00000,0x0000002ba7b00000)]
  0x00000193814af7e0 JavaThread "YQJob-1" [_thread_blocked, id=109564, stack(0x0000002ba8000000,0x0000002ba8100000)]
  0x00000193814afcf0 JavaThread "YQJob-2" [_thread_blocked, id=106220, stack(0x0000002ba8100000,0x0000002ba8200000)]
  0x00000193814b0200 JavaThread "YQJob-3" [_thread_blocked, id=35092, stack(0x0000002ba8200000,0x0000002ba8300000)]
  0x00000193814b0710 JavaThread "YQJob-4" [_thread_blocked, id=16056, stack(0x0000002ba8300000,0x0000002ba8400000)]
  0x00000193814b1130 JavaThread "YQJob-5" [_thread_blocked, id=49128, stack(0x0000002ba8400000,0x0000002ba8500000)]
  0x0000019381874e50 JavaThread "OkHttp ConnectionPool" daemon [_thread_blocked, id=106944, stack(0x0000002ba8900000,0x0000002ba8a00000)]
  0x000001938187b390 JavaThread "OkHttp ConnectionPool" daemon [_thread_blocked, id=94252, stack(0x0000002ba8a00000,0x0000002ba8b00000)]
  0x0000019381879f50 JavaThread "Okio Watchdog" daemon [_thread_blocked, id=94800, stack(0x0000002ba8b00000,0x0000002ba8c00000)]
  0x00000193818767a0 JavaThread "async-18" [_thread_blocked, id=100948, stack(0x0000002ba8d00000,0x0000002ba8e00000)]
  0x00000193818776d0 JavaThread "YQJob-6" [_thread_blocked, id=30480, stack(0x0000002ba9000000,0x0000002ba9100000)]
  0x0000019381878600 JavaThread "YQJob-7" [_thread_blocked, id=102740, stack(0x0000002ba9100000,0x0000002ba9200000)]
  0x0000019381876290 JavaThread "YQJob-8" [_thread_blocked, id=110020, stack(0x0000002ba9200000,0x0000002ba9300000)]
  0x0000019381874430 JavaThread "YQJob-9" [_thread_blocked, id=85912, stack(0x0000002ba9300000,0x0000002ba9400000)]
  0x0000019381875d80 JavaThread "YQJob-10" [_thread_blocked, id=105504, stack(0x0000002ba9400000,0x0000002ba9500000)]
  0x0000019383542570 JavaThread "async-21" [_thread_blocked, id=98256, stack(0x0000002ba9800000,0x0000002ba9900000)]
  0x0000019383541130 JavaThread "async-22" [_thread_blocked, id=109488, stack(0x0000002ba7c00000,0x0000002ba7d00000)]
  0x00000193835434a0 JavaThread "async-27" [_thread_in_native, id=112428, stack(0x0000002ba3800000,0x0000002ba3900000)]
  0x00000193814b43d0 JavaThread "async-30" [_thread_in_native, id=111636, stack(0x0000002ba3700000,0x0000002ba3800000)]
  0x00000193814ae3a0 JavaThread "async-31" [_thread_in_native, id=119240, stack(0x0000002ba4b00000,0x0000002ba4c00000)]
  0x00000193814b39b0 JavaThread "async-33" [_thread_blocked, id=118828, stack(0x0000002ba7400000,0x0000002ba7500000)]
  0x00000193814b5d20 JavaThread "async-34" [_thread_blocked, id=118020, stack(0x0000002ba4a00000,0x0000002ba4b00000)]
  0x000001938187a970 JavaThread "async-36" [_thread_blocked, id=122504, stack(0x0000002ba4900000,0x0000002ba4a00000)]
  0x00000193818780f0 JavaThread "async-37" [_thread_blocked, id=119376, stack(0x0000002ba7500000,0x0000002ba7600000)]
  0x000001938187ae80 JavaThread "async-38" [_thread_blocked, id=7544, stack(0x0000002ba7600000,0x0000002ba7700000)]
  0x0000019381876cb0 JavaThread "async-40" [_thread_blocked, id=118936, stack(0x0000002ba7e00000,0x0000002ba7f00000)]
  0x0000019381873f20 JavaThread "async-41" [_thread_blocked, id=103040, stack(0x0000002ba5d00000,0x0000002ba5e00000)]
  0x0000019381875870 JavaThread "async-42" [_thread_blocked, id=102200, stack(0x0000002ba6400000,0x0000002ba6500000)]
  0x00000193814b5810 JavaThread "async-43" [_thread_blocked, id=122012, stack(0x0000002ba7300000,0x0000002ba7400000)]
  0x00000193814ae8b0 JavaThread "async-44" [_thread_blocked, id=110996, stack(0x0000002ba7900000,0x0000002ba7a00000)]
  0x00000193814b3ec0 JavaThread "async-45" [_thread_blocked, id=44976, stack(0x0000002ba7700000,0x0000002ba7800000)]
  0x00000193814b0c20 JavaThread "Keep-Alive-Timer" daemon [_thread_blocked, id=48964, stack(0x0000002ba3600000,0x0000002ba3700000)]
  0x000001938032baf0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=122924, stack(0x0000002ba7800000,0x0000002ba7900000)]

Other Threads:
=>0x00000193b9ae3010 VMThread "VM Thread" [stack: 0x0000002ba3f00000,0x0000002ba4000000] [id=38364]
  0x00000193ff7ecb70 WatcherThread [stack: 0x0000002ba5c00000,0x0000002ba5d00000] [id=102180]
  0x00000193b6870900 GCTaskThread "GC Thread#0" [stack: 0x0000002ba3a00000,0x0000002ba3b00000] [id=109500]
  0x00000193ff722080 GCTaskThread "GC Thread#1" [stack: 0x0000002ba5000000,0x0000002ba5100000] [id=30416]
  0x00000193ff722750 GCTaskThread "GC Thread#2" [stack: 0x0000002ba5100000,0x0000002ba5200000] [id=90384]
  0x00000193ff722a10 GCTaskThread "GC Thread#3" [stack: 0x0000002ba5200000,0x0000002ba5300000] [id=107860]
  0x00000193ff7230e0 GCTaskThread "GC Thread#4" [stack: 0x0000002ba5300000,0x0000002ba5400000] [id=107876]
  0x00000193ff725fa0 GCTaskThread "GC Thread#5" [stack: 0x0000002ba5400000,0x0000002ba5500000] [id=110324]
  0x00000193ff726260 GCTaskThread "GC Thread#6" [stack: 0x0000002ba5500000,0x0000002ba5600000] [id=109924]
  0x00000193ff725ce0 GCTaskThread "GC Thread#7" [stack: 0x0000002ba5600000,0x0000002ba5700000] [id=89300]
  0x00000193ff7267e0 GCTaskThread "GC Thread#8" [stack: 0x0000002ba5700000,0x0000002ba5800000] [id=101456]
  0x00000193ff726520 GCTaskThread "GC Thread#9" [stack: 0x0000002ba5800000,0x0000002ba5900000] [id=91548]
  0x00000193ff725760 GCTaskThread "GC Thread#10" [stack: 0x0000002ba5900000,0x0000002ba5a00000] [id=16580]
  0x00000193ff7251e0 GCTaskThread "GC Thread#11" [stack: 0x0000002ba5a00000,0x0000002ba5b00000] [id=94704]
  0x00000193ff724c60 GCTaskThread "GC Thread#12" [stack: 0x0000002ba5e00000,0x0000002ba5f00000] [id=95880]
  0x00000193ff724f20 GCTaskThread "GC Thread#13" [stack: 0x0000002ba5f00000,0x0000002ba6000000] [id=110396]
  0x00000193ff7254a0 GCTaskThread "GC Thread#14" [stack: 0x0000002ba6000000,0x0000002ba6100000] [id=98288]
  0x0000019398659660 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000002ba3b00000,0x0000002ba3c00000] [id=76764]
  0x0000019398659e90 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000002ba3c00000,0x0000002ba3d00000] [id=107716]
  0x00000193804f8c80 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000002ba6500000,0x0000002ba6600000] [id=108148]
  0x00000193804fa800 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000002ba6600000,0x0000002ba6700000] [id=14872]
  0x00000193804f9d00 ConcurrentGCThread "G1 Conc#3" [stack: 0x0000002ba6700000,0x0000002ba6800000] [id=109528]
  0x00000193b694a510 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000002ba3d00000,0x0000002ba3e00000] [id=95876]
  0x00000193830ca420 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000002ba7b00000,0x0000002ba7c00000] [id=115328]
  0x00000193830c9b50 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000002ba7d00000,0x0000002ba7e00000] [id=112004]
  0x00000193830c9e40 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000002ba7f00000,0x0000002ba8000000] [id=115552]
  0x0000019381bd21c0 ConcurrentGCThread "G1 Refine#4" [stack: 0x0000002ba8500000,0x0000002ba8600000] [id=94336]
  0x00000193b694ad50 ConcurrentGCThread "G1 Service" [stack: 0x0000002ba3e00000,0x0000002ba3f00000] [id=105228]

Threads with active compile tasks:

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000193985d58c0] Threads_lock - owner thread: 0x00000193b9ae3010
[0x00000193985d5a70] Heap_lock - owner thread: 0x0000019398659660

Heap address: 0x0000000604000000, size: 8128 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x00000193ba000000-0x00000193babb0000-0x00000193babb0000), size 12255232, SharedBaseAddress: 0x00000193ba000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000193bb000000-0x00000193fb000000, reserved size: 1073741824
Narrow klass base: 0x00000193ba000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 20 total, 20 available
 Memory: 32509M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8128M
 Pre-touch: Disabled
 Parallel Workers: 15
 Concurrent Workers: 4
 Concurrent Refinement Workers: 15
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 638976K, used 576436K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 57705K, committed 58560K, reserved 1114112K
  class space    used 7676K, committed 8064K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604000000, 0x0000000604400000, 0x0000000604400000|100%| O|  |TAMS 0x0000000604400000, 0x0000000604000000| Untracked 
|   1|0x0000000604400000, 0x0000000604800000, 0x0000000604800000|100%| O|  |TAMS 0x0000000604800000, 0x0000000604400000| Untracked 
|   2|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%| O|  |TAMS 0x0000000604c00000, 0x0000000604800000| Updating 
|   3|0x0000000604c00000, 0x0000000605000000, 0x0000000605000000|100%| O|  |TAMS 0x0000000605000000, 0x0000000604c00000| Updating 
|   4|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605000000| Updating 
|   5|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605800000, 0x0000000605400000| Updating 
|   6|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605c00000, 0x0000000605800000| Updating 
|   7|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%|HS|  |TAMS 0x0000000606000000, 0x0000000605c00000| Complete 
|   8|0x0000000606000000, 0x0000000606400000, 0x0000000606400000|100%|HC|  |TAMS 0x0000000606400000, 0x0000000606000000| Complete 
|   9|0x0000000606400000, 0x0000000606800000, 0x0000000606800000|100%|HC|  |TAMS 0x0000000606800000, 0x0000000606400000| Complete 
|  10|0x0000000606800000, 0x0000000606c00000, 0x0000000606c00000|100%|HC|  |TAMS 0x0000000606c00000, 0x0000000606800000| Complete 
|  11|0x0000000606c00000, 0x0000000607000000, 0x0000000607000000|100%|HC|  |TAMS 0x0000000607000000, 0x0000000606c00000| Complete 
|  12|0x0000000607000000, 0x0000000607400000, 0x0000000607400000|100%| O|  |TAMS 0x0000000607400000, 0x0000000607000000| Updating 
|  13|0x0000000607400000, 0x0000000607800000, 0x0000000607800000|100%| O|  |TAMS 0x0000000607800000, 0x0000000607400000| Updating 
|  14|0x0000000607800000, 0x0000000607c00000, 0x0000000607c00000|100%| O|  |TAMS 0x0000000607c00000, 0x0000000607800000| Updating 
|  15|0x0000000607c00000, 0x0000000608000000, 0x0000000608000000|100%| O|  |TAMS 0x0000000608000000, 0x0000000607c00000| Untracked 
|  16|0x0000000608000000, 0x0000000608400000, 0x0000000608400000|100%| O|  |TAMS 0x0000000608400000, 0x0000000608000000| Updating 
|  17|0x0000000608400000, 0x0000000608800000, 0x0000000608800000|100%| O|  |TAMS 0x0000000608800000, 0x0000000608400000| Updating 
|  18|0x0000000608800000, 0x0000000608c00000, 0x0000000608c00000|100%| O|  |TAMS 0x0000000608c00000, 0x0000000608800000| Updating 
|  19|0x0000000608c00000, 0x0000000609000000, 0x0000000609000000|100%| O|  |TAMS 0x0000000609000000, 0x0000000608c00000| Updating 
|  20|0x0000000609000000, 0x0000000609400000, 0x0000000609400000|100%| O|  |TAMS 0x0000000609400000, 0x0000000609000000| Updating 
|  21|0x0000000609400000, 0x0000000609800000, 0x0000000609800000|100%| O|  |TAMS 0x0000000609800000, 0x0000000609400000| Updating 
|  22|0x0000000609800000, 0x0000000609c00000, 0x0000000609c00000|100%| O|  |TAMS 0x0000000609c00000, 0x0000000609800000| Updating 
|  23|0x0000000609c00000, 0x000000060a000000, 0x000000060a000000|100%| O|  |TAMS 0x000000060a000000, 0x0000000609c00000| Updating 
|  24|0x000000060a000000, 0x000000060a400000, 0x000000060a400000|100%| O|  |TAMS 0x000000060a400000, 0x000000060a000000| Updating 
|  25|0x000000060a400000, 0x000000060a800000, 0x000000060a800000|100%| O|  |TAMS 0x000000060a800000, 0x000000060a400000| Updating 
|  26|0x000000060a800000, 0x000000060ac00000, 0x000000060ac00000|100%| O|  |TAMS 0x000000060ac00000, 0x000000060a800000| Updating 
|  27|0x000000060ac00000, 0x000000060b000000, 0x000000060b000000|100%| O|  |TAMS 0x000000060b000000, 0x000000060ac00000| Updating 
|  28|0x000000060b000000, 0x000000060b400000, 0x000000060b400000|100%| O|  |TAMS 0x000000060b400000, 0x000000060b000000| Updating 
|  29|0x000000060b400000, 0x000000060b800000, 0x000000060b800000|100%| O|  |TAMS 0x000000060b800000, 0x000000060b400000| Updating 
|  30|0x000000060b800000, 0x000000060bc00000, 0x000000060bc00000|100%| O|  |TAMS 0x000000060bc00000, 0x000000060b800000| Updating 
|  31|0x000000060bc00000, 0x000000060c000000, 0x000000060c000000|100%| O|  |TAMS 0x000000060c000000, 0x000000060bc00000| Updating 
|  32|0x000000060c000000, 0x000000060c400000, 0x000000060c400000|100%| O|  |TAMS 0x000000060c400000, 0x000000060c000000| Updating 
|  33|0x000000060c400000, 0x000000060c800000, 0x000000060c800000|100%| O|  |TAMS 0x000000060c800000, 0x000000060c400000| Updating 
|  34|0x000000060c800000, 0x000000060cc00000, 0x000000060cc00000|100%| O|  |TAMS 0x000000060cc00000, 0x000000060c800000| Updating 
|  35|0x000000060cc00000, 0x000000060d000000, 0x000000060d000000|100%| O|  |TAMS 0x000000060d000000, 0x000000060cc00000| Updating 
|  36|0x000000060d000000, 0x000000060d400000, 0x000000060d400000|100%| O|  |TAMS 0x000000060d400000, 0x000000060d000000| Updating 
|  37|0x000000060d400000, 0x000000060d800000, 0x000000060d800000|100%| O|  |TAMS 0x000000060d800000, 0x000000060d400000| Updating 
|  38|0x000000060d800000, 0x000000060dbffff8, 0x000000060dc00000| 99%| O|  |TAMS 0x000000060dbffff8, 0x000000060d800000| Updating 
|  39|0x000000060dc00000, 0x000000060e000000, 0x000000060e000000|100%| O|  |TAMS 0x000000060e000000, 0x000000060dc00000| Updating 
|  40|0x000000060e000000, 0x000000060e400000, 0x000000060e400000|100%| O|  |TAMS 0x000000060e400000, 0x000000060e000000| Updating 
|  41|0x000000060e400000, 0x000000060e800000, 0x000000060e800000|100%| O|  |TAMS 0x000000060e800000, 0x000000060e400000| Updating 
|  42|0x000000060e800000, 0x000000060ec00000, 0x000000060ec00000|100%| O|  |TAMS 0x000000060ec00000, 0x000000060e800000| Updating 
|  43|0x000000060ec00000, 0x000000060f000000, 0x000000060f000000|100%| O|  |TAMS 0x000000060f000000, 0x000000060ec00000| Updating 
|  44|0x000000060f000000, 0x000000060f400000, 0x000000060f400000|100%| O|  |TAMS 0x000000060f400000, 0x000000060f000000| Updating 
|  45|0x000000060f400000, 0x000000060f800000, 0x000000060f800000|100%| O|  |TAMS 0x000000060f800000, 0x000000060f400000| Updating 
|  46|0x000000060f800000, 0x000000060fc00000, 0x000000060fc00000|100%| O|  |TAMS 0x000000060fc00000, 0x000000060f800000| Updating 
|  47|0x000000060fc00000, 0x0000000610000000, 0x0000000610000000|100%| O|  |TAMS 0x0000000610000000, 0x000000060fc00000| Updating 
|  48|0x0000000610000000, 0x0000000610400000, 0x0000000610400000|100%| O|  |TAMS 0x0000000610400000, 0x0000000610000000| Updating 
|  49|0x0000000610400000, 0x0000000610800000, 0x0000000610800000|100%| O|  |TAMS 0x0000000610800000, 0x0000000610400000| Updating 
|  50|0x0000000610800000, 0x0000000610c00000, 0x0000000610c00000|100%| O|  |TAMS 0x0000000610c00000, 0x0000000610800000| Updating 
|  51|0x0000000610c00000, 0x0000000611000000, 0x0000000611000000|100%| O|  |TAMS 0x0000000611000000, 0x0000000610c00000| Updating 
|  52|0x0000000611000000, 0x0000000611400000, 0x0000000611400000|100%| O|  |TAMS 0x0000000611400000, 0x0000000611000000| Updating 
|  53|0x0000000611400000, 0x0000000611800000, 0x0000000611800000|100%| O|  |TAMS 0x0000000611800000, 0x0000000611400000| Updating 
|  54|0x0000000611800000, 0x0000000611c00000, 0x0000000611c00000|100%| O|  |TAMS 0x0000000611c00000, 0x0000000611800000| Updating 
|  55|0x0000000611c00000, 0x0000000612000000, 0x0000000612000000|100%| O|  |TAMS 0x0000000612000000, 0x0000000611c00000| Updating 
|  56|0x0000000612000000, 0x0000000612400000, 0x0000000612400000|100%| O|  |TAMS 0x0000000612400000, 0x0000000612000000| Updating 
|  57|0x0000000612400000, 0x0000000612800000, 0x0000000612800000|100%| O|  |TAMS 0x0000000612800000, 0x0000000612400000| Updating 
|  58|0x0000000612800000, 0x0000000612c00000, 0x0000000612c00000|100%| O|  |TAMS 0x0000000612c00000, 0x0000000612800000| Updating 
|  59|0x0000000612c00000, 0x0000000613000000, 0x0000000613000000|100%| O|  |TAMS 0x0000000613000000, 0x0000000612c00000| Updating 
|  60|0x0000000613000000, 0x0000000613400000, 0x0000000613400000|100%| O|  |TAMS 0x0000000613400000, 0x0000000613000000| Updating 
|  61|0x0000000613400000, 0x0000000613800000, 0x0000000613800000|100%| O|  |TAMS 0x0000000613800000, 0x0000000613400000| Updating 
|  62|0x0000000613800000, 0x0000000613c00000, 0x0000000613c00000|100%| O|  |TAMS 0x0000000613c00000, 0x0000000613800000| Updating 
|  63|0x0000000613c00000, 0x0000000614000000, 0x0000000614000000|100%| O|  |TAMS 0x0000000614000000, 0x0000000613c00000| Updating 
|  64|0x0000000614000000, 0x0000000614400000, 0x0000000614400000|100%| O|  |TAMS 0x0000000614400000, 0x0000000614000000| Updating 
|  65|0x0000000614400000, 0x0000000614800000, 0x0000000614800000|100%| O|  |TAMS 0x0000000614800000, 0x0000000614400000| Updating 
|  66|0x0000000614800000, 0x0000000614c00000, 0x0000000614c00000|100%| O|  |TAMS 0x0000000614c00000, 0x0000000614800000| Updating 
|  67|0x0000000614c00000, 0x0000000615000000, 0x0000000615000000|100%| O|  |TAMS 0x0000000615000000, 0x0000000614c00000| Updating 
|  68|0x0000000615000000, 0x0000000615400000, 0x0000000615400000|100%| O|  |TAMS 0x0000000615400000, 0x0000000615000000| Updating 
|  69|0x0000000615400000, 0x0000000615800000, 0x0000000615800000|100%| O|  |TAMS 0x0000000615800000, 0x0000000615400000| Updating 
|  70|0x0000000615800000, 0x0000000615c00000, 0x0000000615c00000|100%| O|  |TAMS 0x0000000615c00000, 0x0000000615800000| Updating 
|  71|0x0000000615c00000, 0x0000000616000000, 0x0000000616000000|100%| O|  |TAMS 0x0000000616000000, 0x0000000615c00000| Updating 
|  72|0x0000000616000000, 0x0000000616400000, 0x0000000616400000|100%| O|  |TAMS 0x0000000616400000, 0x0000000616000000| Updating 
|  73|0x0000000616400000, 0x0000000616800000, 0x0000000616800000|100%| O|  |TAMS 0x0000000616800000, 0x0000000616400000| Updating 
|  74|0x0000000616800000, 0x0000000616c00000, 0x0000000616c00000|100%| O|  |TAMS 0x0000000616c00000, 0x0000000616800000| Updating 
|  75|0x0000000616c00000, 0x0000000617000000, 0x0000000617000000|100%| O|  |TAMS 0x0000000617000000, 0x0000000616c00000| Updating 
|  76|0x0000000617000000, 0x0000000617400000, 0x0000000617400000|100%| O|  |TAMS 0x0000000617400000, 0x0000000617000000| Updating 
|  77|0x0000000617400000, 0x0000000617800000, 0x0000000617800000|100%| O|  |TAMS 0x0000000617800000, 0x0000000617400000| Updating 
|  78|0x0000000617800000, 0x0000000617c00000, 0x0000000617c00000|100%| O|  |TAMS 0x0000000617c00000, 0x0000000617800000| Updating 
|  79|0x0000000617c00000, 0x0000000618000000, 0x0000000618000000|100%| O|  |TAMS 0x0000000618000000, 0x0000000617c00000| Updating 
|  80|0x0000000618000000, 0x0000000618400000, 0x0000000618400000|100%| O|  |TAMS 0x0000000618400000, 0x0000000618000000| Updating 
|  81|0x0000000618400000, 0x0000000618800000, 0x0000000618800000|100%| O|  |TAMS 0x0000000618800000, 0x0000000618400000| Updating 
|  82|0x0000000618800000, 0x0000000618c00000, 0x0000000618c00000|100%| O|  |TAMS 0x0000000618c00000, 0x0000000618800000| Updating 
|  83|0x0000000618c00000, 0x0000000619000000, 0x0000000619000000|100%| O|  |TAMS 0x0000000619000000, 0x0000000618c00000| Updating 
|  84|0x0000000619000000, 0x0000000619400000, 0x0000000619400000|100%| O|  |TAMS 0x0000000619400000, 0x0000000619000000| Updating 
|  85|0x0000000619400000, 0x0000000619800000, 0x0000000619800000|100%| O|  |TAMS 0x0000000619800000, 0x0000000619400000| Updating 
|  86|0x0000000619800000, 0x0000000619c00000, 0x0000000619c00000|100%| O|  |TAMS 0x0000000619c00000, 0x0000000619800000| Updating 
|  87|0x0000000619c00000, 0x000000061a000000, 0x000000061a000000|100%| O|  |TAMS 0x000000061a000000, 0x0000000619c00000| Updating 
|  88|0x000000061a000000, 0x000000061a400000, 0x000000061a400000|100%| O|  |TAMS 0x000000061a400000, 0x000000061a000000| Updating 
|  89|0x000000061a400000, 0x000000061a800000, 0x000000061a800000|100%| O|  |TAMS 0x000000061a800000, 0x000000061a400000| Updating 
|  90|0x000000061a800000, 0x000000061ac00000, 0x000000061ac00000|100%| O|  |TAMS 0x000000061ac00000, 0x000000061a800000| Updating 
|  91|0x000000061ac00000, 0x000000061b000000, 0x000000061b000000|100%| O|  |TAMS 0x000000061b000000, 0x000000061ac00000| Updating 
|  92|0x000000061b000000, 0x000000061b400000, 0x000000061b400000|100%| O|  |TAMS 0x000000061b400000, 0x000000061b000000| Updating 
|  93|0x000000061b400000, 0x000000061b800000, 0x000000061b800000|100%| O|  |TAMS 0x000000061b800000, 0x000000061b400000| Updating 
|  94|0x000000061b800000, 0x000000061bc00000, 0x000000061bc00000|100%| O|  |TAMS 0x000000061bc00000, 0x000000061b800000| Updating 
|  95|0x000000061bc00000, 0x000000061c000000, 0x000000061c000000|100%| O|  |TAMS 0x000000061c000000, 0x000000061bc00000| Updating 
|  96|0x000000061c000000, 0x000000061c400000, 0x000000061c400000|100%| O|  |TAMS 0x000000061c400000, 0x000000061c000000| Updating 
|  97|0x000000061c400000, 0x000000061c800000, 0x000000061c800000|100%| O|  |TAMS 0x000000061c800000, 0x000000061c400000| Updating 
|  98|0x000000061c800000, 0x000000061cc00000, 0x000000061cc00000|100%| O|  |TAMS 0x000000061cc00000, 0x000000061c800000| Updating 
|  99|0x000000061cc00000, 0x000000061d000000, 0x000000061d000000|100%| O|  |TAMS 0x000000061d000000, 0x000000061cc00000| Updating 
| 100|0x000000061d000000, 0x000000061d400000, 0x000000061d400000|100%| O|  |TAMS 0x000000061d400000, 0x000000061d000000| Updating 
| 101|0x000000061d400000, 0x000000061d800000, 0x000000061d800000|100%| O|  |TAMS 0x000000061d800000, 0x000000061d400000| Updating 
| 102|0x000000061d800000, 0x000000061dc00000, 0x000000061dc00000|100%| O|  |TAMS 0x000000061dc00000, 0x000000061d800000| Updating 
| 103|0x000000061dc00000, 0x000000061e000000, 0x000000061e000000|100%| O|  |TAMS 0x000000061e000000, 0x000000061dc00000| Updating 
| 104|0x000000061e000000, 0x000000061e400000, 0x000000061e400000|100%| O|  |TAMS 0x000000061e400000, 0x000000061e000000| Updating 
| 105|0x000000061e400000, 0x000000061e800000, 0x000000061e800000|100%| O|  |TAMS 0x000000061e800000, 0x000000061e400000| Updating 
| 106|0x000000061e800000, 0x000000061ec00000, 0x000000061ec00000|100%| O|  |TAMS 0x000000061ec00000, 0x000000061e800000| Updating 
| 107|0x000000061ec00000, 0x000000061f000000, 0x000000061f000000|100%| O|  |TAMS 0x000000061f000000, 0x000000061ec00000| Updating 
| 108|0x000000061f000000, 0x000000061f400000, 0x000000061f400000|100%| O|  |TAMS 0x000000061f400000, 0x000000061f000000| Updating 
| 109|0x000000061f400000, 0x000000061f800000, 0x000000061f800000|100%| O|  |TAMS 0x000000061f800000, 0x000000061f400000| Updating 
| 110|0x000000061f800000, 0x000000061fc00000, 0x000000061fc00000|100%| O|  |TAMS 0x000000061fc00000, 0x000000061f800000| Updating 
| 111|0x000000061fc00000, 0x0000000620000000, 0x0000000620000000|100%| O|  |TAMS 0x0000000620000000, 0x000000061fc00000| Updating 
| 112|0x0000000620000000, 0x0000000620400000, 0x0000000620400000|100%| O|  |TAMS 0x0000000620400000, 0x0000000620000000| Updating 
| 113|0x0000000620400000, 0x0000000620800000, 0x0000000620800000|100%| O|  |TAMS 0x0000000620800000, 0x0000000620400000| Updating 
| 114|0x0000000620800000, 0x0000000620c00000, 0x0000000620c00000|100%| O|  |TAMS 0x0000000620c00000, 0x0000000620800000| Updating 
| 115|0x0000000620c00000, 0x0000000621000000, 0x0000000621000000|100%| O|  |TAMS 0x0000000621000000, 0x0000000620c00000| Updating 
| 116|0x0000000621000000, 0x0000000621400000, 0x0000000621400000|100%| O|  |TAMS 0x0000000621400000, 0x0000000621000000| Updating 
| 117|0x0000000621400000, 0x0000000621800000, 0x0000000621800000|100%| O|  |TAMS 0x0000000621800000, 0x0000000621400000| Updating 
| 118|0x0000000621800000, 0x0000000621c00000, 0x0000000621c00000|100%| O|  |TAMS 0x0000000621c00000, 0x0000000621800000| Updating 
| 119|0x0000000621c00000, 0x0000000622000000, 0x0000000622000000|100%| O|  |TAMS 0x0000000622000000, 0x0000000621c00000| Updating 
| 120|0x0000000622000000, 0x0000000622400000, 0x0000000622400000|100%| O|  |TAMS 0x0000000622400000, 0x0000000622000000| Updating 
| 121|0x0000000622400000, 0x0000000622800000, 0x0000000622800000|100%| O|  |TAMS 0x0000000622800000, 0x0000000622400000| Updating 
| 122|0x0000000622800000, 0x0000000622c00000, 0x0000000622c00000|100%| O|  |TAMS 0x0000000622c00000, 0x0000000622800000| Updating 
| 123|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%| O|  |TAMS 0x0000000623000000, 0x0000000622c00000| Updating 
| 124|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| O|  |TAMS 0x0000000623400000, 0x0000000623000000| Updating 
| 125|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| O|  |TAMS 0x0000000623800000, 0x0000000623400000| Updating 
| 126|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| O|  |TAMS 0x0000000623c00000, 0x0000000623800000| Updating 
| 127|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%| O|  |TAMS 0x0000000624000000, 0x0000000623c00000| Updating 
| 128|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%| O|  |TAMS 0x0000000624400000, 0x0000000624000000| Updating 
| 129|0x0000000624400000, 0x0000000624800000, 0x0000000624800000|100%| O|  |TAMS 0x0000000624800000, 0x0000000624400000| Updating 
| 130|0x0000000624800000, 0x0000000624c00000, 0x0000000624c00000|100%| O|  |TAMS 0x0000000624c00000, 0x0000000624800000| Updating 
| 131|0x0000000624c00000, 0x0000000625000000, 0x0000000625000000|100%| O|  |TAMS 0x0000000625000000, 0x0000000624c00000| Updating 
| 132|0x0000000625000000, 0x0000000625400000, 0x0000000625400000|100%| O|  |TAMS 0x0000000625400000, 0x0000000625000000| Updating 
| 133|0x0000000625400000, 0x0000000625800000, 0x0000000625800000|100%| O|  |TAMS 0x0000000625800000, 0x0000000625400000| Updating 
| 134|0x0000000625800000, 0x0000000625c00000, 0x0000000625c00000|100%| O|  |TAMS 0x0000000625c00000, 0x0000000625800000| Updating 
| 135|0x0000000625c00000, 0x0000000626000000, 0x0000000626000000|100%| O|  |TAMS 0x0000000626000000, 0x0000000625c00000| Updating 
| 136|0x0000000626000000, 0x0000000626400000, 0x0000000626400000|100%| O|  |TAMS 0x0000000626400000, 0x0000000626000000| Updating 
| 137|0x0000000626400000, 0x0000000626800000, 0x0000000626800000|100%| O|  |TAMS 0x0000000626800000, 0x0000000626400000| Updating 
| 138|0x0000000626800000, 0x0000000626c00000, 0x0000000626c00000|100%| O|  |TAMS 0x0000000626c00000, 0x0000000626800000| Updating 
| 139|0x0000000626c00000, 0x0000000627000000, 0x0000000627000000|100%| O|  |TAMS 0x0000000627000000, 0x0000000626c00000| Updating 
| 140|0x0000000627000000, 0x0000000627206c00, 0x0000000627400000| 50%| O|  |TAMS 0x00000006271c5c00, 0x0000000627000000| Updating 
| 141|0x0000000627400000, 0x0000000627400000, 0x0000000627800000|  0%| F|  |TAMS 0x0000000627400000, 0x0000000627400000| Untracked 
| 142|0x0000000627800000, 0x0000000627800000, 0x0000000627c00000|  0%| F|  |TAMS 0x0000000627800000, 0x0000000627800000| Untracked 
| 143|0x0000000627c00000, 0x0000000627c00000, 0x0000000628000000|  0%| F|  |TAMS 0x0000000627c00000, 0x0000000627c00000| Untracked 
| 144|0x0000000628000000, 0x0000000628000000, 0x0000000628400000|  0%| F|  |TAMS 0x0000000628000000, 0x0000000628000000| Untracked 
| 145|0x0000000628400000, 0x0000000628400000, 0x0000000628800000|  0%| F|  |TAMS 0x0000000628400000, 0x0000000628400000| Untracked 
| 146|0x0000000628800000, 0x0000000628800000, 0x0000000628c00000|  0%| F|  |TAMS 0x0000000628800000, 0x0000000628800000| Untracked 
| 147|0x0000000628c00000, 0x0000000628c00000, 0x0000000629000000|  0%| F|  |TAMS 0x0000000628c00000, 0x0000000628c00000| Untracked 
| 148|0x0000000629000000, 0x0000000629000000, 0x0000000629400000|  0%| F|  |TAMS 0x0000000629000000, 0x0000000629000000| Untracked 
| 149|0x0000000629400000, 0x00000006294e67f8, 0x0000000629800000| 22%| S|CS|TAMS 0x0000000629400000, 0x0000000629400000| Complete 
| 150|0x0000000629800000, 0x0000000629800000, 0x0000000629c00000|  0%| F|  |TAMS 0x0000000629800000, 0x0000000629800000| Untracked 
| 151|0x0000000629c00000, 0x0000000629c00000, 0x000000062a000000|  0%| F|  |TAMS 0x0000000629c00000, 0x0000000629c00000| Untracked 
| 152|0x000000062a000000, 0x000000062a000000, 0x000000062a400000|  0%| F|  |TAMS 0x000000062a000000, 0x000000062a000000| Untracked 
| 153|0x000000062a400000, 0x000000062a400000, 0x000000062a800000|  0%| F|  |TAMS 0x000000062a400000, 0x000000062a400000| Untracked 
| 154|0x000000062a800000, 0x000000062a800000, 0x000000062ac00000|  0%| F|  |TAMS 0x000000062a800000, 0x000000062a800000| Untracked 
| 155|0x000000062ac00000, 0x000000062afbbea0, 0x000000062b000000| 93%| E|  |TAMS 0x000000062ac00000, 0x000000062ac00000| Complete 

Card table byte_map: [0x00000193a4960000,0x00000193a5940000] _byte_map_base: 0x00000193a1940000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000019398649170, (CMBitMap*) 0x00000193986491b0
 Prev Bits: [0x00000193a6920000, 0x00000193ae820000)
 Next Bits: [0x00000193ae820000, 0x00000193b6720000)

Polling page: 0x00000193962a0000

Metaspace:

Usage:
  Non-class:     48.86 MB used.
      Class:      7.50 MB used.
       Both:     56.35 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      49.31 MB ( 77%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       7.88 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      57.19 MB (  5%) committed. 

Chunk freelists:
   Non-Class:  14.24 MB
       Class:  8.11 MB
        Both:  22.35 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 94.50 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 1676.
num_arena_deaths: 368.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 915.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 442.
num_chunks_taken_from_freelist: 4113.
num_chunk_merges: 159.
num_chunk_splits: 2478.
num_chunks_enlarged: 1456.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=25625Kb max_used=26509Kb free=23526Kb
 bounds [0x00000193a0100000, 0x00000193a1b70000, 0x00000193a3100000]
 total_blobs=13108 nmethods=12443 adapters=590
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1331.259 Thread 0x00000193b9af1240 nmethod 12430 0x00000193a18e9010 code [0x00000193a18e9280, 0x00000193a18e9cd8]
Event: 1331.259 Thread 0x00000193b9af1240 12434       1       sun.security.util.math.intpoly.IntegerPolynomial::encode (101 bytes)
Event: 1331.259 Thread 0x000001938032baf0 nmethod 12433 0x00000193a18e8910 code [0x00000193a18e8ae0, 0x00000193a18e8dd8]
Event: 1331.259 Thread 0x000001938032baf0 12436       1       sun.security.util.math.intpoly.P256OrderField::carryReduce0 (518 bytes)
Event: 1331.259 Thread 0x00000193b9af1240 nmethod 12434 0x00000193a18e8110 code [0x00000193a18e8300, 0x00000193a18e86a8]
Event: 1331.259 Thread 0x00000193b9af1240 12437       1       sun.security.ec.ECPrivateKeyImpl::<init> (26 bytes)
Event: 1331.259 Thread 0x00000193b9af1240 nmethod 12437 0x00000193a18e7c10 code [0x00000193a18e7dc0, 0x00000193a18e7ff8]
Event: 1331.259 Thread 0x00000193b9af1240 12438   !   1       sun.security.ec.ECPrivateKeyImpl::makeEncoding (92 bytes)
Event: 1331.259 Thread 0x000001938032baf0 nmethod 12436 0x00000193a18e7590 code [0x00000193a18e7720, 0x00000193a18e7b18]
Event: 1331.259 Thread 0x000001938032baf0 12440       1       sun.security.util.DerValue::wrap (19 bytes)
Event: 1331.259 Thread 0x000001938032baf0 nmethod 12440 0x00000193a18e7010 code [0x00000193a18e71c0, 0x00000193a18e7408]
Event: 1331.260 Thread 0x00000193b9af1240 nmethod 12438 0x00000193a18e5990 code [0x00000193a18e5c40, 0x00000193a18e66b8]
Event: 1331.271 Thread 0x00000193b9af1240 12441   !   1       sun.security.ec.ECKeyPairGenerator::generateKeyPair (71 bytes)
Event: 1331.271 Thread 0x000001938032baf0 12442       1       sun.security.ec.ECKeyPairGenerator::generateKeyPairImpl (218 bytes)
Event: 1331.272 Thread 0x00000193b9af1240 nmethod 12441 0x00000193a18e5010 code [0x00000193a18e5220, 0x00000193a18e5698]
Event: 1331.272 Thread 0x00000193b9af1240 12443   !   1       sun.security.ec.ECKeyPairGenerator::generatePrivateScalar (56 bytes)
Event: 1331.272 Thread 0x00000193b9af1240 nmethod 12443 0x00000193a18e4910 code [0x00000193a18e4ae0, 0x00000193a18e4dd8]
Event: 1331.272 Thread 0x000001938032baf0 nmethod 12442 0x00000193a18e2910 code [0x00000193a18e2be0, 0x00000193a18e3b58]
Event: 1331.681 Thread 0x000001938032baf0 12444   !   1       jdk.internal.reflect.GeneratedConstructorAccessor320::newInstance (49 bytes)
Event: 1331.681 Thread 0x000001938032baf0 nmethod 12444 0x00000193a18e2210 code [0x00000193a18e23e0, 0x00000193a18e26a8]

GC Heap History (20 events):
Event: 1330.984 GC heap before
{Heap before GC invocations=1955 (full 0):
 garbage-first heap   total 638976K, used 590054K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1330.985 GC heap after
{Heap after GC invocations=1956 (full 0):
 garbage-first heap   total 638976K, used 565500K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.121 GC heap before
{Heap before GC invocations=1956 (full 0):
 garbage-first heap   total 638976K, used 585980K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.122 GC heap after
{Heap after GC invocations=1957 (full 0):
 garbage-first heap   total 638976K, used 565666K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.260 GC heap before
{Heap before GC invocations=1957 (full 0):
 garbage-first heap   total 638976K, used 590242K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.261 GC heap after
{Heap after GC invocations=1958 (full 0):
 garbage-first heap   total 638976K, used 566235K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.351 GC heap before
{Heap before GC invocations=1958 (full 0):
 garbage-first heap   total 638976K, used 586715K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.352 GC heap after
{Heap after GC invocations=1959 (full 0):
 garbage-first heap   total 638976K, used 569936K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.443 GC heap before
{Heap before GC invocations=1959 (full 0):
 garbage-first heap   total 638976K, used 590416K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.444 GC heap after
{Heap after GC invocations=1960 (full 0):
 garbage-first heap   total 638976K, used 572843K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.527 GC heap before
{Heap before GC invocations=1960 (full 0):
 garbage-first heap   total 638976K, used 597419K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.528 GC heap after
{Heap after GC invocations=1961 (full 0):
 garbage-first heap   total 638976K, used 573256K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.616 GC heap before
{Heap before GC invocations=1961 (full 0):
 garbage-first heap   total 638976K, used 597832K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.617 GC heap after
{Heap after GC invocations=1962 (full 0):
 garbage-first heap   total 638976K, used 575765K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.687 GC heap before
{Heap before GC invocations=1962 (full 0):
 garbage-first heap   total 638976K, used 596245K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.688 GC heap after
{Heap after GC invocations=1963 (full 0):
 garbage-first heap   total 638976K, used 576372K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.787 GC heap before
{Heap before GC invocations=1963 (full 0):
 garbage-first heap   total 638976K, used 600948K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.788 GC heap after
{Heap after GC invocations=1964 (full 0):
 garbage-first heap   total 638976K, used 578144K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.870 GC heap before
{Heap before GC invocations=1964 (full 0):
 garbage-first heap   total 638976K, used 602720K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}
Event: 1331.871 GC heap after
{Heap after GC invocations=1965 (full 0):
 garbage-first heap   total 638976K, used 576436K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 57784K, committed 58560K, reserved 1114112K
  class space    used 7695K, committed 8064K, reserved 1048576K
}

Dll operation events (13 events):
Event: 0.003 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\java.dll
Event: 0.004 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\zip.dll
Event: 0.014 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jsvml.dll
Event: 0.063 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\instrument.dll
Event: 0.069 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jimage.dll
Event: 0.071 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\net.dll
Event: 0.072 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\nio.dll
Event: 0.074 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\zip.dll
Event: 0.122 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management.dll
Event: 0.126 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management_ext.dll
Event: 0.220 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\extnet.dll
Event: 1.136 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\sunmscapi.dll
Event: 1.802 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\verify.dll

Deoptimization events (20 events):
Event: 1331.882 Thread 0x0000019381876cb0 DEOPT PACKING pc=0x00000193a12b28b4 sp=0x0000002ba7efe430
Event: 1331.882 Thread 0x0000019381876cb0 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba7efda78 mode 1
Event: 1331.882 Thread 0x00000193814b5d20 DEOPT PACKING pc=0x00000193a0e7e45e sp=0x0000002ba4afdf20
Event: 1331.882 Thread 0x00000193814b5d20 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba4afd3b0 mode 1
Event: 1331.882 Thread 0x00000193814b5d20 DEOPT PACKING pc=0x00000193a05ac02c sp=0x0000002ba4afdf80
Event: 1331.882 Thread 0x00000193814b5d20 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba4afd450 mode 1
Event: 1331.882 Thread 0x00000193814b5d20 DEOPT PACKING pc=0x00000193a12b28b4 sp=0x0000002ba4afe000
Event: 1331.882 Thread 0x00000193814b5d20 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba4afd648 mode 1
Event: 1331.882 Thread 0x0000019381876cb0 DEOPT PACKING pc=0x00000193a0aba1af sp=0x0000002ba7efdcb0
Event: 1331.882 Thread 0x0000019381876cb0 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba7efd238 mode 1
Event: 1331.882 Thread 0x0000019381876cb0 DEOPT PACKING pc=0x00000193a11d9e54 sp=0x0000002ba7efde20
Event: 1331.882 Thread 0x0000019381876cb0 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba7efd360 mode 1
Event: 1331.882 Thread 0x0000019381876cb0 DEOPT PACKING pc=0x00000193a13704cc sp=0x0000002ba7efdf10
Event: 1331.882 Thread 0x0000019381876cb0 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba7efd408 mode 1
Event: 1331.883 Thread 0x00000193814b5810 DEOPT PACKING pc=0x00000193a0e7e45e sp=0x0000002ba73fddd0
Event: 1331.883 Thread 0x00000193814b5810 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba73fd260 mode 1
Event: 1331.883 Thread 0x00000193814b5810 DEOPT PACKING pc=0x00000193a05ac02c sp=0x0000002ba73fde30
Event: 1331.883 Thread 0x00000193814b5810 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba73fd300 mode 1
Event: 1331.883 Thread 0x00000193814b5810 DEOPT PACKING pc=0x00000193a12b28b4 sp=0x0000002ba73fdeb0
Event: 1331.883 Thread 0x00000193814b5810 DEOPT UNPACKING pc=0x00000193a0157143 sp=0x0000002ba73fd4f8 mode 1

Classes loaded (20 events):
Event: 111.385 Loading class cn/coder/zj/module/collector/collect/metrics/cpu/SangForCpuImpl
Event: 111.385 Loading class cn/coder/zj/module/collector/collect/metrics/cpu/SangForCpuImpl done
Event: 111.386 Loading class cn/coder/zj/module/collector/dal/platform/Platform
Event: 111.386 Loading class cn/coder/zj/module/collector/dal/platform/Platform done
Event: 111.386 Loading class cn/coder/zj/module/collector/collect/metrics/net/SangForNetImpl
Event: 111.386 Loading class cn/coder/zj/module/collector/collect/metrics/net/SangForNetImpl done
Event: 111.386 Loading class cn/coder/zj/module/collector/collect/metrics/mem/SangForMemImpl
Event: 111.386 Loading class cn/coder/zj/module/collector/collect/metrics/mem/SangForMemImpl done
Event: 111.386 Loading class cn/coder/zj/module/collector/dal/platform/Platform
Event: 111.386 Loading class cn/coder/zj/module/collector/dal/platform/Platform done
Event: 111.386 Loading class cn/coder/zj/module/collector/dal/platform/Platform
Event: 111.386 Loading class cn/coder/zj/module/collector/dal/platform/Platform done
Event: 111.386 Loading class cn/coder/zj/module/collector/collect/metrics/disk/SangForDiskImpl
Event: 111.386 Loading class cn/coder/zj/module/collector/collect/metrics/disk/SangForDiskImpl done
Event: 111.386 Loading class cn/coder/zj/module/collector/dal/platform/Platform
Event: 111.386 Loading class cn/coder/zj/module/collector/dal/platform/Platform done
Event: 166.808 Loading class sun/security/ssl/Alert$AlertMessage
Event: 166.808 Loading class sun/security/ssl/Alert$AlertMessage done
Event: 605.290 Loading class java/util/concurrent/ConcurrentLinkedQueue$Itr
Event: 605.290 Loading class java/util/concurrent/ConcurrentLinkedQueue$Itr done

Classes unloaded (20 events):
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb340c00 'java/lang/invoke/LambdaForm$MH+0x00000193bb340c00'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb340800 'java/lang/invoke/LambdaForm$MH+0x00000193bb340800'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb0d9c00 'java/lang/invoke/LambdaForm$MH+0x00000193bb0d9c00'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb0d9800 'java/lang/invoke/LambdaForm$MH+0x00000193bb0d9800'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb0d9400 'java/lang/invoke/LambdaForm$MH+0x00000193bb0d9400'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb0d9000 'java/lang/invoke/LambdaForm$MH+0x00000193bb0d9000'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb0d8c00 'java/lang/invoke/LambdaForm$MH+0x00000193bb0d8c00'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb0d8800 'java/lang/invoke/LambdaForm$MH+0x00000193bb0d8800'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb0d8400 'java/lang/invoke/LambdaForm$MH+0x00000193bb0d8400'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb0d8000 'java/lang/invoke/LambdaForm$MH+0x00000193bb0d8000'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb006c00 'java/lang/invoke/LambdaForm$MH+0x00000193bb006c00'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb006800 'java/lang/invoke/LambdaForm$MH+0x00000193bb006800'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb006400 'java/lang/invoke/LambdaForm$MH+0x00000193bb006400'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb006000 'java/lang/invoke/LambdaForm$MH+0x00000193bb006000'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb005c00 'java/lang/invoke/LambdaForm$MH+0x00000193bb005c00'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb005800 'java/lang/invoke/LambdaForm$MH+0x00000193bb005800'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb004800 'java/lang/invoke/LambdaForm$MH+0x00000193bb004800'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb004400 'java/lang/invoke/LambdaForm$MH+0x00000193bb004400'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb003c00 'java/lang/invoke/LambdaForm$MH+0x00000193bb003c00'
Event: 1331.886 Thread 0x00000193b9ae3010 Unloading class 0x00000193bb001800 'java/lang/invoke/LambdaForm$MH+0x00000193bb001800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 867.322 Thread 0x0000019381875870 Exception <a 'java/lang/NullPointerException'{0x0000000621205d28}> (0x0000000621205d28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 867.330 Thread 0x0000019381875870 Exception <a 'java/lang/NullPointerException'{0x00000006212110d0}> (0x00000006212110d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 924.605 Thread 0x00000193814b2060 Exception <a 'java/lang/NullPointerException'{0x0000000622a5a5f8}> (0x0000000622a5a5f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 924.613 Thread 0x00000193814b2060 Exception <a 'java/lang/NullPointerException'{0x0000000622a65758}> (0x0000000622a65758) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 947.026 Thread 0x00000193814ae3a0 Implicit null exception at 0x00000193a18347e1 to 0x00000193a1837639
Event: 947.026 Thread 0x00000193814ae3a0 Exception <a 'java/lang/NullPointerException'{0x00000006245bcba0}> (0x00000006245bcba0) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 966.359 Thread 0x00000193814b0c20 Implicit null exception at 0x00000193a18347e1 to 0x00000193a1837639
Event: 966.359 Thread 0x00000193814b0c20 Exception <a 'java/lang/NullPointerException'{0x0000000621e7d6c0}> (0x0000000621e7d6c0) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 631]
Event: 978.330 Thread 0x000001938187ae80 Exception <a 'java/lang/NullPointerException'{0x0000000621dc8da8}> (0x0000000621dc8da8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 978.338 Thread 0x000001938187ae80 Exception <a 'java/lang/NullPointerException'{0x0000000621fbb8e0}> (0x0000000621fbb8e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1038.836 Thread 0x0000019383542570 Exception <a 'java/lang/NullPointerException'{0x00000006256b2cf0}> (0x00000006256b2cf0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1038.844 Thread 0x0000019383542570 Exception <a 'java/lang/NullPointerException'{0x000000062ad06670}> (0x000000062ad06670) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1098.491 Thread 0x00000193814b2060 Exception <a 'java/lang/NullPointerException'{0x000000062a6af6b0}> (0x000000062a6af6b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1098.500 Thread 0x00000193814b2060 Exception <a 'java/lang/NullPointerException'{0x000000062a6b68f0}> (0x000000062a6b68f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1157.222 Thread 0x00000193814b5d20 Exception <a 'java/lang/NullPointerException'{0x0000000629e56e08}> (0x0000000629e56e08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1157.231 Thread 0x00000193814b5d20 Exception <a 'java/lang/NullPointerException'{0x00000006299c72a0}> (0x00000006299c72a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1217.656 Thread 0x0000019383542570 Exception <a 'java/lang/NullPointerException'{0x000000062a6e7938}> (0x000000062a6e7938) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1217.663 Thread 0x0000019383542570 Exception <a 'java/lang/NullPointerException'{0x000000062a7a6c00}> (0x000000062a7a6c00) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1276.568 Thread 0x000001938187ae80 Exception <a 'java/lang/NullPointerException'{0x000000062ae96700}> (0x000000062ae96700) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1276.586 Thread 0x000001938187ae80 Exception <a 'java/lang/NullPointerException'{0x000000062a8e09f8}> (0x000000062a8e09f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]

VM Operations (20 events):
Event: 1330.985 Executing VM operation: G1CollectForAllocation done
Event: 1331.121 Executing VM operation: G1CollectForAllocation
Event: 1331.122 Executing VM operation: G1CollectForAllocation done
Event: 1331.260 Executing VM operation: G1CollectForAllocation
Event: 1331.261 Executing VM operation: G1CollectForAllocation done
Event: 1331.350 Executing VM operation: G1CollectForAllocation
Event: 1331.352 Executing VM operation: G1CollectForAllocation done
Event: 1331.443 Executing VM operation: G1CollectForAllocation
Event: 1331.444 Executing VM operation: G1CollectForAllocation done
Event: 1331.527 Executing VM operation: G1CollectForAllocation
Event: 1331.528 Executing VM operation: G1CollectForAllocation done
Event: 1331.616 Executing VM operation: G1CollectForAllocation
Event: 1331.617 Executing VM operation: G1CollectForAllocation done
Event: 1331.687 Executing VM operation: G1CollectForAllocation
Event: 1331.688 Executing VM operation: G1CollectForAllocation done
Event: 1331.787 Executing VM operation: G1CollectForAllocation
Event: 1331.788 Executing VM operation: G1CollectForAllocation done
Event: 1331.870 Executing VM operation: G1CollectForAllocation
Event: 1331.871 Executing VM operation: G1CollectForAllocation done
Event: 1331.884 Executing VM operation: G1PauseRemark

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 1326.421 Thread 0x00000193814b1b50 Thread exited: 0x00000193814b1b50
Event: 1328.159 Thread 0x00000193814b0c20 Thread added: 0x00000193814b3ec0
Event: 1328.159 Thread 0x00000193814b0c20 Thread exited: 0x00000193814b0c20
Event: 1329.169 Thread 0x00000193814ae8b0 Thread added: 0x00000193814b0c20
Event: 1329.170 Thread 0x00000193814b0c20 Thread exited: 0x00000193814b0c20
Event: 1329.979 Thread 0x000001938187ae80 Thread added: 0x00000193814b0c20
Event: 1329.980 Thread 0x00000193814b0c20 Thread exited: 0x00000193814b0c20
Event: 1330.183 Thread 0x00000193814af2d0 Thread exited: 0x00000193814af2d0
Event: 1330.184 Thread 0x0000019381875870 Thread added: 0x00000193814b0c20
Event: 1330.206 Thread 0x00000193814b3ec0 Thread added: 0x00000193814b2570
Event: 1330.206 Thread 0x00000193814b2570 Thread exited: 0x00000193814b2570
Event: 1331.121 Thread 0x0000019383542570 Thread added: 0x00000193814b1b50
Event: 1331.122 Thread 0x00000193814b1b50 Thread exited: 0x00000193814b1b50
Event: 1331.234 Thread 0x0000019383542570 Thread added: 0x00000193814b1b50
Event: 1331.235 Thread 0x00000193814b1b50 Thread exited: 0x00000193814b1b50
Event: 1331.259 Thread 0x00000193b9af1240 Thread added: 0x000001938032baf0
Event: 1331.260 Thread 0x0000019383541130 Thread added: 0x00000193814b1b50
Event: 1331.260 Thread 0x00000193814b1b50 Thread exited: 0x00000193814b1b50
Event: 1331.273 Thread 0x00000193814b5d20 Thread added: 0x00000193814b1b50
Event: 1331.273 Thread 0x00000193814b1b50 Thread exited: 0x00000193814b1b50


Dynamic libraries:
0x00007ff752c60000 - 0x00007ff752c6e000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\java.exe
0x00007fffd4cd0000 - 0x00007fffd4ec8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007fffd36d0000 - 0x00007fffd3792000 	C:\Windows\System32\KERNEL32.DLL
0x00007fffd2580000 - 0x00007fffd2876000 	C:\Windows\System32\KERNELBASE.dll
0x00007fffd2ad0000 - 0x00007fffd2bd0000 	C:\Windows\System32\ucrtbase.dll
0x00007fffbf4f0000 - 0x00007fffbf507000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jli.dll
0x00007fffb9fe0000 - 0x00007fffb9ffb000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\VCRUNTIME140.dll
0x00007fffd37a0000 - 0x00007fffd393d000 	C:\Windows\System32\USER32.dll
0x00007fffb6e40000 - 0x00007fffb70da000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007fffd2aa0000 - 0x00007fffd2ac2000 	C:\Windows\System32\win32u.dll
0x00007fffd2d00000 - 0x00007fffd2d2b000 	C:\Windows\System32\GDI32.dll
0x00007fffd44d0000 - 0x00007fffd456e000 	C:\Windows\System32\msvcrt.dll
0x00007fffd2bd0000 - 0x00007fffd2cea000 	C:\Windows\System32\gdi32full.dll
0x00007fffd2980000 - 0x00007fffd2a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007fffd4ad0000 - 0x00007fffd4aff000 	C:\Windows\System32\IMM32.DLL
0x00007fffc9fa0000 - 0x00007fffc9fac000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\vcruntime140_1.dll
0x00007fff9de80000 - 0x00007fff9df0d000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\msvcp140.dll
0x00007fff43710000 - 0x00007fff4437c000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\server\jvm.dll
0x00007fffd4b00000 - 0x00007fffd4baf000 	C:\Windows\System32\ADVAPI32.dll
0x00007fffd2d30000 - 0x00007fffd2dcf000 	C:\Windows\System32\sechost.dll
0x00007fffd4130000 - 0x00007fffd4253000 	C:\Windows\System32\RPCRT4.dll
0x00007fffd2550000 - 0x00007fffd2577000 	C:\Windows\System32\bcrypt.dll
0x00007fffd3540000 - 0x00007fffd35ab000 	C:\Windows\System32\WS2_32.dll
0x00007fffc5350000 - 0x00007fffc5377000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fffd18f0000 - 0x00007fffd193b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007fffcbd00000 - 0x00007fffcbd0a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007fffd1760000 - 0x00007fffd1772000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007fffd0200000 - 0x00007fffd0212000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007fffbdf90000 - 0x00007fffbdf9a000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jimage.dll
0x00007fffcf740000 - 0x00007fffcf924000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007fffb23f0000 - 0x00007fffb2424000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fffd24c0000 - 0x00007fffd2542000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007fffca2b0000 - 0x00007fffca2eb000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jdwp.dll
0x00007fffce460000 - 0x00007fffce46e000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\instrument.dll
0x00007fffa7590000 - 0x00007fffa75b5000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\java.dll
0x00007fff81f10000 - 0x00007fff81f28000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\zip.dll
0x00007fff4b440000 - 0x00007fff4b517000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jsvml.dll
0x00007fffd2dd0000 - 0x00007fffd353f000 	C:\Windows\System32\SHELL32.dll
0x00007fffd0400000 - 0x00007fffd0ba3000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007fffd3cc0000 - 0x00007fffd4015000 	C:\Windows\System32\combase.dll
0x00007fffd1d90000 - 0x00007fffd1dbb000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007fffd4260000 - 0x00007fffd432d000 	C:\Windows\System32\OLEAUT32.dll
0x00007fffd4020000 - 0x00007fffd40cd000 	C:\Windows\System32\SHCORE.dll
0x00007fffd40d0000 - 0x00007fffd4125000 	C:\Windows\System32\shlwapi.dll
0x00007fffd2290000 - 0x00007fffd22b5000 	C:\Windows\SYSTEM32\profapi.dll
0x00007fffcd920000 - 0x00007fffcd92c000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\dt_socket.dll
0x00007fffd1780000 - 0x00007fffd17bb000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007fffd1af0000 - 0x00007fffd1b5c000 	C:\Windows\system32\mswsock.dll
0x00007fff9de60000 - 0x00007fff9de79000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\net.dll
0x00007fffcd210000 - 0x00007fffcd31a000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007fff9de00000 - 0x00007fff9de16000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\nio.dll
0x00007fffb80e0000 - 0x00007fffb8113000 	C:\Program Files (x86)\Sangfor\SSL\ClientComponent\SangforNspX64.dll
0x00007fffd43a0000 - 0x00007fffd44cb000 	C:\Windows\System32\ole32.dll
0x00007fffd3c00000 - 0x00007fffd3c08000 	C:\Windows\System32\PSAPI.DLL
0x00007fffd17c0000 - 0x00007fffd188a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007fffd2cf0000 - 0x00007fffd2cf8000 	C:\Windows\System32\NSI.dll
0x000000006e940000 - 0x000000006e966000 	C:\Program Files\Bonjour\mdnsNSP.dll
0x00007fffba930000 - 0x00007fffba93a000 	C:\Windows\System32\rasadhlp.dll
0x00007fffbaab0000 - 0x00007fffbab30000 	C:\Windows\System32\fwpuclnt.dll
0x00007fffb9fd0000 - 0x00007fffb9fd9000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management.dll
0x00007fffaeb30000 - 0x00007fffaeb3b000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management_ext.dll
0x00007fffd1ce0000 - 0x00007fffd1cf8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007fffd13a0000 - 0x00007fffd13d8000 	C:\Windows\system32\rsaenh.dll
0x00007fffd2250000 - 0x00007fffd227e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007fffd1d00000 - 0x00007fffd1d0c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007fffcd660000 - 0x00007fffcd677000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007fffcdfe0000 - 0x00007fffcdffd000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007fffbeb20000 - 0x00007fffbeb37000 	C:\Windows\system32\napinsp.dll
0x00007fffbeb00000 - 0x00007fffbeb1b000 	C:\Windows\system32\pnrpnsp.dll
0x00007fffaea00000 - 0x00007fffaea15000 	C:\Windows\system32\wshbth.dll
0x00007fffce1e0000 - 0x00007fffce1fd000 	C:\Windows\system32\NLAapi.dll
0x00007fffbe0c0000 - 0x00007fffbe0d2000 	C:\Windows\System32\winrnr.dll
0x00007fffa1f40000 - 0x00007fffa1f49000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\extnet.dll
0x00007fffa7430000 - 0x00007fffa743e000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\sunmscapi.dll
0x00007fffd2360000 - 0x00007fffd24bd000 	C:\Windows\System32\CRYPT32.dll
0x00007fffd1e00000 - 0x00007fffd1e27000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007fffd1dc0000 - 0x00007fffd1dfb000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007fffabb80000 - 0x00007fffabb87000 	C:\Windows\system32\wshunix.dll
0x00007fffcce50000 - 0x00007fffcce60000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\corretto-17.0.13\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;C:\Users\<USER>\.jdks\corretto-17.0.13\bin\server;C:\Program Files (x86)\Sangfor\SSL\ClientComponent;C:\Program Files\Bonjour

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:63749,suspend=y,server=n -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.2\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture42380.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dfile.encoding=UTF-8 
java_command: cn.coder.zj.module.collector.Collector
java_class_path (initial): D:\git-zj\zj-module-client-collector\target\classes;D:\maven\repository\org\springframework\boot\spring-boot-starter-web\3.0.13\spring-boot-starter-web-3.0.13.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter\3.0.13\spring-boot-starter-3.0.13.jar;D:\maven\repository\org\springframework\boot\spring-boot\3.0.13\spring-boot-3.0.13.jar;D:\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.0.13\spring-boot-autoconfigure-3.0.13.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-logging\3.0.13\spring-boot-starter-logging-3.0.13.jar;D:\maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.19.0\log4j-to-slf4j-2.19.0.jar;D:\maven\repository\org\apache\logging\log4j\log4j-api\2.19.0\log4j-api-2.19.0.jar;D:\maven\repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven\repository\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-json\3.0.13\spring-boot-starter-json-3.0.13.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.14.3\jackson-databind-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.14.3\jackson-annotations-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-core\2.14.3\jackson-core-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.14.3\jackson-datatype-jdk8-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.14.3\jackson-datatype-jsr310-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.14.3\jackson-module-parameter-names-2.14.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-tomcat\3.0.13\spring-boot-starter-tomcat-3.0.13.jar;D:\maven\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 4                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 15                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8522825728                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8522825728                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\jdk17\jdk-17.0.13_windows-x64_bin\jdk-17.0.13
PATH=c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;D:\git\Git\cmd;D:\nojs\nvm;D:\nojs\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\idea\IntelliJ IDEA 2024.2.3\bin;D:\js\nvm;D:\js\nodejs;D:\js\nvm\v16.16.0;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\nojs\nvm;D:\nojs\nodejs;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\jdk17\jdk-17.0.13_windows-x64_bin\jdk-17.0.13\bin;C:\Users\<USER>\Downloads\fvm-3.2.1-windows-x64\fvm\fvm;C:\ProgramData\chocolatey\bin;D:\protoc\protoc-3.19.4-win64\bin;C:\Program Files\CursorModifier;C:\Program Files\Tesseract-OCR\;D:\maven1\apache-maven-3.9.9\bin;d:\Trae\bin;D:\pythonhome\Scripts\;D:\pythonhome\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\idea\IntelliJ IDEA 2024.2.3\bin;D:\js\nvm;D:\js\nodejs;D:\js\nvm\v16.16.0;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\nojs\nvm;D:\nojs\nodejs;D:\vscode\Microsoft VS Code\bin;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
OS uptime: 13 days 2:47 hours

CPU: total 20 (initial active 20) (10 cores per cpu, 2 threads per core) family 6 model 183 stepping 1 microcode 0x123, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb
Processor Information for all 20 processors :
  Max Mhz: 3500, Current Mhz: 3500, Mhz Limit: 3500

Memory: 4k page, system-wide physical 32509M (765M free)
TotalPageFile size 70909M (AvailPageFile size 181M)
current process WorkingSet (physical memory assigned to process): 834M, peak: 834M
current process commit charge ("private bytes"): 888M, peak: 1220M

vm_info: OpenJDK 64-Bit Server VM (17.0.13+11-LTS) for windows-amd64 JRE (17.0.13+11-LTS), built on Oct 10 2024 19:51:30 by "Administrator" with MS VC++ 16.10 / 16.11 (VS2019)

END.
