#
# A fatal error has been detected by the Java Runtime Environment:
#
#  Internal Error (method.cpp:1850), pid=19580, tid=29444
#  fatal error: no original bytecode found in cn.coder.zj.module.collector.collect.metrics.cpu.IsTackCpuImpl.lambda$vmUUid$2(Lcn/coder/zj/module/collector/dal/platform/Platform;Ljava/lang/String;)V at bci 198
#
# JRE version: OpenJDK Runtime Environment Corretto-**********.1 (17.0.13+11) (build 17.0.13+11-LTS)
# Java VM: OpenJDK 64-Bit Server VM Corretto-**********.1 (17.0.13+11-LTS, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/corretto/corretto-17/issues/
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:52826,suspend=y,server=n -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.2\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture49613.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dfile.encoding=UTF-8 cn.coder.zj.module.collector.Collector

Host: Intel(R) Core(TM) i5-14600K, 20 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
Time: Thu Apr 10 18:25:11 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5678) elapsed time: 1660.766675 seconds (0d 0h 27m 40s)

---------------  T H R E A D  ---------------

Current thread (0x000002d6eb9b1f90):  JavaThread "async-18" [_thread_in_vm, id=29444, stack(0x000000fd28d00000,0x000000fd28e00000)]

Stack: [0x000000fd28d00000,0x000000fd28e00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x680c19]
V  [jvm.dll+0x83888a]
V  [jvm.dll+0x83a34e]
V  [jvm.dll+0x247ecb]
V  [jvm.dll+0x633b0f]
V  [jvm.dll+0x385174]
C  0x000002d68a247a99

The last pc belongs to breakpoint (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  cn.coder.zj.module.collector.collect.metrics.cpu.IsTackCpuImpl.lambda$vmUUid$2(Lcn/coder/zj/module/collector/dal/platform/Platform;Ljava/lang/String;)V+198
j  cn.coder.zj.module.collector.collect.metrics.cpu.IsTackCpuImpl$$Lambda$1031+0x000002d6a5698658.accept(Ljava/lang/Object;)V+8
j  java.util.ArrayList$ArrayListSpliterator.forEachRemaining(Ljava/util/function/Consumer;)V+95 java.base@17.0.13
j  java.util.stream.ReferencePipeline$Head.forEach(Ljava/util/function/Consumer;)V+12 java.base@17.0.13
j  cn.coder.zj.module.collector.collect.metrics.cpu.IsTackCpuImpl.vmUUid(Ljava/util/List;Lcn/coder/zj/module/collector/dal/platform/Platform;)Ljava/util/List;+20
j  cn.coder.zj.module.collector.collect.metrics.cpu.IsTackCpuImpl.lambda$collectData$0(Ljava/lang/Object;Lcn/iocoder/zj/framework/common/message/ClusterMsg$Message$Builder;Lcn/coder/zj/module/collector/remoting/send/SendMessageService;)V+57
j  cn.coder.zj.module.collector.collect.metrics.cpu.IsTackCpuImpl$$Lambda$1030+0x000002d6a568f388.run()V+16
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@17.0.13
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@17.0.13
j  java.lang.Thread.run()V+11 java.base@17.0.13
v  ~StubRoutines::call_stub
breakpoint  202 breakpoint  [0x000002d68a247a20, 0x000002d68a247d40]  800 bytes
[MachCode]
  0x000002d68a247a20: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x000002d68a247a40: 4424 0800 | 0000 00eb | 0150 488b | 55e8 e805 | 0000 00e9 | 6001 0000 | 4d8b c548 | 8d44 2408 
  0x000002d68a247a60: 4c89 6dc0 | 498b cfc5 | f877 4989 | afa8 0200 | 0049 8987 | 9802 0000 | 4883 ec20 | f7c4 0f00 
  0x000002d68a247a80: 0000 0f84 | 1a00 0000 | 4883 ec08 | 49ba 4051 | 590a ff7f | 0000 41ff | d248 83c4 | 08e9 0d00 
  0x000002d68a247aa0: 0000 49ba | 4051 590a | ff7f 0000 | 41ff d248 | 83c4 2049 | c787 9802 | 0000 0000 | 0000 49c7 
  0x000002d68a247ac0: 87a8 0200 | 0000 0000 | 0049 c787 | a002 0000 | 0000 0000 | c5f8 7741 | 8b8f 0c04 | 0000 f7c1 
  0x000002d68a247ae0: 0100 0000 | 0f84 4900 | 0000 f7c1 | 0200 0000 | 0f85 3d00 | 0000 4883 | ec20 f7c4 | 0f00 0000 
  0x000002d68a247b00: 0f84 1a00 | 0000 4883 | ec08 49ba | 5012 590a | ff7f 0000 | 41ff d248 | 83c4 08e9 | 0d00 0000 
  0x000002d68a247b20: 49ba 5012 | 590a ff7f | 0000 41ff | d248 83c4 | 20ff e049 | 8b8f 9004 | 0000 4885 | c90f 8459 
  0x000002d68a247b40: 0000 008b | 898c 0000 | 0083 f901 | 0f85 4a00 | 0000 498b | 8f90 0400 | 008b 8990 | 0000 0048 
  0x000002d68a247b60: 83ec 20f7 | c40f 0000 | 000f 841a | 0000 0048 | 83ec 0849 | ba40 1259 | 0aff 7f00 | 0041 ffd2 
  0x000002d68a247b80: 4883 c408 | e90d 0000 | 0049 ba40 | 1259 0aff | 7f00 0041 | ffd2 4883 | c420 ffe0 | 4981 7f08 
  0x000002d68a247ba0: 0000 0000 | 0f84 0500 | 0000 e951 | 93fd ff4c | 8b6d c04c | 8b75 c8c3 | 488b d848 | 8b55 e8e8 
  0x000002d68a247bc0: 0500 0000 | e960 0100 | 004d 8bc5 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af a802 
  0x000002d68a247be0: 0000 4989 | 8798 0200 | 0048 83ec | 20f7 c40f | 0000 000f | 841a 0000 | 0048 83ec | 0849 baf0 
  0x000002d68a247c00: 3659 0aff | 7f00 0041 | ffd2 4883 | c408 e90d | 0000 0049 | baf0 3659 | 0aff 7f00 | 0041 ffd2 
  0x000002d68a247c20: 4883 c420 | 49c7 8798 | 0200 0000 | 0000 0049 | c787 a802 | 0000 0000 | 0000 49c7 | 87a0 0200 
  0x000002d68a247c40: 0000 0000 | 00c5 f877 | 418b 8f0c | 0400 00f7 | c101 0000 | 000f 8449 | 0000 00f7 | c102 0000 
  0x000002d68a247c60: 000f 853d | 0000 0048 | 83ec 20f7 | c40f 0000 | 000f 841a | 0000 0048 | 83ec 0849 | ba50 1259 
  0x000002d68a247c80: 0aff 7f00 | 0041 ffd2 | 4883 c408 | e90d 0000 | 0049 ba50 | 1259 0aff | 7f00 0041 | ffd2 4883 
  0x000002d68a247ca0: c420 ffe0 | 498b 8f90 | 0400 0048 | 85c9 0f84 | 5900 0000 | 8b89 8c00 | 0000 83f9 | 010f 854a 
  0x000002d68a247cc0: 0000 0049 | 8b8f 9004 | 0000 8b89 | 9000 0000 | 4883 ec20 | f7c4 0f00 | 0000 0f84 | 1a00 0000 
  0x000002d68a247ce0: 4883 ec08 | 49ba 4012 | 590a ff7f | 0000 41ff | d248 83c4 | 08e9 0d00 | 0000 49ba | 4012 590a 
  0x000002d68a247d00: ff7f 0000 | 41ff d248 | 83c4 20ff | e049 817f | 0800 0000 | 000f 8405 | 0000 00e9 | e091 fdff 
  0x000002d68a247d20: 4c8b 6dc0 | 4c8b 75c8 | c349 bad0 | 9edc 0aff | 7f00 0041 | ff24 da90 | 0000 0000 | 0000 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002d6e963f7d0, length=71, elements={
0x000002d6a0b31250, 0x000002d6a0b31fe0, 0x000002d6a3c0bfe0, 0x000002d6a3c0e8c0,
0x000002d6a3c10300, 0x000002d6a3c10fe0, 0x000002d6a3c12180, 0x000002d6a3c167a0,
0x000002d6a0b1bdf0, 0x000002d6a3e25700, 0x000002d6a3e3b460, 0x000002d6a3e28180,
0x000002d6a3e47a40, 0x000002d6e9779860, 0x000002d6ea44f830, 0x000002d6ea44fd40,
0x000002d6ea450250, 0x000002d6ea450760, 0x000002d6ea450c70, 0x000002d6ea44f320,
0x000002d6ea451180, 0x000002d6ea44ee10, 0x000002d6ea451690, 0x000002d6ea4520b0,
0x000002d6ea4525c0, 0x000002d6ea451ba0, 0x000002d6eb62e5a0, 0x000002d6eb632770,
0x000002d6eb632260, 0x000002d6eb630910, 0x000002d6eb62eab0, 0x000002d6eb633190,
0x000002d6eb635500, 0x000002d6eb6340c0, 0x000002d6eb631d50, 0x000002d6eb634ff0,
0x000002d6eb62efc0, 0x000002d6eb62f4d0, 0x000002d6eb62f9e0, 0x000002d6eb633bb0,
0x000002d6eb62e090, 0x000002d6eb6345d0, 0x000002d6eb62fef0, 0x000002d6eb634ae0,
0x000002d6eb630e20, 0x000002d6eb635a10, 0x000002d6eb630400, 0x000002d6eb631840,
0x000002d6eb9b1a80, 0x000002d6eb9af200, 0x000002d6eb9b1f90, 0x000002d6eb9aecf0,
0x000002d6eb9b24a0, 0x000002d6eb9af710, 0x000002d6eb9b0b50, 0x000002d6eb9abf60,
0x000002d6eb9b2ec0, 0x000002d6eb9b29b0, 0x000002d6eb9b1060, 0x000002d6eb9ad8b0,
0x000002d6eb9addc0, 0x000002d6ebbfbc10, 0x000002d6ebaf4800, 0x000002d6e9641690,
0x000002d6ebb19fb0, 0x000002d6ebbe5bb0, 0x000002d6ebbf0bd0, 0x000002d6803713b0,
0x000002d6eb9f4c40, 0x000002d6eb9f5160, 0x000002d6ea09f610
}

Java Threads: ( => current thread )
  0x000002d6a0b31250 JavaThread "Reference Handler" daemon [_thread_blocked, id=17932, stack(0x000000fd24200000,0x000000fd24300000)]
  0x000002d6a0b31fe0 JavaThread "Finalizer" daemon [_thread_blocked, id=22216, stack(0x000000fd24300000,0x000000fd24400000)]
  0x000002d6a3c0bfe0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=27896, stack(0x000000fd24400000,0x000000fd24500000)]
  0x000002d6a3c0e8c0 JavaThread "Attach Listener" daemon [_thread_blocked, id=27908, stack(0x000000fd24500000,0x000000fd24600000)]
  0x000002d6a3c10300 JavaThread "Service Thread" daemon [_thread_blocked, id=20004, stack(0x000000fd24600000,0x000000fd24700000)]
  0x000002d6a3c10fe0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=21232, stack(0x000000fd24700000,0x000000fd24800000)]
  0x000002d6a3c12180 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=30240, stack(0x000000fd24800000,0x000000fd24900000)]
  0x000002d6a3c167a0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=19248, stack(0x000000fd24900000,0x000000fd24a00000)]
  0x000002d6a0b1bdf0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=25048, stack(0x000000fd24a00000,0x000000fd24b00000)]
  0x000002d6a3e25700 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=29952, stack(0x000000fd24e00000,0x000000fd24f00000)]
  0x000002d6a3e3b460 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=23780, stack(0x000000fd24f00000,0x000000fd25000000)]
  0x000002d6a3e28180 JavaThread "JDWP Command Reader" daemon [_thread_blocked, id=8468, stack(0x000000fd25000000,0x000000fd25100000)]
  0x000002d6a3e47a40 JavaThread "Notification Thread" daemon [_thread_blocked, id=29224, stack(0x000000fd25100000,0x000000fd25200000)]
  0x000002d6e9779860 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=30380, stack(0x000000fd25d00000,0x000000fd25e00000)]
  0x000002d6ea44f830 JavaThread "AsyncAppender-Worker-async_stdout" daemon [_thread_blocked, id=28012, stack(0x000000fd26300000,0x000000fd26400000)]
  0x000002d6ea44fd40 JavaThread "AsyncAppender-Worker-async_file" daemon [_thread_blocked, id=28520, stack(0x000000fd26400000,0x000000fd26500000)]
  0x000002d6ea450250 JavaThread "AsyncAppender-Worker-async_business" daemon [_thread_blocked, id=26244, stack(0x000000fd26500000,0x000000fd26600000)]
  0x000002d6ea450760 JavaThread "Catalina-utility-1" [_thread_blocked, id=28436, stack(0x000000fd26600000,0x000000fd26700000)]
  0x000002d6ea450c70 JavaThread "Catalina-utility-2" [_thread_blocked, id=5644, stack(0x000000fd26700000,0x000000fd26800000)]
  0x000002d6ea44f320 JavaThread "container-0" [_thread_blocked, id=21216, stack(0x000000fd26b00000,0x000000fd26c00000)]
  0x000002d6ea451180 JavaThread "RMI TCP Connection(idle)" daemon [_thread_blocked, id=22192, stack(0x000000fd26c00000,0x000000fd26d00000)]
  0x000002d6ea44ee10 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=27928, stack(0x000000fd26d00000,0x000000fd26e00000)]
  0x000002d6ea451690 JavaThread "http-nio-1160-exec-1" daemon [_thread_blocked, id=30140, stack(0x000000fd26e00000,0x000000fd26f00000)]
  0x000002d6ea4520b0 JavaThread "http-nio-1160-Poller" daemon [_thread_in_native, id=26808, stack(0x000000fd26f00000,0x000000fd27000000)]
  0x000002d6ea4525c0 JavaThread "http-nio-1160-Acceptor" daemon [_thread_in_native, id=26428, stack(0x000000fd27000000,0x000000fd27100000)]
  0x000002d6ea451ba0 JavaThread "async-1" [_thread_blocked, id=19424, stack(0x000000fd27100000,0x000000fd27200000)]
  0x000002d6eb62e5a0 JavaThread "DestroyJavaVM" [_thread_blocked, id=28748, stack(0x000000fd23b00000,0x000000fd23c00000)]
  0x000002d6eb632770 JavaThread "nioEventLoopGroup-2-1" [_thread_blocked, id=27704, stack(0x000000fd27200000,0x000000fd27300000)]
  0x000002d6eb632260 JavaThread "async-2" [_thread_blocked, id=22544, stack(0x000000fd27400000,0x000000fd27500000)]
  0x000002d6eb630910 JavaThread "async-4" [_thread_blocked, id=11080, stack(0x000000fd27600000,0x000000fd27700000)]
  0x000002d6eb62eab0 JavaThread "async-5" [_thread_blocked, id=25648, stack(0x000000fd27700000,0x000000fd27800000)]
  0x000002d6eb633190 JavaThread "async-6" [_thread_blocked, id=29732, stack(0x000000fd27800000,0x000000fd27900000)]
  0x000002d6eb635500 JavaThread "async-7" [_thread_blocked, id=9516, stack(0x000000fd27900000,0x000000fd27a00000)]
  0x000002d6eb6340c0 JavaThread "async-8" [_thread_blocked, id=24844, stack(0x000000fd27a00000,0x000000fd27b00000)]
  0x000002d6eb631d50 JavaThread "async-9" [_thread_blocked, id=30288, stack(0x000000fd27b00000,0x000000fd27c00000)]
  0x000002d6eb634ff0 JavaThread "async-10" [_thread_blocked, id=29288, stack(0x000000fd27c00000,0x000000fd27d00000)]
  0x000002d6eb62efc0 JavaThread "async-12" [_thread_blocked, id=29084, stack(0x000000fd27d00000,0x000000fd27e00000)]
  0x000002d6eb62f4d0 JavaThread "async-13" [_thread_blocked, id=9300, stack(0x000000fd27f00000,0x000000fd28000000)]
  0x000002d6eb62f9e0 JavaThread "async-14" [_thread_blocked, id=27848, stack(0x000000fd28000000,0x000000fd28100000)]
  0x000002d6eb633bb0 JavaThread "async-15" [_thread_blocked, id=5768, stack(0x000000fd28100000,0x000000fd28200000)]
  0x000002d6eb62e090 JavaThread "YQJob-1" [_thread_blocked, id=6384, stack(0x000000fd28200000,0x000000fd28300000)]
  0x000002d6eb6345d0 JavaThread "YQJob-2" [_thread_blocked, id=16032, stack(0x000000fd28300000,0x000000fd28400000)]
  0x000002d6eb62fef0 JavaThread "YQJob-3" [_thread_blocked, id=30172, stack(0x000000fd28400000,0x000000fd28500000)]
  0x000002d6eb634ae0 JavaThread "YQJob-4" [_thread_blocked, id=22844, stack(0x000000fd28500000,0x000000fd28600000)]
  0x000002d6eb630e20 JavaThread "YQJob-5" [_thread_blocked, id=27028, stack(0x000000fd28600000,0x000000fd28700000)]
  0x000002d6eb635a10 JavaThread "OkHttp Dispatcher" [_thread_blocked, id=30124, stack(0x000000fd28700000,0x000000fd28800000)]
  0x000002d6eb630400 JavaThread "OkHttp ConnectionPool" daemon [_thread_blocked, id=30632, stack(0x000000fd28800000,0x000000fd28900000)]
  0x000002d6eb631840 JavaThread "OkHttp ConnectionPool" daemon [_thread_blocked, id=28620, stack(0x000000fd28900000,0x000000fd28a00000)]
  0x000002d6eb9b1a80 JavaThread "Okio Watchdog" daemon [_thread_blocked, id=29280, stack(0x000000fd28a00000,0x000000fd28b00000)]
  0x000002d6eb9af200 JavaThread "async-17" [_thread_blocked, id=16292, stack(0x000000fd28c00000,0x000000fd28d00000)]
=>0x000002d6eb9b1f90 JavaThread "async-18" [_thread_in_vm, id=29444, stack(0x000000fd28d00000,0x000000fd28e00000)]
  0x000002d6eb9aecf0 JavaThread "async-19" [_thread_blocked, id=28332, stack(0x000000fd28e00000,0x000000fd28f00000)]
  0x000002d6eb9b24a0 JavaThread "async-20" [_thread_blocked, id=30200, stack(0x000000fd28f00000,0x000000fd29000000)]
  0x000002d6eb9af710 JavaThread "YQJob-6" [_thread_blocked, id=29048, stack(0x000000fd29000000,0x000000fd29100000)]
  0x000002d6eb9b0b50 JavaThread "YQJob-7" [_thread_blocked, id=16420, stack(0x000000fd29300000,0x000000fd29400000)]
  0x000002d6eb9abf60 JavaThread "YQJob-8" [_thread_blocked, id=11796, stack(0x000000fd29200000,0x000000fd29300000)]
  0x000002d6eb9b2ec0 JavaThread "YQJob-9" [_thread_blocked, id=9100, stack(0x000000fd29400000,0x000000fd29500000)]
  0x000002d6eb9b29b0 JavaThread "YQJob-10" [_thread_blocked, id=10076, stack(0x000000fd29500000,0x000000fd29600000)]
  0x000002d6eb9b1060 JavaThread "async-21" [_thread_blocked, id=10124, stack(0x000000fd24c00000,0x000000fd24d00000)]
  0x000002d6eb9ad8b0 JavaThread "async-22" [_thread_blocked, id=30484, stack(0x000000fd24d00000,0x000000fd24e00000)]
  0x000002d6eb9addc0 JavaThread "async-23" [_thread_blocked, id=28212, stack(0x000000fd25f00000,0x000000fd26000000)]
  0x000002d6ebbfbc10 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=30224, stack(0x000000fd23800000,0x000000fd23900000)]
  0x000002d6ebaf4800 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=18380, stack(0x000000fd23900000,0x000000fd23a00000)]
  0x000002d6e9641690 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=28872, stack(0x000000fd23a00000,0x000000fd23b00000)]
  0x000002d6ebb19fb0 JavaThread "C1 CompilerThread4" daemon [_thread_blocked, id=23292, stack(0x000000fd24b00000,0x000000fd24c00000)]
  0x000002d6ebbe5bb0 JavaThread "C1 CompilerThread5" daemon [_thread_blocked, id=29936, stack(0x000000fd27300000,0x000000fd27400000)]
  0x000002d6ebbf0bd0 JavaThread "C1 CompilerThread6" daemon [_thread_blocked, id=27216, stack(0x000000fd27500000,0x000000fd27600000)]
  0x000002d6803713b0 JavaThread "C1 CompilerThread7" daemon [_thread_blocked, id=28364, stack(0x000000fd27e00000,0x000000fd27f00000)]
  0x000002d6eb9f4c40 JavaThread "C1 CompilerThread8" daemon [_thread_blocked, id=1660, stack(0x000000fd29100000,0x000000fd29200000)]
  0x000002d6eb9f5160 JavaThread "C1 CompilerThread9" daemon [_thread_blocked, id=30012, stack(0x000000fd29600000,0x000000fd29700000)]
  0x000002d6ea09f610 JavaThread "C1 CompilerThread10" daemon [_thread_blocked, id=30512, stack(0x000000fd28b00000,0x000000fd28c00000)]

Other Threads:
  0x000002d6a3c03010 VMThread "VM Thread" [stack: 0x000000fd24100000,0x000000fd24200000] [id=30392] _threads_hazard_ptr=0x000002d6e963f7d0
  0x000002d6826faea0 WatcherThread [stack: 0x000000fd25e00000,0x000000fd25f00000] [id=10924]
  0x000002d6a0990900 GCTaskThread "GC Thread#0" [stack: 0x000000fd23c00000,0x000000fd23d00000] [id=28984]
  0x000002d6e96beb20 GCTaskThread "GC Thread#1" [stack: 0x000000fd25200000,0x000000fd25300000] [id=19596]
  0x000002d6e96dec60 GCTaskThread "GC Thread#2" [stack: 0x000000fd25300000,0x000000fd25400000] [id=27508]
  0x000002d6e96dfb40 GCTaskThread "GC Thread#3" [stack: 0x000000fd25400000,0x000000fd25500000] [id=28732]
  0x000002d6e96dfe00 GCTaskThread "GC Thread#4" [stack: 0x000000fd25500000,0x000000fd25600000] [id=23004]
  0x000002d6e96e02d0 GCTaskThread "GC Thread#5" [stack: 0x000000fd25600000,0x000000fd25700000] [id=17472]
  0x000002d6e96e0590 GCTaskThread "GC Thread#6" [stack: 0x000000fd25700000,0x000000fd25800000] [id=10952]
  0x000002d6e96e1060 GCTaskThread "GC Thread#7" [stack: 0x000000fd25800000,0x000000fd25900000] [id=28308]
  0x000002d6e96e1730 GCTaskThread "GC Thread#8" [stack: 0x000000fd25900000,0x000000fd25a00000] [id=23752]
  0x000002d6e96e1e00 GCTaskThread "GC Thread#9" [stack: 0x000000fd25a00000,0x000000fd25b00000] [id=27856]
  0x000002d6e96e28d0 GCTaskThread "GC Thread#10" [stack: 0x000000fd25b00000,0x000000fd25c00000] [id=29536]
  0x000002d6e96e3ba0 GCTaskThread "GC Thread#11" [stack: 0x000000fd25c00000,0x000000fd25d00000] [id=30056]
  0x000002d6ea2a6840 GCTaskThread "GC Thread#12" [stack: 0x000000fd26000000,0x000000fd26100000] [id=27464]
  0x000002d6ea2a6b00 GCTaskThread "GC Thread#13" [stack: 0x000000fd26100000,0x000000fd26200000] [id=20536]
  0x000002d6ea2a6dc0 GCTaskThread "GC Thread#14" [stack: 0x000000fd26200000,0x000000fd26300000] [id=4200]
  0x000002d6827786c0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000fd23d00000,0x000000fd23e00000] [id=24032]
  0x000002d682778ef0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000fd23e00000,0x000000fd23f00000] [id=30184]
  0x000002d6ea397320 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000fd26800000,0x000000fd26900000] [id=27924]
  0x000002d6ea398920 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000fd26900000,0x000000fd26a00000] [id=4960]
  0x000002d6ea3975e0 ConcurrentGCThread "G1 Conc#3" [stack: 0x000000fd26a00000,0x000000fd26b00000] [id=30088]
  0x000002d6a0a648d0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000fd23f00000,0x000000fd24000000] [id=28724]
  0x000002d6a0a65110 ConcurrentGCThread "G1 Service" [stack: 0x000000fd24000000,0x000000fd24100000] [id=27820]

Threads with active compile tasks:
C1 CompilerThread0  1660785 5980       1       com.google.gson.internal.bind.ReflectiveTypeAdapterFactory::createBoundField (135 bytes)
C1 CompilerThread1  1660785 5978       1       com.google.gson.internal.bind.ReflectiveTypeAdapterFactory::getFieldNames (90 bytes)
C1 CompilerThread2  1660785 5982       1       com.google.gson.internal.bind.ReflectiveTypeAdapterFactory$BoundField::<init> (34 bytes)
C1 CompilerThread3  1660785 5938       1       com.sun.crypto.provider.GCTR::doFinal (155 bytes)
C1 CompilerThread4  1660785 5950   !   1       okio.InputStreamSource::read (218 bytes)
C1 CompilerThread5  1660785 5937       1       com.sun.crypto.provider.GaloisCounterMode$GCMEngine::overlapDetection (289 bytes)
C1 CompilerThread6  1660785 5946       1       okhttp3.HttpUrl$Builder::canonicalizeHost (101 bytes)
C1 CompilerThread7  1660785 5981       1       com.google.gson.internal.bind.ReflectiveTypeAdapterFactory$1::<init> (64 bytes)
C1 CompilerThread8  1660785 5984       1       java.lang.reflect.Field::getGenericType (20 bytes)
C1 CompilerThread9  1660785 5983       1       com.google.gson.internal.Primitives::isPrimitive (23 bytes)
C1 CompilerThread10  1660785 5977   !   1       com.google.gson.internal.reflect.ReflectionHelper::makeAccessible (49 bytes)

VM state: synchronizing (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000002d6826f6d50] Threads_lock - owner thread: 0x000002d6a3c03010

Heap address: 0x0000000604000000, size: 8128 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000002d6a4000000-0x000002d6a4bb0000-0x000002d6a4bb0000), size 12255232, SharedBaseAddress: 0x000002d6a4000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002d6a5000000-0x000002d6e5000000, reserved size: 1073741824
Narrow klass base: 0x000002d6a4000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 20 total, 20 available
 Memory: 32509M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8128M
 Pre-touch: Disabled
 Parallel Workers: 15
 Concurrent Workers: 4
 Concurrent Refinement Workers: 15
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 139264K, used 91051K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 3 survivors (12288K)
 Metaspace       used 49705K, committed 50240K, reserved 1114112K
  class space    used 6555K, committed 6784K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604000000, 0x0000000604400000, 0x0000000604400000|100%| O|  |TAMS 0x0000000604400000, 0x0000000604000000| Untracked 
|   1|0x0000000604400000, 0x0000000604800000, 0x0000000604800000|100%| O|  |TAMS 0x0000000604800000, 0x0000000604400000| Untracked 
|   2|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%| O|  |TAMS 0x0000000604c00000, 0x0000000604800000| Untracked 
|   3|0x0000000604c00000, 0x0000000604e57200, 0x0000000605000000| 58%| O|  |TAMS 0x0000000604e57200, 0x0000000604c00000| Untracked 
|   4|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605000000| Untracked 
|   5|0x0000000605400000, 0x00000006055d5e00, 0x0000000605800000| 45%| O|  |TAMS 0x00000006055d5e00, 0x0000000605400000| Untracked 
|   6|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%|HS|  |TAMS 0x0000000605800000, 0x0000000605800000| Complete 
|   7|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%|HC|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Complete 
|   8|0x0000000606000000, 0x0000000606400000, 0x0000000606400000|100%|HC|  |TAMS 0x0000000606000000, 0x0000000606000000| Complete 
|   9|0x0000000606400000, 0x0000000606800000, 0x0000000606800000|100%|HC|  |TAMS 0x0000000606400000, 0x0000000606400000| Complete 
|  10|0x0000000606800000, 0x0000000606c00000, 0x0000000606c00000|100%|HC|  |TAMS 0x0000000606800000, 0x0000000606800000| Complete 
|  11|0x0000000606c00000, 0x0000000606e80400, 0x0000000607000000| 62%| O|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  12|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  13|0x0000000607400000, 0x000000060763d890, 0x0000000607800000| 56%| S|CS|TAMS 0x0000000607400000, 0x0000000607400000| Complete 
|  14|0x0000000607800000, 0x0000000607c00000, 0x0000000607c00000|100%| S|CS|TAMS 0x0000000607800000, 0x0000000607800000| Complete 
|  15|0x0000000607c00000, 0x0000000608000000, 0x0000000608000000|100%| S|CS|TAMS 0x0000000607c00000, 0x0000000607c00000| Complete 
|  16|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  17|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  18|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  19|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  20|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  21|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  22|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  23|0x0000000609c00000, 0x0000000609d4e6c8, 0x000000060a000000| 32%| E|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Complete 
|  24|0x000000060a000000, 0x000000060a400000, 0x000000060a400000|100%| E|CS|TAMS 0x000000060a000000, 0x000000060a000000| Complete 
|  25|0x000000060a400000, 0x000000060a800000, 0x000000060a800000|100%| E|CS|TAMS 0x000000060a400000, 0x000000060a400000| Complete 
|  26|0x000000060a800000, 0x000000060ac00000, 0x000000060ac00000|100%| E|CS|TAMS 0x000000060a800000, 0x000000060a800000| Complete 
|  27|0x000000060ac00000, 0x000000060b000000, 0x000000060b000000|100%| E|CS|TAMS 0x000000060ac00000, 0x000000060ac00000| Complete 
|  28|0x000000060b000000, 0x000000060b400000, 0x000000060b400000|100%| E|CS|TAMS 0x000000060b000000, 0x000000060b000000| Complete 
|  29|0x000000060b400000, 0x000000060b800000, 0x000000060b800000|100%| E|  |TAMS 0x000000060b400000, 0x000000060b400000| Complete 
|  66|0x0000000614800000, 0x0000000614c00000, 0x0000000614c00000|100%| E|CS|TAMS 0x0000000614800000, 0x0000000614800000| Complete 
|  67|0x0000000614c00000, 0x0000000615000000, 0x0000000615000000|100%| E|CS|TAMS 0x0000000614c00000, 0x0000000614c00000| Complete 
|  68|0x0000000615000000, 0x0000000615400000, 0x0000000615400000|100%| E|CS|TAMS 0x0000000615000000, 0x0000000615000000| Complete 
| 126|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000, 0x0000000623800000| Complete 

Card table byte_map: [0x000002d68ea80000,0x000002d68fa60000] _byte_map_base: 0x000002d68ba60000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002d682766f80, (CMBitMap*) 0x000002d682766fc0
 Prev Bits: [0x000002d690a40000, 0x000002d698940000)
 Next Bits: [0x000002d698940000, 0x000002d6a0840000)

Polling page: 0x000002d6806e0000

Metaspace:

Usage:
  Non-class:     42.14 MB used.
      Class:      6.40 MB used.
       Both:     48.54 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      42.44 MB ( 66%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       6.62 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      49.06 MB (  5%) committed. 

Chunk freelists:
   Non-Class:  5.03 MB
       Class:  9.39 MB
        Both:  14.42 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 59.31 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 760.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 785.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 2382.
num_chunk_merges: 6.
num_chunk_splits: 1676.
num_chunks_enlarged: 1253.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=17102Kb max_used=17102Kb free=32049Kb
 bounds [0x000002d68a220000, 0x000002d68b330000, 0x000002d68d220000]
 total_blobs=6611 nmethods=5952 adapters=575
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1660.765 Thread 0x000002d6803713b0 nmethod 5951 0x000002d68a3b6590 code [0x000002d68a3b6720, 0x000002d68a3b67d8]
Event: 1660.765 Thread 0x000002d6803713b0 5935       1       com.sun.crypto.provider.GHASH::doFinal (10 bytes)
Event: 1660.765 Thread 0x000002d6eb9f4c40 nmethod 5953 0x000002d68a3b6290 code [0x000002d68a3b6420, 0x000002d68a3b64f8]
Event: 1660.765 Thread 0x000002d6a3c12180 5986       1       java.net.InetAddress$InetAddressHolder::getAddress (5 bytes)
Event: 1660.765 Thread 0x000002d6eb9f4c40 5984       1       java.lang.reflect.Field::getGenericType (20 bytes)
Event: 1660.765 Thread 0x000002d6ea09f610 5977   !   1       com.google.gson.internal.reflect.ReflectionHelper::makeAccessible (49 bytes)
Event: 1660.765 Thread 0x000002d6ebaf4800 nmethod 5949 0x000002d68a3b5690 code [0x000002d68a3b5860, 0x000002d68a3b5b28]
Event: 1660.765 Thread 0x000002d6ebbfbc10 nmethod 5936 0x000002d68a3b5c90 code [0x000002d68a3b5e40, 0x000002d68a3b6058]
Event: 1660.765 Thread 0x000002d6a3c12180 nmethod 5986 0x000002d68a3b4890 code [0x000002d68a3b4a20, 0x000002d68a3b4af8]
Event: 1660.765 Thread 0x000002d6803713b0 nmethod 5935 0x000002d68a3b4b90 code [0x000002d68a3b4d20, 0x000002d68a3b4e48]
Event: 1660.765 Thread 0x000002d6eb9f5160 nmethod 5939 0x000002d68a3b4f10 code [0x000002d68a3b50c0, 0x000002d68a3b5378]
Event: 1660.765 Thread 0x000002d6ebbfbc10 5978       1       com.google.gson.internal.bind.ReflectiveTypeAdapterFactory::getFieldNames (90 bytes)
Event: 1660.765 Thread 0x000002d6ebaf4800 5979       1       com.google.gson.FieldNamingPolicy$1::translateName (5 bytes)
Event: 1660.765 Thread 0x000002d6a3c12180 5980       1       com.google.gson.internal.bind.ReflectiveTypeAdapterFactory::createBoundField (135 bytes)
Event: 1660.765 Thread 0x000002d6803713b0 5981       1       com.google.gson.internal.bind.ReflectiveTypeAdapterFactory$1::<init> (64 bytes)
Event: 1660.765 Thread 0x000002d6ebaf4800 nmethod 5979 0x000002d68a3b4590 code [0x000002d68a3b4720, 0x000002d68a3b47f8]
Event: 1660.765 Thread 0x000002d6ebaf4800 5982       1       com.google.gson.internal.bind.ReflectiveTypeAdapterFactory$BoundField::<init> (34 bytes)
Event: 1660.765 Thread 0x000002d6eb9f5160 5983       1       com.google.gson.internal.Primitives::isPrimitive (23 bytes)
Event: 1660.765 Thread 0x000002d6ebb19fb0 nmethod 5950 0x000002d68a3b3590 code [0x000002d68a3b3840, 0x000002d68a3b4018]
Event: 1660.765 Thread 0x000002d6ebbf0bd0 nmethod 5946 0x000002d68a3b2810 code [0x000002d68a3b2a80, 0x000002d68a3b3048]

GC Heap History (16 events):
Event: 0.144 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 20480K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 2404K, committed 2560K, reserved 1114112K
  class space    used 283K, committed 384K, reserved 1048576K
}
Event: 0.146 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 14757K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 2404K, committed 2560K, reserved 1114112K
  class space    used 283K, committed 384K, reserved 1048576K
}
Event: 0.305 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 35237K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 1 survivors (4096K)
 Metaspace       used 8116K, committed 8256K, reserved 1114112K
  class space    used 1084K, committed 1152K, reserved 1048576K
}
Event: 0.307 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 16966K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 8116K, committed 8256K, reserved 1114112K
  class space    used 1084K, committed 1152K, reserved 1048576K
}
Event: 0.524 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 62022K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 1 survivors (4096K)
 Metaspace       used 15798K, committed 16064K, reserved 1114112K
  class space    used 2255K, committed 2368K, reserved 1048576K
}
Event: 0.526 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 520192K, used 19444K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 15798K, committed 16064K, reserved 1114112K
  class space    used 2255K, committed 2368K, reserved 1048576K
}
Event: 0.618 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total 520192K, used 56308K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 2 survivors (8192K)
 Metaspace       used 21306K, committed 21504K, reserved 1114112K
  class space    used 2916K, committed 3008K, reserved 1048576K
}
Event: 0.620 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total 520192K, used 21439K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 21306K, committed 21504K, reserved 1114112K
  class space    used 2916K, committed 3008K, reserved 1048576K
}
Event: 0.886 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 98304K, used 70591K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 15 young (61440K), 2 survivors (8192K)
 Metaspace       used 27551K, committed 27968K, reserved 1114112K
  class space    used 3836K, committed 4032K, reserved 1048576K
}
Event: 0.889 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 98304K, used 24318K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 27551K, committed 27968K, reserved 1114112K
  class space    used 3836K, committed 4032K, reserved 1048576K
}
Event: 1.093 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 98304K, used 57086K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 3 survivors (12288K)
 Metaspace       used 32903K, committed 33280K, reserved 1114112K
  class space    used 4567K, committed 4736K, reserved 1048576K
}
Event: 1.097 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 311296K, used 27641K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 32903K, committed 33280K, reserved 1114112K
  class space    used 4567K, committed 4736K, reserved 1048576K
}
Event: 1.251 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 311296K, used 52217K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 9 young (36864K), 2 survivors (8192K)
 Metaspace       used 35831K, committed 36288K, reserved 1114112K
  class space    used 4996K, committed 5248K, reserved 1048576K
}
Event: 1.253 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 311296K, used 29874K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 35831K, committed 36288K, reserved 1114112K
  class space    used 4996K, committed 5248K, reserved 1048576K
}
Event: 2.107 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total 139264K, used 103602K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 18 young (73728K), 3 survivors (12288K)
 Metaspace       used 48350K, committed 48896K, reserved 1114112K
  class space    used 6418K, committed 6656K, reserved 1048576K
}
Event: 2.112 GC heap after
{Heap after GC invocations=10 (full 0):
 garbage-first heap   total 139264K, used 54187K [0x0000000604000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 48350K, committed 48896K, reserved 1114112K
  class space    used 6418K, committed 6656K, reserved 1048576K
}

Dll operation events (12 events):
Event: 0.003 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\java.dll
Event: 0.003 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\zip.dll
Event: 0.012 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jsvml.dll
Event: 0.053 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\instrument.dll
Event: 0.057 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jimage.dll
Event: 0.061 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\net.dll
Event: 0.062 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\nio.dll
Event: 0.065 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\zip.dll
Event: 0.110 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management.dll
Event: 0.114 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management_ext.dll
Event: 0.190 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\extnet.dll
Event: 1.000 Loaded shared library C:\Users\<USER>\.jdks\corretto-17.0.13\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 1660.760 Thread 0x000002d6eb62eab0 DEOPT PACKING pc=0x000002d68a781aec sp=0x000000fd277fdf80
Event: 1660.760 Thread 0x000002d6eb62eab0 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd277fd428 mode 1
Event: 1660.760 Thread 0x000002d6eb62eab0 DEOPT PACKING pc=0x000002d68b05be94 sp=0x000000fd277fe150
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd277fd790 mode 1
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT PACKING pc=0x000002d68b05be94 sp=0x000000fd277fe410
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd277fda28 mode 1
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT PACKING pc=0x000002d68afd1084 sp=0x000000fd277fe600
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd277fdac8 mode 1
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT PACKING pc=0x000002d68b05be94 sp=0x000000fd277fe730
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd277fdd48 mode 1
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT PACKING pc=0x000002d68afd1084 sp=0x000000fd277fe920
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd277fdde8 mode 1
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT PACKING pc=0x000002d68b05be94 sp=0x000000fd277fea50
Event: 1660.761 Thread 0x000002d6eb62eab0 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd277fe090 mode 1
Event: 1660.761 Thread 0x000002d6eb632770 DEOPT PACKING pc=0x000002d68b0165f4 sp=0x000000fd272fed40
Event: 1660.761 Thread 0x000002d6eb632770 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd272fe1e0 mode 1
Event: 1660.761 Thread 0x000002d6eb632770 DEOPT PACKING pc=0x000002d68a997e0c sp=0x000000fd272feda0
Event: 1660.761 Thread 0x000002d6eb632770 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd272fe288 mode 1
Event: 1660.761 Thread 0x000002d6eb632770 DEOPT PACKING pc=0x000002d68a9922cc sp=0x000000fd272fee90
Event: 1660.761 Thread 0x000002d6eb632770 DEOPT UNPACKING pc=0x000002d68a277143 sp=0x000000fd272fe390 mode 1

Classes loaded (20 events):
Event: 32.417 Loading class cn/coder/zj/module/collector/dal/platform/Platform
Event: 32.417 Loading class cn/coder/zj/module/collector/dal/platform/Platform done
Event: 32.417 Loading class cn/coder/zj/module/collector/dal/platform/Platform
Event: 32.417 Loading class cn/coder/zj/module/collector/dal/platform/Platform done
Event: 32.417 Loading class org/apache/catalina/webresources/CachedResource
Event: 32.417 Loading class org/apache/catalina/webresources/CachedResource done
Event: 32.418 Loading class cn/coder/zj/module/collector/dal/manager/MetricData
Event: 32.418 Loading class cn/coder/zj/module/collector/dal/manager/MetricData done
Event: 32.419 Loading class org/apache/tomcat/PeriodicEventListener
Event: 32.419 Loading class org/apache/tomcat/PeriodicEventListener done
Event: 42.085 Loading class java/lang/Throwable$WrappedPrintStream
Event: 42.085 Loading class java/lang/Throwable$WrappedPrintStream done
Event: 59.259 Loading class cn/coder/zj/module/collector/constants/istack/IStackApiConstant
Event: 59.259 Loading class cn/coder/zj/module/collector/constants/istack/IStackApiConstant done
Event: 1660.760 Loading class sun/security/ssl/Alert
Event: 1660.760 Loading class sun/security/ssl/Alert done
Event: 1660.761 Loading class sun/net/ConnectionResetException
Event: 1660.761 Loading class sun/net/ConnectionResetException done
Event: 1660.761 Loading class org/apache/commons/lang3/exception/ExceptionUtils
Event: 1660.761 Loading class org/apache/commons/lang3/exception/ExceptionUtils done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 7.399 Thread 0x000002d6eb9b1f90 Exception <a 'java/lang/NoSuchMethodError'{0x000000060a5440f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060a5440f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 7.399 Thread 0x000002d6eb9b1f90 Exception <a 'java/lang/NoSuchMethodError'{0x000000060a548aa0}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, long, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060a548aa0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 7.399 Thread 0x000002d6eb9b1f90 Exception <a 'java/lang/NoSuchMethodError'{0x000000060a5575c0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060a5575c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 25.444 Thread 0x000002d6eb635500 Exception <a 'java/net/SocketException'{0x000000060a95c0f0}: Socket is not connected: shutdown> (0x000000060a95c0f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.444 Thread 0x000002d6eb634ff0 Exception <a 'java/net/SocketException'{0x000000060a8f73c0}: Socket is not connected: shutdown> (0x000000060a8f73c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb62efc0 Exception <a 'java/net/ConnectException'{0x000000060a69ea50}: Connection timed out: getsockopt> (0x000000060a69ea50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb62efc0 Exception <a 'java/net/SocketException'{0x000000060a69f030}: Socket is not connected: shutdown> (0x000000060a69f030) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb632260 Exception <a 'java/net/SocketException'{0x000000060a951038}: Socket is not connected: shutdown> (0x000000060a951038) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb6336a0 Exception <a 'java/net/ConnectException'{0x000000060a6acba8}: Connection timed out: getsockopt> (0x000000060a6acba8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb630910 Exception <a 'java/net/SocketException'{0x000000060a93fe80}: Socket is not connected: shutdown> (0x000000060a93fe80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb6336a0 Exception <a 'java/net/SocketException'{0x000000060a6ad188}: Socket is not connected: shutdown> (0x000000060a6ad188) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb62f4d0 Exception <a 'java/net/ConnectException'{0x000000060a6bc3f8}: Connection timed out: getsockopt> (0x000000060a6bc3f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb633bb0 Exception <a 'java/net/ConnectException'{0x000000060a6cd7e8}: Connection timed out: getsockopt> (0x000000060a6cd7e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb62f4d0 Exception <a 'java/net/SocketException'{0x000000060a6bc9d8}: Socket is not connected: shutdown> (0x000000060a6bc9d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 25.445 Thread 0x000002d6eb633bb0 Exception <a 'java/net/SocketException'{0x000000060a6cddc8}: Socket is not connected: shutdown> (0x000000060a6cddc8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 42.084 Thread 0x000002d6eb9ace90 Exception <a 'java/lang/NullPointerException'{0x000000060a1d03a8}> (0x000000060a1d03a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 42.101 Thread 0x000002d6eb6336a0 Exception <a 'java/lang/NullPointerException'{0x000000060a32a178}> (0x000000060a32a178) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 42.132 Thread 0x000002d6eb632c80 Exception <a 'java/lang/NullPointerException'{0x000000060a260950}> (0x000000060a260950) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1350]
Event: 1660.761 Thread 0x000002d6eb632770 Exception <a 'java/io/IOException'{0x000000060a7cc7a8}> (0x000000060a7cc7a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 1660.761 Thread 0x000002d6ea451180 Exception <a 'java/net/SocketException'{0x000000060a5777f0}: Connection reset by peer: shutdown> (0x000000060a5777f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]

VM Operations (20 events):
Event: 1657.336 Executing VM operation: get/set locals done
Event: 1657.336 Executing VM operation: get/set locals
Event: 1657.336 Executing VM operation: get/set locals done
Event: 1657.336 Executing VM operation: get/set locals
Event: 1657.336 Executing VM operation: get/set locals done
Event: 1660.762 Executing VM operation: ChangeBreakpoints
Event: 1660.762 Executing VM operation: ChangeBreakpoints done
Event: 1660.763 Executing VM operation: ChangeBreakpoints
Event: 1660.764 Executing VM operation: ChangeBreakpoints done
Event: 1660.764 Executing VM operation: ChangeBreakpoints
Event: 1660.764 Executing VM operation: ChangeBreakpoints done
Event: 1660.764 Executing VM operation: ChangeBreakpoints
Event: 1660.764 Executing VM operation: ChangeBreakpoints done
Event: 1660.764 Executing VM operation: ChangeBreakpoints
Event: 1660.764 Executing VM operation: ChangeBreakpoints done
Event: 1660.765 Executing VM operation: ChangeBreakpoints
Event: 1660.765 Executing VM operation: ChangeBreakpoints done
Event: 1660.765 Executing VM operation: ChangeBreakpoints
Event: 1660.765 Executing VM operation: ChangeBreakpoints done
Event: 1660.765 Executing VM operation: ChangeBreakpoints

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 42.079 Thread 0x000002d6ebbf4c00 Thread exited: 0x000002d6ebbf4c00
Event: 42.079 Thread 0x000002d6ebc24830 Thread exited: 0x000002d6ebc24830
Event: 42.085 Thread 0x000002d6eb9ace90 Thread added: 0x000002d6eb9b1060
Event: 42.085 Thread 0x000002d6eb9ace90 Thread exited: 0x000002d6eb9ace90
Event: 42.101 Thread 0x000002d6eb6336a0 Thread added: 0x000002d6eb9ad8b0
Event: 42.101 Thread 0x000002d6eb6336a0 Thread exited: 0x000002d6eb6336a0
Event: 42.133 Thread 0x000002d6eb632c80 Thread added: 0x000002d6eb9addc0
Event: 42.133 Thread 0x000002d6eb632c80 Thread exited: 0x000002d6eb632c80
Event: 47.146 Thread 0x000002d6ebbfbc10 Thread exited: 0x000002d6ebbfbc10
Event: 1660.762 Thread 0x000002d6a3c12180 Thread added: 0x000002d6ebbfbc10
Event: 1660.763 Thread 0x000002d6a3c12180 Thread added: 0x000002d6ebaf4800
Event: 1660.763 Thread 0x000002d6a3c12180 Thread added: 0x000002d6e9641690
Event: 1660.763 Thread 0x000002d6a3c12180 Thread added: 0x000002d6ebb19fb0
Event: 1660.763 Thread 0x000002d6a3c12180 Thread added: 0x000002d6ebbe5bb0
Event: 1660.763 Thread 0x000002d6a3c12180 Thread added: 0x000002d6ebbf0bd0
Event: 1660.763 Thread 0x000002d6a3c12180 Thread added: 0x000002d6803713b0
Event: 1660.763 Thread 0x000002d6a3c12180 Thread added: 0x000002d6eb9f4c40
Event: 1660.763 Thread 0x000002d6a3c12180 Thread added: 0x000002d6eb9f5160
Event: 1660.763 Thread 0x000002d6eb9b0640 Thread exited: 0x000002d6eb9b0640
Event: 1660.764 Thread 0x000002d6e9641690 Thread added: 0x000002d6ea09f610


Dynamic libraries:
0x00007ff7213a0000 - 0x00007ff7213ae000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\java.exe
0x00007fffd4cd0000 - 0x00007fffd4ec8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007fffd36d0000 - 0x00007fffd3792000 	C:\Windows\System32\KERNEL32.DLL
0x00007fffd2580000 - 0x00007fffd2876000 	C:\Windows\System32\KERNELBASE.dll
0x00007fffd2ad0000 - 0x00007fffd2bd0000 	C:\Windows\System32\ucrtbase.dll
0x00007fffce470000 - 0x00007fffce487000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jli.dll
0x00007fffce490000 - 0x00007fffce4ab000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\VCRUNTIME140.dll
0x00007fffd37a0000 - 0x00007fffd393d000 	C:\Windows\System32\USER32.dll
0x00007fffd2aa0000 - 0x00007fffd2ac2000 	C:\Windows\System32\win32u.dll
0x00007fffd2d00000 - 0x00007fffd2d2b000 	C:\Windows\System32\GDI32.dll
0x00007fffb6e40000 - 0x00007fffb70da000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007fffd2bd0000 - 0x00007fffd2cea000 	C:\Windows\System32\gdi32full.dll
0x00007fffd2980000 - 0x00007fffd2a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007fffd44d0000 - 0x00007fffd456e000 	C:\Windows\System32\msvcrt.dll
0x00007fffd4ad0000 - 0x00007fffd4aff000 	C:\Windows\System32\IMM32.DLL
0x00007fffcd340000 - 0x00007fffcd34c000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\vcruntime140_1.dll
0x00007fffbdb20000 - 0x00007fffbdbad000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\msvcp140.dll
0x00007fff0a210000 - 0x00007fff0ae7c000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\server\jvm.dll
0x00007fffd4b00000 - 0x00007fffd4baf000 	C:\Windows\System32\ADVAPI32.dll
0x00007fffd2d30000 - 0x00007fffd2dcf000 	C:\Windows\System32\sechost.dll
0x00007fffd4130000 - 0x00007fffd4253000 	C:\Windows\System32\RPCRT4.dll
0x00007fffd2550000 - 0x00007fffd2577000 	C:\Windows\System32\bcrypt.dll
0x00007fffd3540000 - 0x00007fffd35ab000 	C:\Windows\System32\WS2_32.dll
0x00007fffd18f0000 - 0x00007fffd193b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007fffcbd00000 - 0x00007fffcbd0a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007fffc5350000 - 0x00007fffc5377000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fffd1760000 - 0x00007fffd1772000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007fffd0200000 - 0x00007fffd0212000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007fffcd330000 - 0x00007fffcd33a000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jimage.dll
0x00007fffcf740000 - 0x00007fffcf924000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007fffb23f0000 - 0x00007fffb2424000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fffd24c0000 - 0x00007fffd2542000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007fff49990000 - 0x00007fff499cb000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jdwp.dll
0x00007fffb7cd0000 - 0x00007fffb7cde000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\instrument.dll
0x00007fffbdaf0000 - 0x00007fffbdb15000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\java.dll
0x00007fffbd980000 - 0x00007fffbd998000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\zip.dll
0x00007fffbda10000 - 0x00007fffbdae7000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\jsvml.dll
0x00007fffd2dd0000 - 0x00007fffd353f000 	C:\Windows\System32\SHELL32.dll
0x00007fffd0400000 - 0x00007fffd0ba3000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007fffd3cc0000 - 0x00007fffd4015000 	C:\Windows\System32\combase.dll
0x00007fffd1d90000 - 0x00007fffd1dbb000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007fffd4260000 - 0x00007fffd432d000 	C:\Windows\System32\OLEAUT32.dll
0x00007fffd4020000 - 0x00007fffd40cd000 	C:\Windows\System32\SHCORE.dll
0x00007fffd40d0000 - 0x00007fffd4125000 	C:\Windows\System32\shlwapi.dll
0x00007fffd2290000 - 0x00007fffd22b5000 	C:\Windows\SYSTEM32\profapi.dll
0x00007fffb7cc0000 - 0x00007fffb7ccc000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\dt_socket.dll
0x00007fffd1780000 - 0x00007fffd17bb000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007fffd1af0000 - 0x00007fffd1b5c000 	C:\Windows\system32\mswsock.dll
0x00007fffbd9f0000 - 0x00007fffbda09000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\net.dll
0x00007fffcd210000 - 0x00007fffcd31a000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007fffbd9d0000 - 0x00007fffbd9e6000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\nio.dll
0x00007fffb80e0000 - 0x00007fffb8113000 	C:\Program Files (x86)\Sangfor\SSL\ClientComponent\SangforNspX64.dll
0x00007fffd43a0000 - 0x00007fffd44cb000 	C:\Windows\System32\ole32.dll
0x00007fffd3c00000 - 0x00007fffd3c08000 	C:\Windows\System32\PSAPI.DLL
0x00007fffd17c0000 - 0x00007fffd188a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007fffd2cf0000 - 0x00007fffd2cf8000 	C:\Windows\System32\NSI.dll
0x000000006e940000 - 0x000000006e966000 	C:\Program Files\Bonjour\mdnsNSP.dll
0x00007fffba930000 - 0x00007fffba93a000 	C:\Windows\System32\rasadhlp.dll
0x00007fffbaab0000 - 0x00007fffbab30000 	C:\Windows\System32\fwpuclnt.dll
0x00007fffcd320000 - 0x00007fffcd329000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management.dll
0x00007fffbd9c0000 - 0x00007fffbd9cb000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\management_ext.dll
0x00007fffd1ce0000 - 0x00007fffd1cf8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007fffd13a0000 - 0x00007fffd13d8000 	C:\Windows\system32\rsaenh.dll
0x00007fffd2250000 - 0x00007fffd227e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007fffd1d00000 - 0x00007fffd1d0c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007fffcd660000 - 0x00007fffcd677000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007fffcdfe0000 - 0x00007fffcdffd000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007fffbeb20000 - 0x00007fffbeb37000 	C:\Windows\system32\napinsp.dll
0x00007fffbeb00000 - 0x00007fffbeb1b000 	C:\Windows\system32\pnrpnsp.dll
0x00007fffaea00000 - 0x00007fffaea15000 	C:\Windows\system32\wshbth.dll
0x00007fffce1e0000 - 0x00007fffce1fd000 	C:\Windows\system32\NLAapi.dll
0x00007fffbe0c0000 - 0x00007fffbe0d2000 	C:\Windows\System32\winrnr.dll
0x00007fffbd960000 - 0x00007fffbd969000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\extnet.dll
0x00007fffbd970000 - 0x00007fffbd97e000 	C:\Users\<USER>\.jdks\corretto-17.0.13\bin\sunmscapi.dll
0x00007fffd2360000 - 0x00007fffd24bd000 	C:\Windows\System32\CRYPT32.dll
0x00007fffd1e00000 - 0x00007fffd1e27000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007fffd1dc0000 - 0x00007fffd1dfb000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007fffaf4c0000 - 0x00007fffaf4c7000 	C:\Windows\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\.jdks\corretto-17.0.13\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;C:\Users\<USER>\.jdks\corretto-17.0.13\bin\server;C:\Program Files (x86)\Sangfor\SSL\ClientComponent;C:\Program Files\Bonjour

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:52826,suspend=y,server=n -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.2\captureAgent\debugger-agent.jar=file:/C:/Users/<USER>/AppData/Local/Temp/capture49613.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dfile.encoding=UTF-8 
java_command: cn.coder.zj.module.collector.Collector
java_class_path (initial): D:\git-zj\zj-module-client-collector\target\classes;D:\maven\repository\org\springframework\boot\spring-boot-starter-web\3.0.13\spring-boot-starter-web-3.0.13.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter\3.0.13\spring-boot-starter-3.0.13.jar;D:\maven\repository\org\springframework\boot\spring-boot\3.0.13\spring-boot-3.0.13.jar;D:\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.0.13\spring-boot-autoconfigure-3.0.13.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-logging\3.0.13\spring-boot-starter-logging-3.0.13.jar;D:\maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.19.0\log4j-to-slf4j-2.19.0.jar;D:\maven\repository\org\apache\logging\log4j\log4j-api\2.19.0\log4j-api-2.19.0.jar;D:\maven\repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven\repository\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-json\3.0.13\spring-boot-starter-json-3.0.13.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.14.3\jackson-databind-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.14.3\jackson-annotations-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-core\2.14.3\jackson-core-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.14.3\jackson-datatype-jdk8-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.14.3\jackson-datatype-jsr310-2.14.3.jar;D:\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.14.3\jackson-module-parameter-names-2.14.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-tomcat\3.0.13\spring-boot-starter-tomcat-3.0.13.jar;D:\maven\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 4                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 15                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8522825728                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8522825728                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\jdk17\jdk-17.0.13_windows-x64_bin\jdk-17.0.13
PATH=c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;D:\git\Git\cmd;D:\nojs\nvm;D:\nojs\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\idea\IntelliJ IDEA 2024.2.3\bin;D:\js\nvm;D:\js\nodejs;D:\js\nvm\v16.16.0;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\nojs\nvm;D:\nojs\nodejs;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\jdk17\jdk-17.0.13_windows-x64_bin\jdk-17.0.13\bin;C:\Users\<USER>\Downloads\fvm-3.2.1-windows-x64\fvm\fvm;C:\ProgramData\chocolatey\bin;D:\protoc\protoc-3.19.4-win64\bin;C:\Program Files\CursorModifier;C:\Program Files\Tesseract-OCR\;D:\maven1\apache-maven-3.9.9\bin;d:\Trae\bin;D:\pythonhome\Scripts\;D:\pythonhome\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\idea\IntelliJ IDEA 2024.2.3\bin;D:\js\nvm;D:\js\nodejs;D:\js\nvm\v16.16.0;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\nojs\nvm;D:\nojs\nodejs;D:\vscode\Microsoft VS Code\bin;C:\tools\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Pub\Cache\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
OS uptime: 0 days 9:16 hours

CPU: total 20 (initial active 20) (10 cores per cpu, 2 threads per core) family 6 model 183 stepping 1 microcode 0x123, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb
Processor Information for all 20 processors :
  Max Mhz: 3500, Current Mhz: 3500, Mhz Limit: 3500

Memory: 4k page, system-wide physical 32509M (13256M free)
TotalPageFile size 46845M (AvailPageFile size 15806M)
current process WorkingSet (physical memory assigned to process): 280M, peak: 280M
current process commit charge ("private bytes"): 351M, peak: 665M

vm_info: OpenJDK 64-Bit Server VM (17.0.13+11-LTS) for windows-amd64 JRE (17.0.13+11-LTS), built on Oct 10 2024 19:51:30 by "Administrator" with MS VC++ 16.10 / 16.11 (VS2019)

END.
