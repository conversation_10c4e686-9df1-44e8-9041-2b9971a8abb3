<?xml version="1.0" encoding="utf-8" ?>
<configuration debug="false">

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/log/logs.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/log/logs-%d{yyyy-MM-dd}.log
            </fileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="BUSINESS"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/log/business.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/log/business-%d{yyyy-MM-dd}.log
            </fileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="async_stdout" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="STDOUT"/>
    </appender>

    <appender name="async_file" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE"/>
    </appender>

    <appender name="async_business" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="BUSINESS"/>
    </appender>

    <logger name="com.baosight.paladin.tool.standalone.servicebase.log.BusinessLog" level="info">
        <appender-ref ref="async_business"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="async_stdout"/>
        <appender-ref ref="async_file"/>
    </root>
</configuration>
