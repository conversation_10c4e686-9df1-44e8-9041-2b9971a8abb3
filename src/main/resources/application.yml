server:
  port: 1160
  shutdown: graceful
  tomcat:
    threads:
      min-spare: 1
spring:
  application:
    name: ${HOSTNAME:@collector@}${PID}
  profiles:
    active: cluster
---
spring:
  config:
    activate:
      on-profile: cluster


async:
  core-size: 2
  max-size: 5
  queue-capacity: 10
  keep-alive-seconds: 30
  thread-name-prefix: async-

collector:
  #  start: false
  clientId: 140
  server: ***********:9098


