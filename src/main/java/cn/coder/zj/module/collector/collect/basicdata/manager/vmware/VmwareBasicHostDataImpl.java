package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.PerMonitorDO;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.vmware.RealtimePerfMonitor;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.HostHardwareSummary;
import com.vmware.vim25.HostSystemIdentificationInfo;
import com.vmware.vim25.HostSystemInfo;
import com.vmware.vim25.VirtualMachineSummary;
import com.vmware.vim25.mo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_HOST;
@Slf4j
public class VmwareBasicHostDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                try {
                    List<HostData> hostDataList = handleHostSystem(platform);
                    if (!CollUtil.isEmpty(hostDataList)) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(hostDataList)
                                .metricsName(BASIC_HOST.code())
                                .build();

                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
                } catch (Exception e) {
                    log.error("平台 [{}] 主机数据收集失败", platform.getPlatformName(), e);
                    throw new RuntimeException(e);
                }
            });
        }

    }

    private List<HostData> handleHostSystem(Platform platform) throws Exception {
        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();
        // 创建服务实例并获取主机列表
        List<HostSystem> systems = new ArrayList<>();
        HostSystem hostSystem;
        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("HostSystem");
        if (managedEntities != null) {
            for (ManagedEntity managedEntity : managedEntities) {
                hostSystem = (HostSystem) managedEntity;
                systems.add(hostSystem);
            }
        }

        if (systems.isEmpty()) {
            log.warn("平台 [{}] 未找到宿主机信息", platform.getPlatformName());
            return null;
        }

        // 处理物理机数据
        return processHardwareInfo(serviceInstance, systems, platform);
    }

    private List<HostData> processHardwareInfo(ServiceInstance serviceInstance, List<HostSystem> systems, Platform platform) throws Exception {
        List<HostData> list = new ArrayList<>();
        for (HostSystem host : systems) {
            HostData hostData = collectHostInfo(platform, host, serviceInstance);
            list.add(hostData);
        }
        return list;
    }

    private HostData collectHostInfo(Platform platform, HostSystem hostSystem, ServiceInstance serviceInstance) throws Exception {
        HostHardwareSummary hostHardwareSummary = hostSystem.getSummary().getHardware();
        BigDecimal memorySize = Convert.toBigDecimal(hostHardwareSummary.getMemorySize());
        BigDecimal availableCpuCapacity = NumberUtil.div(Convert.toBigDecimal(hostSystem.getSummary().getQuickStats().getOverallCpuUsage()), 1000, 2);
        BigDecimal hostMemoryUsage = NumberUtil.mul(Convert.toBigDecimal(hostSystem.getSummary().getQuickStats().getOverallMemoryUsage()), 1024 * 1024);
        BigDecimal availableMemoryCapacity = NumberUtil.sub(memorySize, hostMemoryUsage);
        String vms = hostSystem.getMOR().getVal();
        String state = mapPowerState(hostSystem.getRuntime().getPowerState().name());
        String ip = hostSystem.getName();
        String status = StrUtil.upperFirst(hostSystem.getRuntime().getConnectionState().name());
        String clusterUuid = hostSystem.getParent().getMOR().getVal();
        String clusterName = hostSystem.getParent().getName();
        String name = hostSystem.getParent().getParent().getParent().getName();
        String architecture = "x86_64";
        BigDecimal memoryUsed = NumberUtil.round(NumberUtil.mul(NumberUtil.div(hostMemoryUsage, memorySize), 100),2);
        ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntity("HostSystem", hostSystem.getName());

        double cpuUsage = calculateCpuUsage(managedEntities, hostSystem.getName(), serviceInstance, platform);
        long networkRx = calculateNetworkUsage(managedEntities, hostSystem.getName(), serviceInstance, "net.bytesRx.average", platform);
        long networkTx = calculateNetworkUsage(managedEntities, hostSystem.getName(), serviceInstance, "net.bytesTx.average", platform);
        long packetsRx = calculateNetworkUsage(managedEntities, hostSystem.getName(), serviceInstance, "net.packetsRx.summation", platform);
        double totalCapacity = 0;
        double freeCapacity = 0;
        Datastore[] dataStores = hostSystem.getDatastores();
        for (Datastore datastore : dataStores) {
            totalCapacity += datastore.getSummary().getCapacity();
            freeCapacity += datastore.getSummary().getFreeSpace();
        }
        double storageUsage = NumberUtil.sub(totalCapacity, freeCapacity);
        double diskUsed = NumberUtil.mul(NumberUtil.div(storageUsage, totalCapacity), 100);

        Integer cpuNum = Convert.toInt(hostHardwareSummary.getNumCpuThreads());

        HostData dto = new HostData();

        int cpuCommitRate = 0;
        int memCommitRate = 0;
        VirtualMachine[] vm = hostSystem.getVms();
        for (VirtualMachine virtualMachine : vm) {
            VirtualMachineSummary virtualMachineSummary = virtualMachine.getSummary();
            String stateName = virtualMachineSummary.getRuntime().getPowerState().name();
            if(stateName.equals("poweredOn") && !virtualMachine.getConfig().isTemplate()){
                Integer numCpu = virtualMachineSummary.getConfig().getNumCpu();
                Integer numMem = virtualMachineSummary.getConfig().getMemorySizeMB();
                cpuCommitRate += numCpu;
                memCommitRate += numMem;
            }
        }
        BigDecimal memTotal = NumberUtil.mul(Convert.toBigDecimal(memCommitRate), 1024, 1024);
        dto.setCpuCommitRate(new BigDecimal(cpuCommitRate));
        dto.setMemoryCommitRate(memTotal);

        dto.setCpuOverPercent(Convert.toBigDecimal(1));
        dto.setMemoryOverPercent(Convert.toBigDecimal(1));

        //品牌名称
        HostSystemInfo systemInfo = hostSystem.getHardware().getSystemInfo();
        String vendor = systemInfo.getVendor();
        dto.setBrandName(vendor);
        String model = systemInfo.getModel();
        dto.setModel(model);
        //序列号
        HostSystemIdentificationInfo[] identifyingInfo = systemInfo.getOtherIdentifyingInfo();
        String serialNumber = "-";
        if (identifyingInfo != null) {
            for (HostSystemIdentificationInfo info : identifyingInfo) {
                if ("ServiceTag".equals(info.getIdentifierType().getKey())) {
                    serialNumber = info.getIdentifierValue();
                    break;
                }
            }
        }
        dto.setSerialNumber(serialNumber);
        dto.setCpuType(hostSystem.getHardware().getCpuPkg()[0].getDescription());

        dto.setReservedMemory(new BigDecimal(0));
        dto.setUuid(clusterUuid + hostHardwareSummary.getUuid() + vms);
        dto.setName(hostSystem.getName());
        dto.setState(state);
        dto.setIp(ip);
        dto.setStatus(status);
        dto.setClusterUuid(clusterUuid);
        dto.setClusterName(clusterName);
        dto.setTotalCpuCapacity(Convert.toLong(cpuNum));
        dto.setAvailableCpuCapacity(Convert.toLong(availableCpuCapacity));
        dto.setMemoryUsed(memoryUsed);
        dto.setCpuUsed(Convert.toBigDecimal(cpuUsage));
        dto.setDiskUsed(Convert.toBigDecimal(diskUsed));
        dto.setDiskUsedBytes(Convert.toBigDecimal(storageUsage));
        dto.setDiskFreeBytes(Convert.toBigDecimal(freeCapacity));
        dto.setTotalDiskCapacity(Convert.toBigDecimal(totalCapacity));
        dto.setCpuSockets(Convert.toInt(hostHardwareSummary.getNumCpuPkgs()));
        dto.setArchitecture(architecture);
        dto.setCpuNum(cpuNum);
        dto.setTotalMemoryCapacity(Convert.toLong(memorySize));
        dto.setAvailableMemoryCapacity(Convert.toLong(availableMemoryCapacity));
        dto.setDeleted(0);
        dto.setVms(vms);
        dto.setTypeName("vmware");
        dto.setBandwidthUpstream(Convert.toBigDecimal(networkRx));
        dto.setBandwidthDownstream(Convert.toBigDecimal(networkTx));
        dto.setPacketRate(Convert.toBigDecimal(packetsRx));
        dto.setRegionId(platform.getRegionId());
        dto.setManager(platform.getPlatformName());
        dto.setAvailableManager(name);
        dto.setPlatformId(platform.getPlatformId());
        dto.setPlatformName(platform.getPlatformName());
        dto.setIsMaintain(0);
        dto.setTotalVirtualMemory(memorySize);
        return dto;
    }

    private double calculateCpuUsage(ManagedEntity managedEntity, String name, ServiceInstance serviceInstance, Platform platform) throws Exception {
        List<PerMonitorDO> doList = RealtimePerfMonitor.getPerEntityMetricBasesByName(name, serviceInstance, managedEntity, "cpu.usage.average", platform);
        return doList.isEmpty() ? 0 : NumberUtil.div((float) doList.stream().mapToLong(PerMonitorDO::getValue).sum() / doList.size(), 100,2);
    }

    private long calculateNetworkUsage(ManagedEntity managedEntity, String name, ServiceInstance serviceInstance, String metric, Platform platform) throws Exception {
        List<PerMonitorDO> networkList = RealtimePerfMonitor.getPerEntityMetricBasesByName(name, serviceInstance, managedEntity, metric, platform);
        return NumberUtil.round(networkList.stream().mapToLong(PerMonitorDO::getValue).sum(), 2).longValue();
    }

    private String mapPowerState(String powerState) {
        return switch (powerState) {
            case "poweredOn" -> "Enabled";
            case "poweredOff" -> "Disabled";
            case "stopped" -> "Suspended";
            default -> "Unknown";
        };
    }

    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_HOST.code();
    }
}
