package cn.coder.zj.module.collector.collect.basicdata.manager.winhong;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.StorageData;
import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_WIN_HONG_STORAGE;

@Slf4j
public class WinHongBasicStorageDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        StorageData storageData = new StorageData();

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<StorageData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_STORAGE.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<StorageData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);


        List<StorageData> storageList = new ArrayList<>();
        JsonArray storages = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_STORAGE_POOLS, null, headers).getAsJsonObject().get("data").getAsJsonArray();
        if (!storages.isEmpty()) {
            for (JsonElement vstorage : storages) {
                JsonObject storage = (JsonObject) vstorage;
                BigDecimal availableCapacity = new BigDecimal(storage.get("available").getAsString());
                BigDecimal totalPhysicalCapacity = new BigDecimal(storage.get("capacity").getAsString());
                String uuid = storage.get("id").getAsString();
                StorageData storageRespCreateReqDTO = new StorageData();
                storageRespCreateReqDTO.setName(storage.get("name").getAsString());
                storageRespCreateReqDTO.setUuid(uuid);
                storageRespCreateReqDTO.setUrl(storage.get("storageResourceName") != null ? storage.get("storageResourceName").getAsString() : "not used");
                storageRespCreateReqDTO.setState(Integer.valueOf(storage.get("status").getAsString()) == 2 ? "Enabled" : "Disabled");
                storageRespCreateReqDTO.setStatus(Integer.valueOf(storage.get("status").getAsString()) == 2 ? "Connected" : "Disconnected");
                storageRespCreateReqDTO.setTotalCapacity(Long.valueOf(storage.get("capacity").getAsString()));
                storageRespCreateReqDTO.setTotalPhysicalCapacity(totalPhysicalCapacity);
                storageRespCreateReqDTO.setUsedCapacity(Long.valueOf(storage.get("usedCapacity").getAsString()));
                storageRespCreateReqDTO.setAvailableCapacity(availableCapacity);
                storageRespCreateReqDTO.setAvailablePhysicalCapacity(new BigDecimal(storage.get("available").getAsString()));
                if (storage.get("usedCapacity").getAsString().equals("0")) {
                    storageRespCreateReqDTO.setCapacityUtilization(new BigDecimal(0));
                } else {
                    storageRespCreateReqDTO.setCapacityUtilization(new BigDecimal(storage.get("usedCapacity").getAsString()).divide(new BigDecimal(storage.get("capacity").getAsString()), 2, RoundingMode.HALF_UP));
                }
                storageRespCreateReqDTO.setType(getType(storage.get("type").getAsString()));
                storageRespCreateReqDTO.setPlatformId(platform.getPlatformId());
                storageRespCreateReqDTO.setPlatformName(platform.getPlatformName());
                storageRespCreateReqDTO.setRegionId(platform.getRegionId());
                storageRespCreateReqDTO.setDeleted(0);
                storageRespCreateReqDTO.setTypeName("winHong");
                ZonedDateTime zonedDateTime = ZonedDateTime.parse(storage.get("time").getAsString(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ"));
                Date date = Date.from(zonedDateTime.toInstant());
                storageRespCreateReqDTO.setCreateTime(date);
                storageRespCreateReqDTO.setSCreateTime(date);

                storageRespCreateReqDTO.setMediaType("机械盘");
                storageRespCreateReqDTO.setManager("winhong");
                storageRespCreateReqDTO.setStoragePercent(new BigDecimal(1));


                //虚拟可用容量
                BigDecimal availableDecimal = availableCapacity.compareTo(new BigDecimal(0)) > 0 ? availableCapacity : new BigDecimal(0);
                //虚拟容量
                storageRespCreateReqDTO.setVirtualCapacity(totalPhysicalCapacity.multiply(storageRespCreateReqDTO.getStoragePercent()));
                storageRespCreateReqDTO.setAllocation(totalPhysicalCapacity.subtract(availableDecimal));
                if (availableDecimal.compareTo(new BigDecimal(0)) > 0) {
                    storageRespCreateReqDTO.setCommitRate(storageRespCreateReqDTO.getAllocation().divide(availableDecimal, 2, RoundingMode.HALF_UP));
                } else {
                    storageRespCreateReqDTO.setCommitRate(BigDecimal.valueOf(0));
                }
                storageRespCreateReqDTO.setVUpdateTime(date);
                storageRespCreateReqDTO.setRemark(getStringFromJson(storage, "remark"));

                //查询存储池关联的主机列表
                JsonArray hoststorage = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_STORAGE_POOLS_HOSTS.replace("{storagePoolId}", uuid), null, headers).getAsJsonObject().get("data").getAsJsonArray();
                storageRespCreateReqDTO.setStorageHostRelationDataList(
                        Optional.ofNullable(hoststorage)
                                .map(hosts -> StreamSupport.stream(hosts.spliterator(), false)
                                        .map(host -> new StorageHostRelationData()
                                                .setHardwareUuid(getStringFromJson(host.getAsJsonObject(), "hostId", ""))
                                                .setStorageUuid(uuid)
                                                .setPlatformId(platform.getPlatformId())
                                                .setPlatformName(platform.getPlatformName()))
                                        .collect(Collectors.toList()))
                                .orElse(new ArrayList<>()));
                storageList.add(storageRespCreateReqDTO);
            }
        }
        return storageList;
    }

    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            return GSON.fromJson(response.body().string(), JsonElement.class);
        } catch (IOException e) {
            log.error("error collecting basic data: {}", e.getMessage());
            return new JsonObject();
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_WIN_HONG_STORAGE.code();
    }

    public String getType(String type) {
        switch (type) {
            case "1":
                return "fc存储";
            case "2":
                return "iscsi存储";
            case "3":
                return "nfs存储";
            case "4":
                return "分布式存储";
            case "5":
                return "本地存储";
            case "6":
                return "共享存储";
            case "6+1":
                return "fc共享存储";
            case "6+2":
                return "iscsi 共享存储";
            case "6+6":
                return "nvme共享存储";
            default:
                return "本地存储";
        }
    }
}
