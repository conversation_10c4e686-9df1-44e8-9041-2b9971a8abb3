package cn.coder.zj.module.collector.util;

import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

import java.util.Map;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
@Slf4j
public class ApiUtil {
    public static JsonArray getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            // 检查HTTP状态码
            if (!response.isSuccessful()) {
                if (response.code() == 401 || response.code() == 403) {
                    log.error("API认证失败, URL: {}, 状态码: {}, 可能是token过期", url, response.code());
                } else {
                    log.error("API请求失败, URL: {}, 状态码: {}", url, response.code());
                }
                return new JsonArray();
            }
            
            String responseBody = response.body().string();
            if (responseBody == null || responseBody.trim().isEmpty()) {
                log.warn("API返回空响应体, URL: {}", url);
                return new JsonArray();
            }
            
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            if (element != null && element.isJsonObject()) {
                JsonElement dataElement = element.getAsJsonObject().get("data");
                if (dataElement != null && dataElement.isJsonArray()) {
                    return dataElement.getAsJsonArray();
                }
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {},请求头: {}", url, e.getMessage(),headers.toString());
            return new JsonArray();
        }
    }

    public static JsonObject getJsonObjectFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            JsonElement dataElement = element.getAsJsonObject().get("data");
            if (dataElement != null && dataElement.isJsonObject()) {
                return dataElement.getAsJsonObject();
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonObject();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {},请求头: {}", url, e.getMessage(),headers.toString());
            return new JsonObject();
        }
    }

    public static JsonObject getJsonObjectFromFsApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            if (element != null && element.isJsonObject()) {
                return element.getAsJsonObject();
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonObject();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {},请求头: {}", url, e.getMessage(),headers.toString());
            return new JsonObject();
        }
    }

    public static JsonArray getJsonArrayFromFsApi(String url, Map<String, String> headers,JsonArray requestArray) {
        try (Response response = OkHttpService.postJsonFs(url, headers,requestArray)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            if (element != null && element.isJsonObject()) {
                return element.getAsJsonObject().getAsJsonArray("items");
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {},请求头: {}", url, e.getMessage(),headers.toString());
            return new JsonArray();
        }
    }

    public static JsonObject getNologJsonObjectFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            JsonElement dataElement = element.getAsJsonObject().get("data");
            if (dataElement != null && dataElement.isJsonObject()) {
                return dataElement.getAsJsonObject();
            }
            return new JsonObject();
        } catch (Exception e) {
            return new JsonObject();
        }
    }

    public static JsonArray getInSpurJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            if (element != null && element.isJsonArray()) {
                return element.getAsJsonArray();
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {},请求头: {}", url, e.getMessage(),headers.toString());
            return new JsonArray();
        }
    }
}
