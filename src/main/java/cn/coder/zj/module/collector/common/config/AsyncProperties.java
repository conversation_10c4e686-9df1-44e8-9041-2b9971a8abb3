package cn.coder.zj.module.collector.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "async")
public class AsyncProperties {

    /**
     * 核心线程数
     */
    @Value("${async.core-size:5}")
    private int coreSize;

    /**
     * 最大线程数
     */
    @Value("${async.max-size:10}")
    private int maxSize;

    /**
     * 线程队列
     */
    @Value("${async.queue-capacity:20}")
    private int queueCapacity;

    /**
     * 存活时间
     */
    @Value("${async.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    /**
     * 线程名字前缀
     */
    @Value("${async.thread-name-prefix:async-}")
    private String threadNamePrefix;
}
