package cn.coder.zj.module.collector.collect.basicdata.manager.winhong;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StateUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static cn.coder.zj.module.collector.util.ApiUtil.getInSpurJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_WIN_HONG_VM;

@Slf4j
public class WinHongBasicVmDataImpl extends AbstractBasicData {
    protected long startTime;


    private static final BigDecimal ZERO = new BigDecimal(0);
    private static final String KVM = "KVM";
    private static final String VM_UUID_PREFIX = "VMUuid=";


    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) o;
                String token = platform.getWinHongPlatform().getToken();
                if (token == null) {
                    log.error("平台 {} token为空", platform.getPlatformName());
                    return;
                }
                List<VmData> list = collectData(platform);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VM.code())
                        .build();
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
            });
        }
    }

    private List<VmData> collectData(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);
        Map<String, String> param = new HashMap<>();
        param.put("isPaginate", "false");
        param.put("needDiskInfo", "true");
        param.put("needHostName", "true");
        param.put("needInterface", "true");
        param.put("needMonitorInfo", "true");
        param.put("needPoolName", "true");
        param.put("needClusterName", "true");
        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_CLOUDS, param, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<VmData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    VmData vmData = collectVmInfo(platform, jsonElement,headers);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理主机数据异常, hostMap: {}, error: {}", jsonElement, e.getMessage());
                }
            }
        }
        return dataList;
    }

    private VmData collectVmInfo(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
        VmData vmData = new VmData();
        String uuid = getStringFromJson(jsonObject, "uuid", "");
        String platformUrl = platform.getPlatformUrl();
        // 获取所有必要的API数据
        JsonObject summary = getJsonObjectFromApi(platformUrl + WinHongApiConstant.GET_SUMMARY.replace("{domainId}", uuid), null, headers);
        Map<String, String> param = new HashMap<>(2);
        param.put("needMonitorInfo", "true");
        param.put("domainFlag", "4");
        JsonObject domainInfo = getJsonObjectFromApi(platformUrl + WinHongApiConstant.GET_DOMAIN_INFO.replace("{domainId}", uuid), param, headers);
        JsonArray diskInfos = getDiskJsonArrayFromApi(platformUrl + WinHongApiConstant.GET_DOMAIN_DISK_INFO.replace("{domainId}", uuid), null, headers);
        JsonObject cpuAndMemoryRate = getJsonObjectFromApi(platformUrl + WinHongApiConstant.GET_CPU_AND_MEMORY_RATE.replace("{domainId}", uuid), null, headers);
        JsonObject tool = getJsonObjectFromApi(platformUrl + WinHongApiConstant.GET_CLOUD_TOOL.replace("{domainId}", uuid), null, headers);
        JsonArray tags = getInSpurJsonArrayFromApi(platformUrl + WinHongApiConstant.GET_TAG.replace("{domainId}", uuid), null, headers);
        List<String> tagValues = new ArrayList<>();
        if (CollUtil.isNotEmpty(tags)) {
            for (JsonElement json : tags) {
                JsonObject tagDO = GsonUtil.GSON.toJsonTree(json).getAsJsonObject();
                String tag = getStringFromJson(tagDO, "tagName", "");
                String tagId = getStringFromJson(tagDO, "tagId", "");
                if(StrUtil.isNotEmpty(tag) && StrUtil.isNotEmpty(tagId)){
                    tagValues.add(tag+"&"+tagId);
                }
            }
        }
        String resultTag = tagValues.isEmpty() ? null :(tagValues.size() == 1 ? tagValues.get(0) : String.join(",", tagValues));
        // 设置基本信息
        vmData.setTag(resultTag);
        vmData.setDeleted(0);
        vmData.setUuid(uuid);
        vmData.setName(getStringFromJson(jsonObject, "name", "-"));
        String state = getStringFromJson(jsonObject, "status", "-");
        vmData.setState(StateUtil.stateConvert(state));
        vmData.setPowerState(StateUtil.powerStateConvert(state));
        vmData.setIp(getStringFromJson(jsonObject, "ip", ""));
        vmData.setZoneUuid(getStringFromJson(jsonObject, "poolId", ""));
        vmData.setZoneName(getStringFromJson(jsonObject, "poolName", ""));
        vmData.setClusterUuid(getStringFromJson(jsonObject, "clusterId", ""));
        vmData.setClusterName(getStringFromJson(jsonObject, "clusterName", ""));
        vmData.setHardwareUuid(getStringFromJson(jsonObject, "hostId", ""));
        vmData.setHardwareName(getStringFromJson(jsonObject, "hostName", ""));
        vmData.setGuestOsType(getStringFromJson(jsonObject, "osVersion", ""));
        vmData.setArchitecture(getStringFromJson(summary, "cpuArchitecture", ""));

        String toolVersion = getStringFromJson(tool, "toolVersion", "");
        vmData.setToolsInstalled(StrUtil.isNotEmpty(toolVersion)?"false":"true");
        vmData.setToolsRunStatus(StrUtil.isNotEmpty(toolVersion) ? "run" : "stop");
        // 设置CPU和内存信息
        int cpuNum = domainInfo.get("cpu").getAsJsonObject().get("cores").getAsInt();
        vmData.setCpuNum(cpuNum);
        vmData.setMemorySize(getLongFromJson(jsonObject, "memory"));
        vmData.setCpuUsed(getBigFromJson(cpuAndMemoryRate, "cpuRate"));
        vmData.setMemoryUsed(getBigFromJson(cpuAndMemoryRate, "memRate"));

        // 设置镜像信息
        String imageUuid = getStringFromJson(domainInfo, "templateId", "");
        String imageName = getStringFromJson(domainInfo, "templateName", "");
        vmData.setImageUuid(imageUuid);
        vmData.setImageName(imageName);
        vmData.setIso(imageName);

        // 设置MAC地址
        JsonArray bridgeInterfaces = jsonObject.get("bridgeInterfaces").getAsJsonArray();
        if (bridgeInterfaces.size() > 0) {
            String mac = bridgeInterfaces.get(0).getAsJsonObject().get("mac").getAsString();
            vmData.setMac(StrUtil.isEmpty(mac) ? "-" : (mac.contains(",") ? mac.split(",")[0].trim() : mac));
        } else {
            vmData.setMac("");
        }

        // 设置创建日期
        String createDateStr = getStringFromJson(jsonObject, "createTime", "");
        try {
            if (StrUtil.isNotEmpty(createDateStr)) {
                // 先尝试解析原始日期字符串
                Date createDate = DateUtil.parse(createDateStr);
                // 格式化为标准格式
                String formattedDate = DateUtil.format(createDate, "yyyy-MM-dd HH:mm:ss");
                vmData.setVCreateDate(DateUtil.parse(formattedDate));
            } else {
                // 如果日期为空，使用当前时间并格式化
                vmData.setVCreateDate(DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
            }
        } catch (Exception e) {
            log.warn("日期解析失败: {}, 使用当前时间", createDateStr);
            vmData.setVCreateDate(DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
        }

        // 设置平台信息
        vmData.setRegionId(platform.getRegionId());
        vmData.setPlatformId(platform.getPlatformId());
        vmData.setPlatformName(platform.getPlatformName());
        vmData.setType("winhong");
        vmData.setTypeName(platform.getTypeCode());

        // 处理磁盘信息
        List<Map> diskList = GSON.fromJson(diskInfos, new TypeToken<List<Map>>(){}.getType());
        Long totalDiskCapacity = 0L, diskUsedBytes = 0L;
        BigDecimal diskUsed = ZERO;
        if (!diskList.isEmpty()) {
            // 处理第一个磁盘（系统盘）
            Map systemDisk = diskList.get(0);
            totalDiskCapacity = parseLongSafely(systemDisk.get("capacity"), "系统盘容量");
            diskUsedBytes = parseLongSafely(systemDisk.get("allocation"), "系统盘已分配空间");
            if(totalDiskCapacity != 0L){
                diskUsed = new BigDecimal(diskUsedBytes).divide(new BigDecimal(totalDiskCapacity)).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
            }
        }

        if(StrUtil.isNotEmpty(toolVersion)){
            vmData.setDiskUsed(getBigFromJson(cpuAndMemoryRate, "diskRate"));
        }else {
            vmData.setDiskUsed(diskUsed);
        }
        // 设置磁盘和网络信息
        vmData.setActualSize(new BigDecimal(diskUsedBytes));
        vmData.setCloudSize(new BigDecimal(totalDiskCapacity));
        vmData.setTotalDiskCapacity(new BigDecimal(totalDiskCapacity));
        vmData.setDiskFreeBytes(new BigDecimal(totalDiskCapacity - diskUsedBytes));
        vmData.setDiskUsedBytes(new BigDecimal(diskUsedBytes));
        vmData.setNetworkInBytes(getBigFromJson(jsonObject, "inAvgBandwidth"));
        vmData.setNetworkOutBytes(getBigFromJson(jsonObject, "outAvgBandwidth"));
        vmData.setNetworkInPackets(getBigFromJson(jsonObject, "inAvgBandwidth"));
        vmData.setNetworkOutPackets(getBigFromJson(jsonObject, "outBurstSize"));

        // 设置其他属性
        vmData.setAutoInitType(domainInfo.get("isHa").getAsBoolean() ? "NeverStop" : "None");
        vmData.setGuideMode(domainInfo.get("bootType").getAsInt() == 0 ? "Legacy" : "UEFI");

        return vmData;
    }

    private JsonArray getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);

            // 检查响应是否为有效的JSON对象
            if (element != null && element.isJsonObject()) {
                JsonElement dataElement = element.getAsJsonObject().get("data");
                // 检查data字段是否为数组
                if (dataElement != null && dataElement.isJsonArray()) {
                    return dataElement.getAsJsonArray();
                }
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }

    private JsonArray getDiskJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);

            // 检查响应是否为有效的JSON对象
            if (element != null && element.isJsonObject()) {
                JsonElement dataElement = element.getAsJsonObject().get("diskDevices");
                // 检查data字段是否为数组
                if (dataElement != null && dataElement.isJsonArray()) {
                    return dataElement.getAsJsonArray();
                }
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }

    private JsonObject getJsonObjectFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);

            // 检查响应是否为有效的JSON对象
            if (element != null && element.isJsonObject()) {
                return element.getAsJsonObject();
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonObject();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonObject();
        }
    }

    private Long parseLongSafely(Object value, String fieldName) {
        if (value == null) {
            return 0L;
        }
        try {
            return new BigDecimal(value.toString()).longValue();
        } catch (Exception e) {
            log.warn("转换{}失败: {}", fieldName, value);
            return 0L;
        }
    }

    private String getStringFromJson(JsonObject jsonObject, String key, String defaultValue) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsString();
        }
        return defaultValue;
    }

    private Long getLongFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsLong();
        }
        return 0L;
    }


    private BigDecimal getBigFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsBigDecimal();
        }
        return new BigDecimal(0);
    }

    private String stateConvert(String state) {
        String target = "";
        switch (state) {
            case "1":
                target = "Running";
                break;
            case "starting":
                target = "Starting";
                break;
            case "Stopping":
                target = "Stopping";
                break;
            case "2":
                target = "Stopped";
                break;
            case "resetting":
                target = "Rebooting";
                break;
            case "deleting":
                target = "Destroying";
                break;
            case "suspend":
                target = "Stopped";
                break;
            case "suspending":
                target = "Stopping";
                break;
            default:
                target = "Unknown";
        }
        return target;
    }

    public static String powerStateConvert(String state) {
        return  switch (state) {
            case "Created" -> "on";
            case "Starting" -> "on";
            case "Running" -> "on";
            case "Stopping" -> "off";
            case "Stopped" -> "off";
            case "Unknown" -> "unknown";
            case "Rebooting" -> "on";
            case "Destroyed" -> "off";
            case "Destroying" -> "off";
            case "Migrating" -> "on";
            case "Expunging" -> "off";
            case "Paunging" -> "off";
            case "Paused" -> "off";
            case "Resuming" -> "on";
            case "VolumeMigrating" -> "on";
            default -> "unknown";
        };
    }

    @Override
    public String supportProtocol() {
        return BASIC_WIN_HONG_VM.code();
    }
}
