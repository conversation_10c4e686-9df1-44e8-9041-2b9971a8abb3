package cn.coder.zj.module.collector.dal.platform.zstack;

import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZsTackPlatform {

    /**
     * 认证token
     */
    private String token;

    /**
     * 虚拟机uuid
     */
    private List<MonitorInfo> vmUuids;

    /**
     * 主机uuid
     */
    private List<MonitorInfo> hostUuids;

    private Long type;

    private String userName;

    private String password;


}
