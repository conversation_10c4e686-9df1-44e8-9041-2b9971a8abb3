package cn.coder.zj.module.collector.service.vmware;

import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.manager.PerMonitorDO;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.vmware.vim25.mo.ManagedEntity;
import com.vmware.vim25.mo.ServiceInstance;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;

/**
 * VMware监控指标工具类
 * 用于抽取VMware各监控实现类的公共方法
 */
@Slf4j
public class VmwareMetricsUtil {

    /**
     * 创建指标数据对象
     */
    public static MetricData createMetricData(Long platformId, String resourceId, String resourceName, String metricName) {
        MetricData metricData = new MetricData();
        metricData.setPlatformId(platformId);
        metricData.setResourceId(resourceId);
        metricData.setResourceName(resourceName);
        metricData.setMetricName(metricName);
        metricData.setValues(new ArrayList<>());
        metricData.setTimestamps(new ArrayList<>());
        return metricData;
    }

    /**
     * 通用的指标收集方法
     */
    public static void collectMetric(ServiceInstance serviceInstance, Platform platform, 
                                    MetricData metricData, ManagedEntity managedEntity, 
                                    String entityName, String counterName,
                                    BiFunction<PerMonitorDO, Platform, Double> valueConverter,String type) throws Exception {
        List<PerMonitorDO> metricList = RealtimePerfMonitor.getPerEntityMetricBasesByName(
                entityName, serviceInstance, managedEntity, counterName, platform);
                
        if (metricList.isEmpty()) {
            return;
        }
        //取metricList数据中最后一个数据的时间戳和value
        // metricList value 为负数去除
        metricList.removeIf(metric -> metric.getValue() < 0);
        PerMonitorDO lastMetric = metricList.get(metricList.size() - 1);
        if (lastMetric.getInstance().isEmpty()) {
            lastMetric.setInstance("0");
        }
        // 使用转换函数处理值
        Double value = valueConverter.apply(lastMetric, platform);
        Long timestamp = lastMetric.getDateTime().getTime() / 1000;
        metricData.getTimestamps().add(timestamp);
        metricData.getValues().add(value);
        metricData.setType(type);
    }

    /**
     * CPU值转换器 - 百分比值
     */
    public static Double cpuValueConverter(PerMonitorDO perMonitorDO, Platform platform) {
        BigDecimal value = NumberUtil.div(Convert.toBigDecimal(perMonitorDO.getValue()), 100, 2);
        if (value.doubleValue() > 100) {
            return 100.00;  // 如果值超过100%，则返回100%作为上限
        } else {
            return value.doubleValue();
        }
    }

    /**
     * 磁盘值转换器 - KB/s转换为B/s
     */
    public static Double diskValueConverter(PerMonitorDO perMonitorDO, Platform platform) {
        BigDecimal value = Convert.toBigDecimal(perMonitorDO.getValue());
        // 将KB/s转换为B/s (乘以1024)
        BigDecimal bytesValue = NumberUtil.mul(value, 1024);
        return bytesValue.doubleValue();
    }

    /**
     * 内存值转换器 - KB转换为B
     */
    public static Double memValueConverter(PerMonitorDO perMonitorDO, Platform platform) {
        BigDecimal value = Convert.toBigDecimal(perMonitorDO.getValue());
        // 将KB转换为B (乘以1024)
        BigDecimal bytesValue = NumberUtil.mul(value, 1024);
        return bytesValue.doubleValue();
    }

    /**
     * 网络值转换器 - KB/s转换为B/s
     */
    public static Double netValueConverter(PerMonitorDO perMonitorDO, Platform platform) {
        BigDecimal value = Convert.toBigDecimal(perMonitorDO.getValue());
        // 将KB/s转换为B/s (乘以8再乘以1024)
        BigDecimal bitsValue = NumberUtil.mul(value, 8);
        BigDecimal bytesValue = NumberUtil.mul(bitsValue, 1024);
        return bytesValue.doubleValue();
    }
}