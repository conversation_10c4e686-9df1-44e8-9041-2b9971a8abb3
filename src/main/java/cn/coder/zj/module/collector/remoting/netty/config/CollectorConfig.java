package cn.coder.zj.module.collector.remoting.netty.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@Data
@Component
@ConfigurationProperties(prefix = "collector")
public class CollectorConfig {

    /**
     * 服务端的ip,port
     */
    private String server;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 是否启动,默认开启
     */
    private boolean start = true;

    /**
     *  密钥
     */
    private String clientSecretKey="c39987a6807dc7a786dbef193a59bff44bb13ce96bdc491e9ac290a9534c176b";
    

    private Map<Integer,String > serverInfoMap = new HashMap<>(16);

    @PostConstruct
    public void initServerInfo() {
        String[] serverList = server.split(",");
        for (String address : serverList) {
            String[] parts = address.split(":");
            if (parts.length == 2) {
                String ip = parts[0];
                int port = Integer.parseInt(parts[1]);
                serverInfoMap.put(port,ip );
            } else {
                throw new IllegalArgumentException("Invalid address format: " + address);
            }
        }
    }

    /**
     * 提供公共方法来获取解析后的服务器信息
     *
     * @return Map<String, Integer> 包含IP和端口的映射
     */
    public Map<Integer,String> getServerInfo() {
        return serverInfoMap;
    }
}
