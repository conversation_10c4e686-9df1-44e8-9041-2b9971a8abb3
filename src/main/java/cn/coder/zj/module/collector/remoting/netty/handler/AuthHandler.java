package cn.coder.zj.module.collector.remoting.netty.handler;

import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.enums.MessageType;
import cn.coder.zj.module.collector.remoting.netty.config.CollectorConfig;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import static cn.coder.zj.module.collector.job.annotation.YQJobProcessor.TASK_CACHE;

/**
 * <AUTHOR>
 **/
@Slf4j
public class AuthHandler extends ChannelInboundHandlerAdapter {

    private final CollectorConfig collectorConfig;

    public AuthHandler(CollectorConfig collectorConfig) {
        this.collectorConfig = collectorConfig;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        //发送鉴权消息
        ClusterMsg.Message messageInfo = ClusterMsg.Message.newBuilder()
                .setClientId(collectorConfig.getClientId())
                .setType(ClusterMsg.MessageType.AUTH)
                .setData(new Gson().toJson(collectorConfig))
                .build();
        ctx.writeAndFlush(messageInfo);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        log.info("msg",msg);
        if (msg instanceof ClusterMsg.Message response) {
            if (MessageType.AUTH.code().equals(response.getType().getNumber())) {
                log.info("auth successful !");
                CacheService.putCtx("ctx", ctx);
            }
            ctx.fireChannelRead(msg);
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("netty server error:{}", ExceptionUtils.getStackTrace(cause));
        ctx.close();
    }

}
