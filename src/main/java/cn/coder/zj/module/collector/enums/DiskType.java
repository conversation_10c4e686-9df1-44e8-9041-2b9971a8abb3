package cn.coder.zj.module.collector.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public enum DiskType {

    PROTOCOL_ZS_TACK_DISK("zstackDisk", "zstack内存"),
    PROTOCOL_FUSION_COMPUTE_DISK("fusionComputeDisk", "fusionCompute内存"),
    PROTOCOL_IS_TACK_DISK("istackDisk", "istack内存"),
    PROTOCOL_VMWARE_DISK("vmwareDisk", "vmware内存"),
    PROTOCOL_WIN_HONG_DISK("winHongDisk", "winHong内存"),
    PROTOCOL_IN_SPUR_DISK("inSpurisk", "浪潮磁盘"),
    PROTOCOL_SXF_DISK("sxfDisk", "深信服内存");

    private final String code;

    private final String desc;


    public static DiskType fromCode(String code) {
        for (DiskType typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String code() {
        return this.code;
    }

    public static List<String> getAllDiskTypeCodes() {
        List<String> codes = new ArrayList<>();
        for (DiskType diskType : DiskType.values()) {
            codes.add(diskType.code());
        }
        return codes;
    }
}
