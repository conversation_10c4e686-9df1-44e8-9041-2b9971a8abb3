package cn.coder.zj.module.collector.collect.basicdata.manager.sangfor;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.StorageData;
import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_SXF_STORAGE;
@Slf4j
public class SangForBasicStorageDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<StorageData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_STORAGE.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<StorageData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie, "CSRFPreventionToken", token);
        String platformUrl = platform.getPlatformUrl();
        List<StorageData> dataList = new ArrayList<>();

        // 获取存储列表
        JsonArray storageArray = getLocalJsonArrayFromApi(platformUrl + SangForApiConstant.GET_STORAGE_LIST, null, headers);
        if (CollUtil.isNotEmpty(storageArray)) {
            // 处理普通存储
            for (JsonElement jsonElement : storageArray) {
                try {
                    StorageData vmData = collectStorageData(platform, jsonElement, headers);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理深信存储1数据异常, hostMap: {}, error: {}", jsonElement, e.getMessage());
                }
            }
        } else {
            // 处理云存储
            JsonArray storageClouds = getCloudJsonArrayFromApi(platformUrl + SangForApiConstant.GET_STORAGE_CLOUD_LIST, null, headers);
            if (storageClouds.size() == 0) return dataList;

            // 获取实时数据并创建映射
            JsonArray storageCloudsRealTime = getCloudJsonArrayFromApi(platformUrl + SangForApiConstant.GET_STORAGE_CLOUD_LIST_REALTIME, null, headers);
            Map<String, String> realTimeMap = StreamSupport.stream(storageCloudsRealTime.spliterator(), false)
                    .map(JsonElement::getAsJsonObject)
                    .collect(Collectors.toMap(
                            obj -> getStringFromJson(obj, "id", ""),
                            obj -> getStringFromJson(obj, "nfstype", ""),
                            (existing, replacement) -> existing
                    ));

            // 更新存储云数据
            for (int i = 0; i < storageClouds.size(); i++) {
                JsonObject storage = storageClouds.get(i).getAsJsonObject();
                String id = getStringFromJson(storage, "id", "");
                if (realTimeMap.containsKey(id)) {
                    storage.addProperty("nfstype", realTimeMap.get(id));
                }
            }

            // 处理存储云数据
            for (JsonElement storageCloud : storageClouds) {
                try {
                    StorageData vmData = collectStorageData(platform, storageCloud, headers);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理深信服存储2数据异常, hostMap: {}, error: {}", storageCloud, e.getMessage());
                }
            }
        }

        return dataList;
    }

    private StorageData collectStorageData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String id = getStringFromJson(jsonObject, "id", "");
        StorageData storageData = new StorageData();

        // 判断是否为云存储类型
        boolean isCloudStorage = jsonObject.has("health_status");

        if (isCloudStorage) {
            // 云存储处理逻辑
            String healthStatus = getStringFromJson(jsonObject, "health_status", "");
            boolean isHealthy = "ok".equals(healthStatus);

            // 设置基本信息
            storageData.setName(getStringFromJson(jsonObject, "name", ""))
                    .setUuid(id)
                    .setUrl("not used")
                    .setState(isHealthy ? "Enabled" : "Disabled")
                    .setStatus(isHealthy ? "Connected" : "Disconnected")
                    .setTotalCapacity(getLongFromJson(jsonObject, "total"))
                    .setTotalPhysicalCapacity(getBigFromJson(jsonObject, "total"))
                    .setUsedCapacity(getLongFromJson(jsonObject, "total") - getLongFromJson(jsonObject, "free"))
                    .setAvailableCapacity(getBigFromJson(jsonObject, "free"))
                    .setAvailablePhysicalCapacity(getBigFromJson(jsonObject, "free"))
                    .setCapacityUtilization(getBigFromJson(jsonObject, "disk_num"))
                    .setType("vsnfs".equals(getStringFromJson(jsonObject, "nfstype", "")) ? "虚拟存储" : "其他存储");
        } else {
            // 普通存储处理逻辑
            storageData.setName(getStringFromJson(jsonObject, "name", ""))
                    .setUuid(id)
                    .setUrl("not used")
                    .setState(getIntFromJson(jsonObject,"disable") == 0 ? "Enabled" : "Disabled")
                    .setStatus(getIntFromJson(jsonObject,"active") == 1 ? "Connected" : "Disconnected")
                    .setTotalCapacity(getLongFromJson(jsonObject,"total"))
                    .setTotalPhysicalCapacity(getBigFromJson(jsonObject,"total"))
                    .setUsedCapacity(getLongFromJson(jsonObject,"used"))
                    .setAvailableCapacity(getBigFromJson(jsonObject,"avail"))
                    .setAvailablePhysicalCapacity(getBigFromJson(jsonObject,"avail"))
                    .setCapacityUtilization(getBigFromJson(jsonObject,"used_ratio"))
                    .setType(getStringFromJson(jsonObject,"type",""));
        }

        // 共同设置的属性
        storageData.setPlatformId(platform.getPlatformId())
                .setPlatformName(platform.getPlatformName())
                .setRegionId(platform.getRegionId())
                .setDeleted(0)
                .setTypeName("sangFor")
                .setCreateTime(new Date())
                .setSCreateTime(new Date())
                .setMediaType("机械盘")
                .setManager(platform.getPlatformName())
                .setStoragePercent(BigDecimal.ONE)
                .setRemark(getStringFromJson(jsonObject,"remark",""));

        // 计算虚拟容量
        BigDecimal availableDecimal = storageData.getAvailableCapacity().max(BigDecimal.ZERO);
        storageData.setVirtualCapacity(storageData.getTotalPhysicalCapacity().multiply(storageData.getStoragePercent()))
                .setAllocation(storageData.getTotalPhysicalCapacity().subtract(availableDecimal))
                .setCommitRate(availableDecimal.compareTo(BigDecimal.ZERO) != 0
                        ? storageData.getAllocation().divide(availableDecimal, 2, RoundingMode.HALF_UP)
                        : BigDecimal.ZERO);

        // 设置主机关系列表
        String platformUrl = platform.getPlatformUrl();
        storageData.setStorageHostRelationDataList(
                Optional.ofNullable(getJsonArrayFromApi(platformUrl + SangForApiConstant.GET_VTPSTORAGE_DETAIL.replace("{id}", id), null, headers))
                        .map(hosts -> StreamSupport.stream(hosts.spliterator(), false)
                                .map(host -> new StorageHostRelationData()
                                        .setHardwareUuid(getStringFromJson(host.getAsJsonObject(), "host_id", ""))
                                        .setStorageUuid(id)
                                        .setPlatformId(platform.getPlatformId())
                                        .setPlatformName(platform.getPlatformName()))
                                .collect(Collectors.toList()))
                        .orElse(new ArrayList<>()));

        return storageData;
    }

    public static JsonArray getLocalJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            if (element != null && element.isJsonObject()) {
                JsonArray asJsonArray = element.getAsJsonObject().getAsJsonObject("data").getAsJsonArray("local");
                if (asJsonArray != null && asJsonArray.isJsonArray()) {
                    return asJsonArray;
                }
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }

    public static JsonArray getCloudJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            if (element != null && element.isJsonObject()) {
                JsonArray asJsonArray = element.getAsJsonObject().getAsJsonObject("data").getAsJsonArray("volumes");
                if (asJsonArray != null && asJsonArray.isJsonArray()) {
                    return asJsonArray;
                }
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_SXF_STORAGE.code();
    }
}
