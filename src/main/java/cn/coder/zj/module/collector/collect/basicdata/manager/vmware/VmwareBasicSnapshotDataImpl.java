package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.InventoryNavigator;
import com.vmware.vim25.mo.ManagedEntity;
import com.vmware.vim25.mo.ServiceInstance;
import com.vmware.vim25.mo.VirtualMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.rmi.RemoteException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SNAPSHOT;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_SNAPSHOT;
@Slf4j
public class VmwareBasicSnapshotDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute( () -> {
                try {
                    List<VolumeSnapshotData> snapshotDataList = handleSnapshotData(platform);
                    if (!CollUtil.isEmpty(snapshotDataList)) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(snapshotDataList)
                                .metricsName(BASIC_SNAPSHOT.code())
                                .build();

                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
                }catch (Exception e){
                    log.error("平台 [{}] 快照数据收集失败", platform.getPlatformName(), e);
                    throw new RuntimeException(e);
                }
            });
        }

    }

    private List<VolumeSnapshotData> handleSnapshotData(Platform platform) throws RemoteException {
        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();
        List<Map<String, Object>> vmSnapshots = getVMSnapshots(serviceInstance,platform);
        if (vmSnapshots == null || vmSnapshots.isEmpty()) {
            return null;
        }
        return convertToSnapshotDTOs(vmSnapshots, platform);
    }

    private List<VolumeSnapshotData> convertToSnapshotDTOs(List<Map<String, Object>> vmSnapshots, Platform platform) {
        return vmSnapshots.stream()
                .map(snapshot -> {
                    VolumeSnapshotData dto = new VolumeSnapshotData();
                    dto.setCreateTime(new Date());
                    dto.setUuid(Objects.toString(snapshot.get("uuid"), ""));
                    dto.setName(Objects.toString(snapshot.get("name"), ""));
                    dto.setDescription(Objects.toString(snapshot.get("description"), ""));
                    dto.setVolumeUuid(Objects.toString(snapshot.get("vms"), ""));
                    dto.setInstallPath(Objects.toString(snapshot.get("snapshotDirectory"), ""));
                    dto.setType("主机快照");
                    dto.setLatest("true");
                    dto.setPlatformName(platform.getPlatformName());
                    dto.setPlatformId(platform.getPlatformId());
                    dto.setTypeName("vmware");
                    dto.setStatus("Enabled");
                    Date createTime = DateUtil.parse(snapshot.get("createTime").toString(), "EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
                    dto.setVCreateDate(createTime);;
                    dto.setVUpdateDate(new Date());
                    dto.setFormat("vmsn");
                    dto.setIsMemory(false);
                    dto.setDeleted(0);
                    dto.setHostUuid(Objects.toString(snapshot.get("hostUuid").toString(),""));
                    dto.setHostName(Objects.toString(snapshot.get("hostName").toString(),""));
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public List<Map<String, Object>> getVMSnapshots(ServiceInstance serviceInstance,Platform platform) throws RemoteException {
        try {
            ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder()).searchManagedEntities("VirtualMachine");
            return Arrays.stream(managedEntities)
                    .map(entity -> (VirtualMachine) entity)
                    .map(vm -> {
                        try {
                            List<Map<String, Object>> snapshots = getAllSnapshotInfo(vm);
                            snapshots.forEach(snapshot -> {
                                snapshot.put("hostName", vm.getName());
                                snapshot.put("vms", vm.getMOR().getVal());
                            });
                            return snapshots;
                        } catch (Exception e) {
                            log.error("平台 [{}] 获取主机快照异常: {}",platform.getPlatformName(),vm.getName(), e);
                            return Collections.<Map<String, Object>>emptyList();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("平台 [{}] 获取虚拟机快照列表失败: {}",platform.getPlatformName(),e.getMessage(), e);
            throw new RemoteException("获取虚拟机快照列表失败: " + e.getMessage());
        }
    }

    private List<Map<String, Object>> getAllSnapshotInfo(VirtualMachine vm) throws RemoteException {
        try {
            VirtualMachineSnapshotInfo snapInfo = vm.getSnapshot();
            if (snapInfo == null || snapInfo.getRootSnapshotList() == null) {
                return Collections.emptyList();
            }
            List<Map<String, Object>> allSnapshots = new ArrayList<>();
            Arrays.stream(snapInfo.getRootSnapshotList()).forEach(snapshot -> processSnapshotTree(snapshot, vm, allSnapshots));
            return allSnapshots;
        } catch (Exception e) {
            throw new RemoteException("获取快照信息失败: " + e.getMessage());
        }
    }

    private void processSnapshotTree(VirtualMachineSnapshotTree snapshot, VirtualMachine vm, List<Map<String, Object>> allSnapshots) {
        try {
            Map<String, Object> snapshotInfo = getSnapshotDetails(snapshot, vm);
            VirtualMachineSnapshotTree[] childSnapshots = snapshot.getChildSnapshotList();
            boolean hasChildren = childSnapshots != null && childSnapshots.length > 0;
            snapshotInfo.put("hasChildren", hasChildren);
            snapshotInfo.put("childCount", hasChildren ? childSnapshots.length : 0);
            allSnapshots.add(snapshotInfo);
            if (hasChildren) {
                Arrays.stream(childSnapshots)
                        .forEach(childSnapshot -> processSnapshotTree(childSnapshot, vm, allSnapshots));
            }
        } catch (Exception e) {
            log.error("Failed to process snapshot: {}", snapshot.getName(), e);
        }
    }

    private Map<String, Object> getSnapshotDetails(VirtualMachineSnapshotTree snapshot, VirtualMachine vm) {
        Map<String, Object> snapshotInfo = new HashMap<>();
        // 基本信息
        snapshotInfo.put("name", snapshot.getName());
        snapshotInfo.put("description", snapshot.getDescription());
        snapshotInfo.put("createTime", snapshot.getCreateTime().getTime());
        snapshotInfo.put("id", snapshot.getId());
        snapshotInfo.put("state", snapshot.getState().toString());

        // 快照引用信息
        ManagedObjectReference mor = snapshot.getSnapshot();
        if (mor != null) {
            snapshotInfo.put("uuid", mor.getVal());
        }
        VirtualMachineConfigInfo config = vm.getConfig();
        if (config != null && config.getFiles() != null) {
            snapshotInfo.put("snapshotDirectory", config.getFiles().getSnapshotDirectory());
            snapshotInfo.put("vmPathName", config.getFiles().getVmPathName());
            snapshotInfo.put("logDirectory", config.getFiles().getLogDirectory());
        }
        VirtualMachineSummary virtualMachineSummary = vm.getSummary();
        String hostUuid = vm.getMOR().getVal() + "-" + virtualMachineSummary.getConfig().getUuid();
        snapshotInfo.put("hostUuid", hostUuid);
        snapshotInfo.put("hostName", vm.getName());
        return snapshotInfo;
    }


    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_SNAPSHOT.code();
    }
}
