package cn.coder.zj.module.collector.collect.basicdata.manager.winhong;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VOLUME_INFO;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_WIN_HONG_VOLUME_INFO;

@Slf4j
public class WinHongBasicVolumeInfoDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VolumeInfoData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VOLUME_INFO.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VolumeInfoData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);
        Map<String, String> param = new HashMap<>();
        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_CLOUDS, param, headers).getAsJsonObject().get("data").getAsJsonArray();
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<VolumeInfoData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {

                JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
                String uuid = getStringFromJson(jsonObject, "uuid", "");
                String name = getStringFromJson(jsonObject, "name", "");

                JsonArray diskList = new JsonArray();
                String url = platform.getPlatformUrl() + WinHongApiConstant.GET_DOMAIN_DISK_INFO.replace("{domainId}", uuid);
                JsonElement element = getJsonArrayFromApi(url, null, headers);
                if (element != null && element.isJsonObject()) {
                    JsonObject obj = element.getAsJsonObject();
                    diskList = obj.getAsJsonArray("diskDevices");
                }

                for (int i = 0; i < diskList.size(); i++) {
                    JsonObject disk = diskList.get(i).getAsJsonObject();
                    VolumeInfoData volumeDTO = new VolumeInfoData();
                    volumeDTO.setDescription(getStringFromJson(disk, "remark"));

                    String sourceFile = getStringFromJson(disk, "sourceFile");
                    if (StrUtil.isNotBlank(sourceFile)) {
                        String fileName = sourceFile.contains("/") ?
                                sourceFile.substring(sourceFile.lastIndexOf('/') + 1) :
                                sourceFile;
                        volumeDTO.setName(fileName);
                    }

                    volumeDTO.setFormat(getFormat(Integer.valueOf(disk.get("driverType").getAsString())));
                    Long capacity = !disk.get("capacity").isJsonNull() ? Long.parseLong(disk.get("capacity").getAsString()) : 0;
                    Long allocation = !disk.get("allocation").isJsonNull() ? Long.parseLong(disk.get("allocation").toString()) : 0;
                    volumeDTO.setSize(capacity);
                    volumeDTO.setActualFree(capacity - allocation);
                    volumeDTO.setActualUse(allocation);
                    if (capacity == 0L) {
                        volumeDTO.setActualRatio("0");
                    } else {
                        volumeDTO.setActualRatio(new BigDecimal(allocation).multiply(new BigDecimal(100)).divide(new BigDecimal(capacity), 2, RoundingMode.HALF_UP).toString());
                    }
                    //磁盘类型：file, block, network …
                    if (i == 0) {
                        volumeDTO.setType("Root");
                    } else {
                        volumeDTO.setType("Data");
                    }
                    volumeDTO.setIsMount(true);
                    volumeDTO.setActualSize(allocation);
                    volumeDTO.setState("Enabled");
                    volumeDTO.setUuid(disk.get("volId").isJsonNull() ? "" : disk.get("volId").getAsString());
                    volumeDTO.setStatus("Ready");
                    volumeDTO.setPlatformId(String.valueOf(platform.getPlatformId()));
                    volumeDTO.setPlatformName(platform.getPlatformName());
                    volumeDTO.setVmInstanceUuid(uuid);
                    volumeDTO.setVmInstanceName(name);
                    volumeDTO.setDeleted(0);
                    volumeDTO.setCreateTime(new Date());
                    volumeDTO.setPrimaryStorageUuid(disk.get("storagePoolId").isJsonNull() ? "" : disk.get("storagePoolId").getAsString());
                    volumeDTO.setPrimaryStorageName(disk.get("storagePoolShowName").isJsonNull() ? "" : disk.get("storagePoolShowName").getAsString());
                    volumeDTO.setPrimaryStorageType(getType(disk.get("storagePoolType").isJsonNull() ? "" : disk.get("storagePoolType").getAsString()).toLowerCase());
                    volumeDTO.setMediaType("rotate");
                    dataList.add(volumeDTO);
                }

            }
        }
        return dataList;
    }

    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);

            // 检查响应是否为有效的JSON对象
            if (element != null && element.isJsonObject()) {
                return element;
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }

    public String getFormat(Integer busType) {
        return switch (busType) {
            case 1 -> "智能";
            case 2 -> "高速";
            default -> "其他";
        };
    }
    public String getType(String type) {
        return switch (type) {
            case "1" -> "fc存储";
            case "2" -> "iscsi存储";
            case "3" -> "nfs存储";
            case "4" -> "分布式存储";
            case "5" -> "本地存储";
            case "6" -> "共享存储";
            case "6+1" -> "fc共享存储";
            case "6+2" -> "iscsi 共享存储";
            case "6+6" -> "nvme共享存储";
            default -> "本地存储";
        };
    }

    @Override
    public String supportProtocol() {
        return BASIC_WIN_HONG_VOLUME_INFO.code();
    }
}
