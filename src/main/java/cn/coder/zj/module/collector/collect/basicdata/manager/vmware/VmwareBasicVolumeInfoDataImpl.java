package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.Datastore;
import com.vmware.vim25.mo.ServiceInstance;
import com.vmware.vim25.mo.VirtualMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static cn.coder.zj.module.collector.service.vmware.VmComputerResourceSummary.getVmList;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VOLUME_INFO;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_VOLUME_INFO;

@Slf4j
public class VmwareBasicVolumeInfoDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute( () -> {
                try {
                    List<VolumeInfoData> volumeInfoDataList = handleVolumeInfoData(platform);

                    if (!CollUtil.isEmpty(volumeInfoDataList)) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(volumeInfoDataList)
                                .metricsName(BASIC_VOLUME_INFO.code())
                                .build();

                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private List<VolumeInfoData> handleVolumeInfoData(Platform platform) throws Exception {
        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();
        List<VirtualMachine> virtualMachines = getVmList(serviceInstance);
        if (virtualMachines != null) {
            return processVolumeInfo(virtualMachines, platform);
        }else {
            return new ArrayList<>();
        }
    }

    private List<VolumeInfoData> processVolumeInfo(List<VirtualMachine> virtualMachines, Platform platform) {
        List<VolumeInfoData> allVolumes = new ArrayList<>();
        for (VirtualMachine vm : virtualMachines) {
            try {
                if (vm == null) {
                    continue;
                }
                String uuid = vm.getConfig().getUuid();
                String vms = vm.getMOR().getVal();
                VirtualDevice[] devices = vm.getConfig().getHardware().getDevice();
                String uid = vms + "-" + uuid;
                List<VmDiskFileInfo> diskFileInfos = getVmDiskInfoWithActualSizeVm(vm);
                List<VolumeInfoData> vmVolumes = new ArrayList<>();
                volumeVmwareInfo(uid, platform, vm, devices, diskFileInfos, vmVolumes,vms);
                allVolumes.addAll(vmVolumes);
            } catch (Exception e) {
                log.error("Error processing volume info for VM: " + vm.getName(), e);
            }
        }
        return allVolumes;
    }

    private void volumeVmwareInfo(String uuid, Platform platform, VirtualMachine virtualMachine, VirtualDevice[] devices,
                                  List<VmDiskFileInfo> diskFileInfos, List<VolumeInfoData> vmVolumes,String vms) {
        if (CollUtil.isEmpty(diskFileInfos)) {
            return;
        }

        // 收集虚拟磁盘信息
        List<Map<String, Object>> virtualDisks = Optional.ofNullable(devices)
                .map(devs -> Arrays.stream(devs)
                        .filter(dev -> dev instanceof VirtualDisk)
                        .map(dev -> {
                            VirtualDisk disk = (VirtualDisk) dev;
                            VirtualDeviceBackingInfo backing = disk.getBacking();
                            if (!(backing instanceof VirtualDiskFlatVer2BackingInfo ||
                                    backing instanceof VirtualDiskSparseVer2BackingInfo)) {
                                log.warn("未知的磁盘backing类型: {}", backing.getClass().getName());
                                return null;
                            }

                            Map<String, Object> diskInfo = new HashMap<>(4);
                            diskInfo.put("capacityInKB", disk.getCapacityInKB());
                            diskInfo.put("uuid", backing instanceof VirtualDiskFlatVer2BackingInfo ?
                                    ((VirtualDiskFlatVer2BackingInfo) backing).getUuid() :
                                    ((VirtualDiskSparseVer2BackingInfo) backing).getUuid());
                            diskInfo.put("name", backing instanceof VirtualDiskFlatVer2BackingInfo ?
                                    ((VirtualDiskFlatVer2BackingInfo) backing).getFileName() :
                                    ((VirtualDiskSparseVer2BackingInfo) backing).getFileName());
                            diskInfo.put("datastore", backing instanceof VirtualDiskFlatVer2BackingInfo ?
                                    ((VirtualDiskFlatVer2BackingInfo) backing).getDatastore().getVal() :
                                    ((VirtualDiskSparseVer2BackingInfo) backing).getDatastore().getVal());
                            return diskInfo;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        // 获取数据存储
        Datastore[] datastores = Optional.ofNullable(virtualMachine)
                .map(vm -> {
                    try {
                        return vm.getDatastores();
                    } catch (RemoteException e) {
                        return new Datastore[]{};
                    }
                })
                .orElse(new Datastore[]{});

        // 构建flat文件大小映射
        Map<String, Long> fileSizeMap = diskFileInfos.stream()
                .collect(Collectors.toMap(
                        info -> Convert.toStr(info.getPath()),
                        info -> Convert.toLong(info.getFileSize()),
                        (v1, v2) -> v1 > v2 ? v1 : v2
                ));

        Map<String, Long> flatMap = processDiskSizes(fileSizeMap);

        // 处理磁盘信息
        AtomicBoolean isFirstDisk = new AtomicBoolean(true);
        boolean isMount = StrUtil.isNotEmpty(virtualMachine.getName());
        virtualDisks.forEach(disk -> {
            String name = Convert.toStr(disk.get("name"));
            Long actualSize = flatMap.getOrDefault(name, Convert.toLong(diskFileInfos.stream()
                    .filter(info -> name.equals(Convert.toStr(info.getPath())))
                    .findFirst()
                    .map(VmDiskFileInfo::getFileSize)
                    .orElse(0L)));

            VolumeInfoData volume = createVolumeInfo(platform, virtualMachine, uuid, name, actualSize, isMount);

            boolean isRoot = isFirstDisk.get();
            isFirstDisk.set(false);
            updateVolumeWithDiskInfo(volume, disk, actualSize, datastores, isRoot,vms);

            vmVolumes.add(volume);
        });
    }

    private Map<String, Long> processDiskSizes(Map<String, Long> originalMap) {
        Map<String, Long> processedMap = new HashMap<>();

        originalMap.forEach((fileName, size) -> {
            String baseFileName = fileName;
            // 只匹配 -flat 和 -delta 后缀
            if (fileName.matches(".*(-flat|-delta|-digest|-sesparse)\\.vmdk$")) {
                baseFileName = fileName.replaceFirst("(-flat|-delta|-digest|-sesparse)\\.vmdk$", ".vmdk");
            }
            processedMap.merge(baseFileName, size, Long::sum);
        });

        return processedMap;
    }

    private VolumeInfoData createVolumeInfo(Platform platform, VirtualMachine virtualMachine, String uuid,String name, Long actualSize, boolean isMount) {
        VolumeInfoData volume = new VolumeInfoData();
        volume.setName(name);
        volume.setPlatformId(StrUtil.toString(platform.getPlatformId()));
        volume.setPlatformName(platform.getPlatformName());
        volume.setDescription("vm云盘");
        volume.setVmInstanceUuid(uuid);
        volume.setVmInstanceName(virtualMachine.getName());
        volume.setType("Data");
        volume.setFormat("vmdk");
        volume.setState("Enabled");
        volume.setStatus("Ready");
        volume.setDeleted(0);
        volume.setMediaType("rotate");
        volume.setIsMount(isMount);
        volume.setSize(actualSize);
        volume.setActualSize(actualSize);
        volume.setActualRatio("0");
        volume.setActualFree(0L);
        volume.setActualUse(actualSize);
        return volume;
    }

    private void updateVolumeWithDiskInfo(VolumeInfoData volume, Map<String, Object> diskInfo,Long actualSize, Datastore[] datastores,boolean isRoot,String vms) {
        Long size = Convert.toLong(diskInfo.get("capacityInKB"));
        String volumeUuid = vms + Convert.toStr(diskInfo.get("uuid"));
        BigDecimal capacityInBytes = NumberUtil.mul(Convert.toBigDecimal(size), 1024);
        if(actualSize == 0l){
            actualSize = Convert.toLong(capacityInBytes);
        }
        volume.setType(isRoot ? "Root" : "Data");
        volume.setUuid(volumeUuid);
        volume.setSize(Convert.toLong(capacityInBytes));
        volume.setActualSize(actualSize);
        volume.setActualRatio(Convert.toStr(NumberUtil.div(actualSize, capacityInBytes, 2)));
        volume.setActualFree(NumberUtil.sub(capacityInBytes, actualSize).longValue());
        volume.setActualUse(actualSize);

        for (Datastore dm : datastores) {
            if (dm.getMOR().getVal().equals(diskInfo.get("datastore"))) {
                String storageUuid = (dm.getSummary().getName() + dm.getSummary().getUrl() +
                        dm.getParent().getMOR().getVal()).replaceAll("[\\s\\\\/:*?\"<>|]", "");
                volume.setPrimaryStorageUuid(storageUuid);
                volume.setPrimaryStorageName(dm.getSummary().getName());
                volume.setPrimaryStorageType(dm.getSummary().getType().toLowerCase());
                break;
            }
        }
    }

    public List<VmDiskFileInfo> getVmDiskInfoWithActualSizeVm(VirtualMachine vm) {
        if (vm == null) {
            throw new IllegalArgumentException("VirtualMachine cannot be null");
        }

        List<VmDiskFileInfo> diskInfoList = new ArrayList<>();
        try {
            VirtualMachineFileLayoutEx fileLayoutEx = vm.getLayoutEx();
            if (fileLayoutEx == null) {
                log.warn("VM [{}] 的 FileLayoutEx 为空，无法获取磁盘信息", vm.getName());
                return diskInfoList;
            }

            for (VirtualMachineFileLayoutExFileInfo fileInfo : fileLayoutEx.getFile()) {
                if (fileInfo == null) {
                    continue;
                }
                // 获取vmdk 文件
                if (fileInfo.getName().contains(".vmdk")) {
                    VmDiskFileInfo diskInfo = new VmDiskFileInfo();
                    diskInfo.setPath(fileInfo.getName());
                    diskInfo.setFileSize(fileInfo.getSize());
                    diskInfoList.add(diskInfo);
                }
            }
        } catch (Exception e) {
            log.error("获取VM [{}] 磁盘信息时发生错误", vm.getName(), e);
            throw new RuntimeException("Failed to get VM disk info: " + e.getMessage(), e);
        }

        return diskInfoList;
    }

    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_VOLUME_INFO.code();
    }
}
