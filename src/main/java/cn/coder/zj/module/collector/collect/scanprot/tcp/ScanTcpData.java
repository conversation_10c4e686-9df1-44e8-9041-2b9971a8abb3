package cn.coder.zj.module.collector.collect.scanprot.tcp;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.coder.zj.module.collector.util.NetworkUtil.*;

@Slf4j
public class ScanTcpData {

    public static List<ScanIPData> checkTcpPorts(List<ScanIPData> endpoints) {
        if (endpoints == null || endpoints.isEmpty()) {
            return Collections.emptyList();
        }

        List<List<ScanIPData>> batches = splitIntoBatches(endpoints, 20);
        int totalSize = endpoints.size();
        Long ipRangeId = endpoints.get(0).getIpRangeId();

        AtomicInteger processedCount = new AtomicInteger(0);
        for (List<ScanIPData> batch : batches) {
            // 第一轮SNMP检测
            batch.parallelStream().forEach(endpoint -> {
                endpoint.setTcpStatus(checkEndpointTcp(endpoint) ? 1 : 2);
            });

            // 对当前批次中失败的IP进行重试
            batch.parallelStream()
                    .filter(endpoint -> endpoint.getTcpStatus() == 2)
                    .forEach(endpoint -> {
                        endpoint.setTcpStatus(checkEndpointTcp(endpoint) ? 1 : 2);
                    });

            // 更新进度
            processedCount.addAndGet(batch.size());

            sendMessage(ipRangeId, totalSize, processedCount.get(),"tcp");
        }

        return endpoints;
    }

    private static boolean checkEndpointTcp(ScanIPData endpoint) {
        boolean isReachable = isTcpPortOpen(endpoint.getIpAddress(), endpoint.getTcpPort());
        endpoint.setTcpStatus(isReachable ? 1 : 2);
        return isReachable;
    }
}