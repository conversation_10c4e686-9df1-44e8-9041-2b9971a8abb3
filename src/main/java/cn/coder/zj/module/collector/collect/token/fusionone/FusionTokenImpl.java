package cn.coder.zj.module.collector.collect.token.fusionone;

import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.fusionone.FsOnePlatform;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;

import static cn.coder.zj.module.collector.enums.PlatformType.FUSION_ONE;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class FusionTokenImpl extends AbstractToken {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void token(Platform platform) {
        String host = platform.getPlatformUrl().replace("http://", "").replace("https://", "");
        HashMap<String, String> headers = new HashMap<>();
        headers.put("X-Auth-User", platform.getUsername());
        headers.put("X-Auth-Key", hash(platform.getPassword()));
        headers.put("X-Auth-UserType", "2");
        headers.put("X-Auth-AuthType", "0");
        headers.put("Host", host);
        headers.put("Accept", "application/json;version=8.0;charset=UTF-8");
        headers.put("Content-Type", "application/json; charset=UTF-8");
        headers.put("Accept-Language", "zh_CN");

        try {
            OkHttpService.postSyncHead(platform.getPlatformUrl() + FusionOneApiConstant.LOGIN, null, headers, new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    log.error("登录请求失败: {}", e.getMessage());
                    updatePlatformStatus(platform, false);
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (!response.isSuccessful() || response.body() == null) {
                        updatePlatformStatus(platform, false);
                        return;
                    }

                    try {
                        String token = response.headers().get("X-Auth-Token");
                        headers.put("X-Auth-Token", token);

                        Response siteResponse = OkHttpService.getSync(platform.getPlatformUrl() + FusionOneApiConstant.SITE, null, headers);
                        if (siteResponse.isSuccessful() && siteResponse.body() != null) {
                            JsonObject siteInfo = GSON.fromJson(siteResponse.body().string(), JsonElement.class).getAsJsonObject();
                            JsonObject siteObj = siteInfo.getAsJsonArray("sites").get(0).getAsJsonObject();

                            platform.setFsOnePlatform(FsOnePlatform.builder()
                                    .token(token)
                                    .siteId(siteObj.get("urn").getAsString().split("urn:sites:")[1])
                                    .siteName(siteObj.get("name").getAsString())
                                    .host(host)
                                    .build());

                            CacheService.put(FUSION_ONE.code(), Collections.singletonMap(platform.getPlatformId().toString(), platform));
                            log.info("Fusionone登录成功，token已缓存");
                            updatePlatformStatus(platform, true);
                        }

                        if (siteResponse.body() != null) {
                            siteResponse.body().close();
                        }
                    } catch (Exception e) {
                        log.error("获取站点数据失败: {}", e.getMessage());
                        updatePlatformStatus(platform, false);
                    } finally {
                        if (response.body() != null) {
                            response.body().close();
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("FS登录数据异常: {}", e.getMessage());
            updatePlatformStatus(platform, false);
        }
    }

    /**
     * 发送平台异常信息
     */
    private void updatePlatformStatus(Platform platform, boolean isOnline) {
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        if (isOnline) {
            platform.setState(0L);
        } else {
            platform.setState(1L);
        }
        platform.setDateTime(new Date());
        sendMessageService.sendMessage(CacheService.getCtx("ctx"), ClusterMsg.Message.newBuilder().setData(GsonUtil.GSON.toJson(platform)).setType(ClusterMsg.MessageType.DETECT).setTime(System.currentTimeMillis()).build());
    }

    public String hash(String input) {
        try {
            // 创建 SHA-256 的 MessageDigest 实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            // 进行哈希计算
            byte[] hashBytes = digest.digest(input.getBytes());
            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not found", e);
        }
    }

    @Override
    public String supportProtocol() {
        return FUSION_ONE.code();
    }
}
