package cn.coder.zj.module.collector.collect.basicdata.manager.winhong;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static cn.coder.zj.module.collector.util.CommonUtil.getBigFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_WIN_HONG_HOST;

@Slf4j
public class WinHongBasicHostDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<HostData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_HOST.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<HostData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);
        Map<String, String> param = new HashMap<>();
        param.put("needClusterName", "true");
        param.put("needHostRate", "true");
        param.put("needPoolName", "true");
        param.put("needStorageRate", "true");
        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HARDWARE_LIST, param, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<HostData> dataList = new ArrayList<>();
        List<Map> hostList = GSON.fromJson(hostArray, new TypeToken<List<Map>>(){}.getType());
        if (CollUtil.isNotEmpty(hostList)) {
            for (Map hostMap : hostList) {
                try {
                    HostData hostData = collectHostInfo(platform, hostMap, token);
                    if (hostData != null) {
                        dataList.add(hostData);
                    }
                } catch (Exception e) {
                    log.error("处理主机数据异常, hostMap: {}, error: {}", hostMap, e.getMessage());
                }
            }
        }
        return dataList;
    }

    private HostData collectHostInfo(Platform platform, Map map, String token) {
        try {
            // 提取基本信息
            String uuid = Convert.toStr(map.get("id"));
            String platformUrl = platform.getPlatformUrl();
            Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);
            Map<String, String> param = Collections.singletonMap("hostId", uuid);

            // 获取各种API数据
            JsonObject ratioObj = getJsonObjectFromApi(platformUrl + WinHongApiConstant.WIN_HONG_API_PREFIX
                    + WinHongApiConstant.COMPUTE + "/hosts/" + uuid + "/resource/ratio", null, headers);
            JsonObject memoryObj = getJsonObjectFromApi(platformUrl
                    + WinHongApiConstant.GET_RESERVE_MEMORY.replace("{hostId}", uuid), null, headers);
            JsonArray diskInfo = getJsonArrayFromApi(platformUrl + WinHongApiConstant.GET_DISKS_INFO, param, headers);
            JsonArray phynicArray = getJsonArrayFromApi(platformUrl + WinHongApiConstant.WIN_HONG_API_PREFIX
                    + WinHongApiConstant.COMPUTE + "/hosts/" + uuid + "/phynic", param, headers);
            JsonObject summaryHost = getJsonObjectFromApi(platformUrl
                    + WinHongApiConstant.GET_SUMMARY_HOST.replace("{domainId}", uuid), null, headers);

            // 计算CPU相关数据
            Integer cpuNum = Convert.toInt(map.get("cpuCores"));
            BigDecimal availableCpuCapacity = Convert.toBigDecimal(map.get("cpuCores"));
            BigDecimal cpuCommitRate = getBigFromJson(ratioObj, "vmCpuTotal");
            BigDecimal totalCpuCapacity = new BigDecimal(cpuNum);

            // 计算内存相关数据
            Long totalMemoryCapacity = Convert.toLong(map.get("memory"));
            Long memoryUsage = Optional.ofNullable(map.get("memoryUsage")).map(Convert::toLong).orElse(0L);
            BigDecimal availableMemoryCapacity = NumberUtil.sub(totalMemoryCapacity, memoryUsage);
            BigDecimal memCommitRate = getBigFromJson(ratioObj, "vmMemTotal");

            // 计算磁盘相关数据
            List<Map> diskInfoList = GSON.fromJson(diskInfo, new TypeToken<List<Map>>(){}.getType());
            BigDecimal[] diskCapacities = calculateDiskCapacities(diskInfoList);

            // 计算网络带宽数据
            BigDecimal[] bandwidths = calculateBandwidths(phynicArray, platform, uuid, token, headers);

            // 构建并返回主机数据对象
            return buildHostData(map, platform, uuid, cpuNum, availableCpuCapacity, totalCpuCapacity, cpuCommitRate,
                    totalMemoryCapacity, availableMemoryCapacity, memCommitRate, diskCapacities, bandwidths,
                    memoryObj.get("reserveMemorySize").getAsBigDecimal(), summaryHost.get("hostModel").getAsString());
        } catch (Exception e) {
            log.error("获取主机详细信息异常, uuid: {}, error: {}", map.get("id"), e.getMessage());
            return null;
        }
    }

    private BigDecimal[] calculateDiskCapacities(List<Map> diskInfoList) {
        if (CollUtil.isEmpty(diskInfoList)) {
            return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        }

        BigDecimal totalDiskCapacity = BigDecimal.ZERO;
        BigDecimal diskUsedBytes = BigDecimal.ZERO;

        for (Map diskMap : diskInfoList) {
            totalDiskCapacity = totalDiskCapacity.add(Convert.toBigDecimal(diskMap.get("total")));
            diskUsedBytes = diskUsedBytes.add(Convert.toBigDecimal(diskMap.get("used")));
        }

        BigDecimal totalScaled = totalDiskCapacity.setScale(0, RoundingMode.DOWN);
        BigDecimal usedScaled = diskUsedBytes.setScale(0, RoundingMode.DOWN);
        BigDecimal diskFreeBytes = NumberUtil.sub(totalScaled, usedScaled);

        return new BigDecimal[]{
                NumberUtil.mul(totalScaled, 1024, 1024, 1024),
                NumberUtil.mul(usedScaled, 1024, 1024, 1024),
                NumberUtil.mul(diskFreeBytes, 1024, 1024, 1024)
        };
    }

    private BigDecimal[] calculateBandwidths(JsonArray phynicArray, Platform platform, String uuid,
                                             String token, Map<String, String> headers) {
        if (ObjectUtil.isNull(phynicArray) || phynicArray.size() == 0) {
            return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO};
        }

        List<Map> phynicList = GSON.fromJson(phynicArray, new TypeToken<List<Map>>(){}.getType());
        if (CollUtil.isEmpty(phynicList)) {
            return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO};
        }

        BigDecimal bandwidthUpstream = BigDecimal.ZERO;
        BigDecimal bandwidthDownstream = BigDecimal.ZERO;

        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);

        for (Map phynicMap : phynicList) {
            try {
                Map<String, String> flowParams = new HashMap<>(5);
                flowParams.put("hostIds", uuid);
                flowParams.put("token", token);
                flowParams.put("mac", Convert.toStr(phynicMap.get("mac")));
                flowParams.put("endTime", endTime);
                flowParams.put("startTime", startTime);

                JsonArray flowInfo = getRootJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_FLOW_INFO,
                        flowParams, headers);
                for (JsonElement item : flowInfo) {
                    JsonArray netitemJson = item.getAsJsonObject().get("networkFlowRspList").getAsJsonArray();
                    for (JsonElement element : netitemJson) {
                        JsonObject itemJson = element.getAsJsonObject();
                        JsonArray outputJson = itemJson.getAsJsonObject("output").getAsJsonArray("data");
                        JsonArray inputJson = itemJson.getAsJsonObject("input").getAsJsonArray("data");

                        if (!outputJson.isEmpty()) {
                            bandwidthUpstream = bandwidthUpstream.add(outputJson.get(0).getAsJsonObject().get("value").getAsBigDecimal());
                        }
                        if (!inputJson.isEmpty()) {
                            bandwidthDownstream = bandwidthDownstream.add(inputJson.get(0).getAsJsonObject().get("value").getAsBigDecimal());
                        }
                    }

                }


            } catch (Exception e) {
                log.error("获取网络流量信息异常, mac: {}, error: {}", phynicMap.get("mac"), e.getMessage());
            }
        }

        return new BigDecimal[]{bandwidthUpstream, bandwidthDownstream};
    }

    private HostData buildHostData(Map map, Platform platform, String uuid, Integer cpuNum,
                                   BigDecimal availableCpuCapacity, BigDecimal totalCpuCapacity, BigDecimal cpuCommitRate,
                                   Long totalMemoryCapacity, BigDecimal availableMemoryCapacity, BigDecimal memCommitRate,
                                   BigDecimal[] diskCapacities, BigDecimal[] bandwidths, BigDecimal reserveMemory, String model) {

        HostData hostData = new HostData();

        // 设置基本信息
        hostData.setUuid(uuid);
        hostData.setName(Convert.toStr(map.get("hostname")));
        hostData.setIp(Convert.toStr(map.get("ip")));
        hostData.setClusterUuid(Convert.toStr(map.get("clusterId")));
        hostData.setClusterName(Convert.toStr(map.get("clusterName")));
        hostData.setAvailableManager(Convert.toStr(map.get("poolName")));
        hostData.setRegionId(platform.getRegionId());

        // 设置CPU信息
        hostData.setArchitecture(Convert.toStr(map.get("cpuArchitecture")));
        hostData.setCpuSockets(Convert.toInt(map.get("cpuSockets")));
        hostData.setCpuNum(cpuNum);
        hostData.setCpuUsed(Optional.ofNullable(map.get("cpuRate")).map(Convert::toBigDecimal).orElse(new BigDecimal(0)));
        hostData.setTotalCpuCapacity(Convert.toLong(totalCpuCapacity));
        hostData.setAvailableCpuCapacity(Convert.toLong(availableCpuCapacity));
        hostData.setCpuCommitRate(cpuCommitRate);
        hostData.setCpuOverPercent(new BigDecimal(1));
        hostData.setCpuType(map.get("cpuModelName").toString());

        // 设置内存信息
        hostData.setTotalMemoryCapacity(totalMemoryCapacity);
        hostData.setAvailableMemoryCapacity(Convert.toLong(availableMemoryCapacity));
        hostData.setMemoryUsed(Optional.ofNullable(map.get("memoryRate")).map(Convert::toBigDecimal).orElse(new BigDecimal(0)));
        hostData.setMemoryCommitRate(memCommitRate);
        hostData.setTotalVirtualMemory(new BigDecimal(totalMemoryCapacity));
        hostData.setMemoryOverPercent(new BigDecimal(1));
        hostData.setReservedMemory(reserveMemory);

        // 设置磁盘信息
        hostData.setTotalDiskCapacity(diskCapacities[0]);
        hostData.setDiskUsedBytes(diskCapacities[1]);
        hostData.setDiskFreeBytes(diskCapacities[2]);
        hostData.setDiskUsed(Optional.ofNullable(map.get("storageRate")).map(Convert::toBigDecimal).orElse(new BigDecimal(0)));

        // 设置网络信息
        hostData.setBandwidthUpstream(bandwidths[0]);
        hostData.setBandwidthDownstream(bandwidths[1]);
        hostData.setPacketRate(new BigDecimal(0));

        // 设置状态信息
        boolean isConnected = !Convert.toBool("isConnected");
        hostData.setStatus(isConnected ? "Connected" : "Disconnected");
        hostData.setState((Boolean) map.get("isMaintain") ? "Maintenance" : (isConnected ? "Enabled" : "Disabled"));

        // 设置平台信息
        hostData.setPlatformId(platform.getPlatformId());
        hostData.setPlatformName(platform.getPlatformName());
        hostData.setManager(platform.getPlatformName());
        hostData.setTypeName("winhong");
        hostData.setDeleted(0);
        hostData.setManufacturer("-");
        hostData.setVms("");
        hostData.setSerialNumber(map.get("serialNumber").toString());
        hostData.setBrandName("WinHong");
        hostData.setModel(model);
        hostData.setIsMaintain(Convert.toBool(map.get("isMaintain")) ? 1 : 0);

        return hostData;
    }

    private JsonArray getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            return GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("data").getAsJsonArray();
        } catch (IOException e) {
            log.error("error collecting basic data: {}", e.getMessage());
            return new JsonArray();
        }
    }

    private JsonArray getRootJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            return GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonArray();
        } catch (IOException e) {
            log.error("error collecting basic data: {}", e.getMessage());
            return new JsonArray();
        }
    }

    private JsonObject getJsonObjectFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            return GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
        } catch (IOException e) {
            log.error("error collecting basic data: {}", e.getMessage());
            return new JsonObject();
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_WIN_HONG_HOST.code();
    }


}
