package cn.coder.zj.module.collector.remoting.netty.handler;

import cn.coder.zj.module.collector.remoting.netty.NettyClient;
import cn.coder.zj.module.collector.remoting.netty.config.CollectorConfig;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.EventLoop;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 心跳检测
 * <AUTHOR>
 **/
@Slf4j
public class HeartHandler extends SimpleChannelInboundHandler<ClusterMsg.Message> {

    private final NettyClient nettyClient;

    private CollectorConfig collectorConfig;

    public HeartHandler(NettyClient nettyClient,CollectorConfig collectorConfig) {
        this.nettyClient = nettyClient;
        this.collectorConfig = collectorConfig;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, ClusterMsg.Message msg) {
        ctx.fireChannelRead(msg);
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent idleStateEvent) {
            IdleState state = idleStateEvent.state();
            if (state == IdleState.WRITER_IDLE) {
                log.info("didn't receive any data from the server for 10 seconds.");
                ClusterMsg.Message msg = ClusterMsg.Message.newBuilder()
                        .setClientId(ctx.channel().id().asLongText())
                        .setType(ClusterMsg.MessageType.HEART)
                        .setData("heartbeat test...").build();
                ctx.writeAndFlush(msg);
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }

    /**
     * 处理断开重连(10s/次)
     */
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.info("heartbeat server disconnection detected...");
        final EventLoop eventLoop = ctx.channel().eventLoop();
        Map<Integer, String> serverInfo = collectorConfig.getServerInfo();
        for (Map.Entry<Integer, String> entry : serverInfo.entrySet()) {
            eventLoop.schedule(() -> nettyClient.doConnect(new Bootstrap(),
                            eventLoop,entry.getValue(),
                            entry.getKey()),
                    10L, TimeUnit.SECONDS);
            super.channelInactive(ctx);
        }
    }
}
