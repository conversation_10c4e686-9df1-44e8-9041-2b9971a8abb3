package cn.coder.zj.module.collector.collect.basicdata.manager.sangfor;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL2Data;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL3Data;
import cn.iocoder.zj.framework.common.enums.MetricsType;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.util.*;
import java.util.stream.Collectors;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.ApiUtil.getNologJsonObjectFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getIntFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_NET;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_SXF_NET;

@Slf4j
public class SangForBasicNetDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<NetWorkL2Data> listL2 = new ArrayList<>();
                List<NetWorkL3Data> listL3 = new ArrayList<>();
                List<Map<String, List<?>>> maps = collectData(platformObj);

                Optional.ofNullable(maps)
                        .filter(m -> !m.isEmpty())
                        .ifPresent(mapList -> mapList.forEach(map -> {
                            Optional.ofNullable(map.get("NetWorkL2"))
                                    .map(list -> ((List<?>) list).stream()
                                            .filter(NetWorkL2Data.class::isInstance)
                                            .map(NetWorkL2Data.class::cast)
                                            .collect(Collectors.toList()))
                                    .ifPresent(listL2::addAll);

                            Optional.ofNullable(map.get("NetWorkL3"))
                                    .map(list -> ((List<?>) list).stream()
                                            .filter(NetWorkL3Data.class::isInstance)
                                            .map(NetWorkL3Data.class::cast)
                                            .collect(Collectors.toList()))
                                    .ifPresent(listL3::addAll);
                        }));

                //l2网卡
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(listL2)
                        .metricsName(BASIC_NET.code())
                        .metricsType(MetricsType.BASIC_NET_L2.code())
                        .build()));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                //l3网卡
                ChannelHandlerContext ctx = CacheService.getCtx("ctx");
                ClusterMsg.Message.Builder messageL3 = ClusterMsg.Message.newBuilder().setType(ClusterMsg.MessageType.BASIC)
                        .setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(listL3)
                                .metricsName(BASIC_NET.code())
                                .metricsType(MetricsType.BASIC_NET_L3.code())
                                .build()));
                messageL3.setTime(System.currentTimeMillis());
                ctx.writeAndFlush(messageL3);
            });

            String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
            log.info("collect basic data end, cost {} seconds", endTimeFormatted);
        }
    }

    private List<Map<String, List<?>>> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie,"CSRFPreventionToken",token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_HARDWARE, null, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<Map<String, List<?>>> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    Map<String, List<?>> vmData = collectHostNetData(platform, jsonElement,headers);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理深信服物理网络数据异常, hostMap: {}, error: {}", jsonElement.toString(), e.getMessage());
                }
            }
        }
        return dataList;
    }

    private  Map<String, List<?>> collectHostNetData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        Map<String, List<?>> listMap = new HashMap<>();
        JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String hostId = getStringFromJson(jsonObject, "id", "");
        String platformUrl = platform.getPlatformUrl();

        List<NetWorkL2Data> netWorkL2 = new ArrayList<>();
        List<NetWorkL3Data> netWorkL3 = new ArrayList<>();
        // 获取网络信息
        Map<String, JsonArray> networkInfoArray = getNetworksInfo(
                platformUrl + SangForApiConstant.GET_V_NETWORK_LIST,
                platformUrl + SangForApiConstant.GET_NETWORK_INFO,
                null, headers);

        // 获取物理网卡信息
        JsonArray nicArray = Optional.ofNullable(getJsonArrayFromApi(platformUrl + SangForApiConstant.GET_IFACES, null, headers))
                .map(nicInfoArray -> nicInfoArray.asList().stream()
                        .map(JsonElement::getAsJsonObject)
                        .filter(nicInfo -> hostId.equals(getStringFromJson(nicInfo, "node_id", "")))
                        .findFirst()
                        .map(nicInfo -> nicInfo.getAsJsonArray("data"))
                        .orElse(new JsonArray()))
                .orElse(new JsonArray());

        // 处理物理网卡数据
        nicArray.forEach(element -> {
            JsonObject nic = element.getAsJsonObject();
            String ip = getStringFromJson(nic, "ip", "");
            if (StrUtil.isNotEmpty(ip)) {
                String id = getStringFromJson(nic, "id", "");
                String mac = getStringFromJson(nic, "mac", "");

                NetWorkL2Data netWorkInfo = new NetWorkL2Data();
                netWorkInfo.setName(getStringFromJson(nic, "name", ""));
                netWorkInfo.setUuid(id);
                netWorkInfo.setVlan(getStringFromJson(nic, "vlan_id", ""));
                netWorkInfo.setPlatformId(platform.getPlatformId());
                netWorkInfo.setPlatformName(platform.getPlatformName());
                netWorkInfo.setRegionId(platform.getRegionId());
                netWorkInfo.setPhysicalInterface(getStringFromJson(nic, "type", ""));
                if(StrUtil.isNotEmpty(getStringFromJson(nic, "vlan_id", ""))){
                    netWorkInfo.setType("VLAN");
                }else {
                    netWorkInfo.setType("FLAT");
                }
                netWorkInfo.setTypeName("sangFor");
                netWorkL2.add(netWorkInfo);

                NetWorkL3Data netWorkL3DTO = new NetWorkL3Data();
                netWorkL3DTO.setUuid(id + ip + mac);
                netWorkL3DTO.setType("L3BasicNetwork");
                netWorkL3DTO.setNetworkSegment(ip + "-" + getStringFromJson(nic, "netmask", ""));
                netWorkL3DTO.setNetmask(getStringFromJson(nic, "netmask", ""));
                netWorkL3DTO.setGateway(getStringFromJson(nic, "gateway_ip", ""));
                netWorkInfo.setPlatformId(platform.getPlatformId());
                netWorkInfo.setPlatformName(platform.getPlatformName());
                netWorkL3DTO.setL2NetworkUuid(id);
                netWorkL3DTO.setL2NetworkName(getStringFromJson(nic, "name", ""));
                netWorkL3DTO.setTypeName("sangFor");
                netWorkL3.add(netWorkL3DTO);
            }
        });

        // 处理VLAN网卡数据
        Optional.ofNullable(networkInfoArray)
                .map(info -> info.get("vlanGroup"))
                .ifPresent(vlanArray -> vlanArray.forEach(element -> {
                    String id = getStringFromJson(element.getAsJsonObject(), "id", "");
                    NetWorkL2Data netWorkL = new NetWorkL2Data();
                    netWorkL.setName(getStringFromJson(element.getAsJsonObject(), "name", ""));
                    netWorkL.setUuid(id);
                    netWorkL.setPlatformId(platform.getPlatformId());
                    netWorkL.setPlatformName(platform.getPlatformName());
                    netWorkL.setRegionId(platform.getRegionId());
                    netWorkL.setPhysicalInterface(getStringFromJson(element.getAsJsonObject(), "type", ""));
                    if(StrUtil.isNotEmpty(getStringFromJson(element.getAsJsonObject(), "vlan_id", ""))){
                        netWorkL.setType("VLAN");
                    }else {
                        netWorkL.setType("FLAT");
                    }
                    netWorkL.setTypeName("sangFor");
                    netWorkL2.add(netWorkL);
                }));

        listMap.put("NetWorkL2",netWorkL2);
        listMap.put("NetWorkL3",netWorkL3);
        return listMap;
    }

    public Map<String, JsonArray> getNetworksInfo(String url, String urlInfo, Map<String, String> params, Map<String, String> headers) {
        JsonArray bridgeList = new JsonArray();
        JsonArray vlanGroup = new JsonArray();

        try (Response response = OkHttpService.getSync(url, params, headers)) {
            JsonObject root = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
            Optional.ofNullable(root.getAsJsonArray("data"))
                    .ifPresent(dataArray -> dataArray.forEach(datum ->
                            Optional.ofNullable(datum.getAsJsonObject().getAsJsonArray("data"))
                                    .ifPresent(netArray -> netArray.forEach(net -> {
                                        String networkId = getStringFromJson(net.getAsJsonObject(), "id", "");
                                        JsonObject detailRes = getNologJsonObjectFromApi(urlInfo + networkId, null, headers);

                                        if (detailRes.size() > 0) {
                                            Optional.ofNullable(detailRes.getAsJsonArray("bridgeList"))
                                                    .ifPresent(bridgeList::addAll);
                                            Optional.ofNullable(detailRes.getAsJsonArray("vlanGroup"))
                                                    .ifPresent(vlanGroup::addAll);
                                        }
                                    }))
                    ));
        } catch (Exception e) {
            log.error("获取网络信息失败, URL: {}, 错误: {}", url, e.getMessage());
        }

        return Map.of("bridgeList", bridgeList, "vlanGroup", vlanGroup);
    }

    @Override
    public String supportProtocol() {
        return BASIC_SXF_NET.code();
    }
}
