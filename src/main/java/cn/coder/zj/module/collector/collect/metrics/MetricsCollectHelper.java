package cn.coder.zj.module.collector.collect.metrics;

import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.service.apicache.FsPreApiCacheService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;

/**
 * 指标收集辅助工具类
 * 平台的通用数据收集方法
 * 
 * <AUTHOR>
 */
@Slf4j
public class MetricsCollectHelper {

    /**
     * 收集FusionOne平台虚拟机数据
     */
    public static List<MetricData> collectVmData(Platform platform, String errorType, MetricsHandler handler) {
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_CLOUD_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("vms");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<MetricData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    List<MetricData> vmData = handler.handleVmMetrics(platform, jsonElement, header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理Fusionone-{}虚拟机数据异常, hostMap: {}, error: {}", errorType, "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    /**
     * 收集FusionOne平台物理机数据
     */
    public static List<MetricData> collectHostData(Platform platform, String errorType, MetricsHandler handler) {
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_HOST_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject hostdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray hostArray = hostdata.getAsJsonArray("hosts");
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<MetricData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    List<MetricData> vmData = handler.handleHostMetrics(platform, jsonElement, header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理Fusionone-{}物理机数据异常, hostMap: {}, error: {}", errorType, "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    /**
     * 收集InSpur平台虚拟机数据
     */
    public static List<MetricData> collectInSpurVmData(Platform platform, String errorType, MetricsHandler handler) {
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + InSpurApiConstant.GET_CLOUD_LIST;
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization",token
        );
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("items");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<MetricData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    List<MetricData> vmData = handler.handleVmMetrics(platform, jsonElement, header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("浪潮-{}虚拟机数据异常, hostMap: {}, error: {}", errorType, "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    /**
     * 收集InSpur平台物理机数据
     */
    public static List<MetricData> collectInSpurHostData(Platform platform, String errorType, MetricsHandler handler) {
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String hostUrl = platform.getPlatformUrl() + InSpurApiConstant.GET_HOST_LIST;
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization",token
        );
        JsonObject hostdata = FsApiCacheService.getJsonObject(hostUrl, null, header);
        JsonArray hostArray = hostdata.getAsJsonArray("items");
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<MetricData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    List<MetricData> vmData = handler.handleHostMetrics(platform, jsonElement, header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("浪潮-{}物流机数据异常, hostMap: {}, error: {}", errorType, "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    /**
     * 获取FusionOne平台指标值数组
     */
    public static JsonArray getMetricsValueArray(Platform platform, JsonObject cloudInfo, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        String siteId = platform.getFsOnePlatform().getSiteId();

        JsonObject requestBody = new JsonObject();
        JsonArray metricArray = new JsonArray();
        metricArray.add("cpu_usage");
        metricArray.add("mem_usage");
        metricArray.add("disk_usage");
        metricArray.add("disk_req_in");
        metricArray.add("disk_req_out");
        metricArray.add("disk_io_in");
        metricArray.add("disk_io_out");
        metricArray.add("nic_byte_in");
        metricArray.add("nic_byte_out");
        requestBody.add("metricId", metricArray);
        requestBody.addProperty("urn", getStringFromJson(cloudInfo, "urn"));
        JsonArray requestArray = new JsonArray();
        requestArray.add(requestBody);

        return FsPreApiCacheService.getJsonArray(
                platformUrl + FusionOneApiConstant.GET_REAL_TIME_DATA.replace("{siteId}", siteId),
                headers,
                requestArray
        );
    }

    /**
     * 创建基础指标数据对象（FusionOne）
     */
    public static MetricData createBaseMetricData(Platform platform, JsonObject cloud, String metricName,String type) {
        MetricData metricData = new MetricData();
        metricData.setPlatformId(platform.getPlatformId());
        metricData.setMetricName(metricName);
        metricData.setResourceId(getStringFromJson(cloud,"uuid"));
        metricData.setResourceName(getStringFromJson(cloud,"name"));
        metricData.setTimestamps(Arrays.asList(System.currentTimeMillis()));
        metricData.setType(type);
        return metricData;
    }

    /**
     * 创建指标数据对象（InSpur）
     */
    public static MetricData createData(Platform platform, JsonObject cloud, String metricName,String type) {
        MetricData metricData = new MetricData();
        metricData.setPlatformId(platform.getPlatformId());
        metricData.setMetricName(metricName);
        metricData.setResourceId(getStringFromJson(cloud,"id"));
        metricData.setResourceName(getStringFromJson(cloud,"name"));
        metricData.setTimestamps(Arrays.asList(System.currentTimeMillis()));
        metricData.setType(type);
        return metricData;
    }

    public interface MetricsHandler {
        List<MetricData> handleVmMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers);
        List<MetricData> handleHostMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers);
    }
} 