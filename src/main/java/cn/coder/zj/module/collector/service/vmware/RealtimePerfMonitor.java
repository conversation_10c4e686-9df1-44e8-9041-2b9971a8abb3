package cn.coder.zj.module.collector.service.vmware;

import cn.coder.zj.module.collector.dal.manager.PerMonitorDO;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.convert.Convert;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.ManagedEntity;
import com.vmware.vim25.mo.PerformanceManager;
import com.vmware.vim25.mo.ServiceInstance;
import lombok.extern.slf4j.Slf4j;

import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
public class RealtimePerfMonitor {

    // 优化缓存结构，使用更高效的缓存机制
    private static final Map<String, Integer> counterCache = new ConcurrentHashMap<>();
    private static final Map<String, Map<String, Integer>> perCounterList = new ConcurrentHashMap<>();
    // 添加超时缓存，避免频繁查询相同的计数器
    private static final long CACHE_EXPIRY_TIME = 3600000; // 缓存过期时间：1小时
    private static final Map<String, Long> cacheTimestamps = new ConcurrentHashMap<>();

    /**
     * {@code @description:} 根据指定id获取性能指标数据
     * <AUTHOR>
     * {@code @date} 2024/7/25 10:55
     */
    public static List<PerMonitorDO> getPerEntityMetricBasesByName(String name, ServiceInstance si, ManagedEntity vm, String counterName, Platform platform) throws Exception {
        long startTime = System.currentTimeMillis();

        List<PerMonitorDO> list = new ArrayList<>();

        if (vm == null) {
            System.out.println("Virtual Machine " + name + " cannot be found.");
            si.getServerConnection().logout();
            return list;
        }

        PerformanceManager perfMgr = si.getPerformanceManager();
        PerfProviderSummary pps = perfMgr.queryPerfProviderSummary(vm);
        int refreshInterval = pps.getRefreshRate();

        Integer counterId = 0;

        Map<String, Integer> platformMap = perCounterList.get(StringUtil.toString(platform.getPlatformId()));

        if (platformMap == null) {
            PerfCounterInfo[] d = perfMgr.getPerfCounter();
            Map<String, Integer> map = new HashMap<>();
            for (PerfCounterInfo counter : d) {
                String fullName = counter.getGroupInfo().getKey() + "." + counter.getNameInfo().getKey() + "." + counter.getRollupType();
                map.put(fullName, counter.getKey());
                if (counterName.equals(fullName)) {
                    counterId = counter.getKey();
                }
            }
            perCounterList.put(StringUtil.toString(platform.getPlatformId()), map);
        } else {
            counterId = platformMap.get(counterName);
        }

        if (counterId == null) {
            System.out.println("Counter not found for: " + counterName);
            return list;
        }

        PerfMetricId perfMetricId = new PerfMetricId();
        perfMetricId.setCounterId(counterId);

        // 判断是否支持 "" 聚合实例
        String aggregateCounterName = "cpu.usage.average";  // 你可以根据需要再加别的计数器

        if (aggregateCounterName.equals(counterName)) {
            perfMetricId.setInstance("");  // 特殊处理聚合场景
        } else {
            // 如果出现问题则删除该部分代码直接传*
            // 判断是否支持 "" 聚合实例
            PerfMetricId[] availableMetrics = perfMgr.queryAvailablePerfMetric(vm, null, null, refreshInterval);
            boolean supportAggregateInstance = false;
            if (availableMetrics==null){
                perfMetricId.setInstance("*");
            }else {
                for (PerfMetricId id : availableMetrics) {
                    if (id.getCounterId() == counterId) {
                        String inst = id.getInstance();
                        if (inst == null || inst.isEmpty()) {
                            supportAggregateInstance = true;
                            break;
                        }
                    }
                }
            }
            if (supportAggregateInstance) {
                perfMetricId.setInstance("");
            } else {
                perfMetricId.setInstance("*");
            }
        }

        PerfQuerySpec qSpec = createPerfQuerySpec(vm, new PerfMetricId[]{perfMetricId}, 3, refreshInterval);
        PerfEntityMetricBase[] pValues = perfMgr.queryPerf(new PerfQuerySpec[]{qSpec});

        if (pValues != null) {
            for (PerfEntityMetricBase pValue : pValues) {
                if (pValue instanceof PerfEntityMetric) {
                    list.addAll(printPerfMetric((PerfEntityMetric) pValue));
                    break;
                } else if (pValue instanceof PerfEntityMetricCSV) {
                    printPerfMetricCSV((PerfEntityMetricCSV) pValue);
                } else {
                    System.out.println("Unexpected sub-type of PerfEntityMetricBase.");
                }
            }
        }

        long eTime = System.currentTimeMillis();
        double executionTimeInSeconds = (eTime - startTime) / 1000.0;
        return list;
    }


    public static PerMonitorDO getPerEntityMericBasesCreaateByname(String name, ServiceInstance si, ManagedEntity vm, String counterName, Calendar beginTime, Calendar endTime) throws Exception {

        long startTime = System.currentTimeMillis(); // 开始时间

        if (vm == null) {
            System.out.println("Virtual Machine " + name
                    + " cannot be found.");
            si.getServerConnection().logout();
        }
        List<PerMonitorDO> list = new ArrayList<>();

        PerformanceManager perfMgr = si.getPerformanceManager();
        // find out the refresh interval for the virtual machine
        // use this interval to retrieve real time performance data
        // the interval must be one of 300, 1800, 7200 and 86400
        // see http://pubs.vmware.com/vsphere-60/index.jsp?topic=%2Fcom.vmware.wssdk.apiref.doc%2Fvim.HistoricalInterval.html
        PerfProviderSummary pps = perfMgr.queryPerfProviderSummary(vm);
        int refreshInterval = pps.getRefreshRate().intValue();
//        System.out.println("Current refresh interval is " + refreshInterval);

        Integer counterId = counterCache.get(counterName);
        if (counterId == null) {
            PerfCounterInfo[] perfCounters = perfMgr.getPerfCounter();
            Map<Integer, String> counterKeyToNameMap = new HashMap<>();

            for (PerfCounterInfo counter : perfCounters) {
                String fullName = counter.getGroupInfo().getKey() + "." + counter.getNameInfo().getKey() + "." + counter.getRollupType();
                counterKeyToNameMap.put(counter.getKey(), fullName);
                if (counterName.equals(fullName)) {
                    counterId = counter.getKey();
                    counterCache.put(counterName, counterId); // 缓存counterId
                }
            }
        }

        if (counterId != null) {
            System.out.println("CounterId for " + counterName + " is: " + counterId);
        } else {
            System.out.println("Counter not found for: " + counterName);
        }

        PerMonitorDO perMonitorDO = new PerMonitorDO();

        PerfMetricId perfMetricId = new PerfMetricId();
        perfMetricId.setCounterId(counterId);
        perfMetricId.setInstance("*");


        PerfQuerySpec qSpec = createPerfQuerySpecBytime(
                vm, new PerfMetricId[]{perfMetricId}, 3, refreshInterval, beginTime, endTime);
        PerfEntityMetricBase[] pValues = perfMgr.queryPerf(
                new PerfQuerySpec[]{qSpec});
        if (pValues != null) {
            for (int i = 0; i < pValues.length; ++i) {
                String entityDesc = pValues[i].getEntity().getType()
                        + ":" + pValues[i].getEntity().get_value();
                System.out.println("Entity:" + entityDesc);
                if (pValues[i] instanceof PerfEntityMetric) {
                    perMonitorDO = printPerfMetricByName((PerfEntityMetric) pValues[i]);
                    break;
                } else if (pValues[i] instanceof PerfEntityMetricCSV) {
                    printPerfMetricCSV((PerfEntityMetricCSV) pValues[i]);
                } else {
                    System.out.println("UnExpected sub-type of " +
                            "PerfEntityMetricBase.");
                }
            }
        }
        long eTime = System.currentTimeMillis(); // 结束时间
        long executionTime = eTime - startTime;
        double executionTimeInSeconds = executionTime / 1000.0;
        System.out.println("getPerEntityMericBasesCreaateByname代码执行时间: " + executionTimeInSeconds + " seconds");
        return perMonitorDO;
    }


    static PerfQuerySpec createPerfQuerySpec(ManagedEntity me, PerfMetricId[] metricIds, int maxSample, int interval) {
        PerfQuerySpec qSpec = new PerfQuerySpec();
        qSpec.setEntity(me.getMOR());
        qSpec.setMaxSample(maxSample);
        qSpec.setMetricId(metricIds);
        qSpec.setFormat("normal");
        qSpec.setIntervalId(interval);
        return qSpec;
    }


    static PerfQuerySpec createPerfQuerySpecBytime(ManagedEntity me,
                                                   PerfMetricId[] metricIds, int maxSample, int interval, Calendar startTime, Calendar endTime) {
        PerfQuerySpec qSpec = new PerfQuerySpec();
        qSpec.setEntity(me.getMOR());
        // set the maximum of metrics to be return
        // only appropriate in real-time performance collecting
//        qSpec.setMaxSample(new Integer(maxSample));
        qSpec.setMetricId(metricIds);
        // optionally you can set format as "normal" or "csv"
//        qSpec.setFormat("normal");
        // set the interval to the refresh rate for the entity
//        qSpec.setIntervalId(new Integer(300));
        qSpec.setStartTime(startTime);
        qSpec.setEndTime(endTime);

        return qSpec;
    }

    static void displayValues(PerfEntityMetricBase[] values) {
        for (int i = 0; i < values.length; ++i) {
            String entityDesc = values[i].getEntity().getType()
                    + ":" + values[i].getEntity().get_value();
            System.out.println("Entity:" + entityDesc);
            if (values[i] instanceof PerfEntityMetric) {
                printPerfMetric((PerfEntityMetric) values[i]);
            } else if (values[i] instanceof PerfEntityMetricCSV) {
                printPerfMetricCSV((PerfEntityMetricCSV) values[i]);
            } else {
                System.out.println("UnExpected sub-type of " +
                        "PerfEntityMetricBase.");
            }
        }
    }

    static List<PerMonitorDO> printPerfMetric(PerfEntityMetric pem) {
        List<PerMonitorDO> list = new ArrayList<>();
        PerfMetricSeries[] vals = pem.getValue();
        PerfSampleInfo[] infos = pem.getSampleInfo();

        for (int j = 0; vals != null && j < vals.length; ++j) {
            if (vals[j] instanceof PerfMetricIntSeries) {
                PerfMetricIntSeries val = (PerfMetricIntSeries) vals[j];
                long[] longs = val.getValue();
                String instance = val.getId().getInstance();
                int conterId = val.getId().getCounterId();

                for (int i = 0; infos != null && i < infos.length; i++) {
                    PerMonitorDO perMonitorDO = new PerMonitorDO();
                    perMonitorDO.setDateTime(infos[i].getTimestamp().getTime());
                    perMonitorDO.setInstance(instance);
                    perMonitorDO.setValue(longs[i]);
                    perMonitorDO.setCounterId(Convert.toLong(conterId));
                    list.add(perMonitorDO);
//                    System.out.println("Sample time: " + infos[i].getTimestamp().getTime() + ", Value: " + longs[i]);
                }
            }
        }

        return list;
    }

    static PerMonitorDO printPerfMetricByName(PerfEntityMetric pem) {
        PerfMetricSeries[] vals = pem.getValue();
        PerfSampleInfo[] infos = pem.getSampleInfo();

        PerfMetricIntSeries val = (PerfMetricIntSeries) vals[0];
        long[] longs = val.getValue();
        Long values = longs[0];

        PerMonitorDO perMonitorDO = new PerMonitorDO();
        try {
            perMonitorDO.setValue(values);
            perMonitorDO.setInstance(val.getId().getInstance());
            perMonitorDO.setDateTime(infos[0].getTimestamp().getTime());
//            System.out.println("Sampling Times and Values:");
        } catch (Exception e) {
            log.info("处理性能数据异常printPerfMetric：" + e.getMessage());
            return null;
        }

        return perMonitorDO;
    }

    static void printPerfMetricCSV(PerfEntityMetricCSV pems) {
        System.out.println("SampleInfoCSV: " + pems.getSampleInfoCSV());
        PerfMetricSeriesCSV[] csvs = pems.getValue();
        for (PerfMetricSeriesCSV csv : csvs) {
            System.out.println("PerfCounterId: " + csv.getId().getCounterId());
            System.out.println("CSV sample values: " + csv.getValue());
        }
    }

    /**
     * 获取性能指标数据，优化版本
     */
    public static Map<String, List<PerMonitorDO>> getPerEntityMetricsByNamesTest(String name, ServiceInstance si,
                                                                             ManagedEntity vm, List<String> counterNames) throws RemoteException {
        Map<String, List<PerMonitorDO>> metricsMap = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            if (vm == null) {
                log.warn("找不到虚拟机: {}", name);
                return metricsMap;
            }
            
            // 1. 获取性能管理器和刷新间隔
            PerformanceManager perfMgr = si.getPerformanceManager();
            PerfProviderSummary pps = perfMgr.queryPerfProviderSummary(vm);
            int refreshInterval = pps.getRefreshRate().intValue();

            // 2. 获取所有可用的性能计数器信息 - 优化缓存查询
            Map<String, Integer> counterIdMap = getOrInitializeCounters(perfMgr, counterNames);

            // 3. 构建性能查询规范 - 优化实例处理
            List<PerfMetricId> perfMetricIds = buildPerfMetricIds(counterNames, counterIdMap);
            if (perfMetricIds.isEmpty()) {
                log.warn("未找到任何有效的性能计数器: {}", counterNames);
                return metricsMap;
            }

            // 4. 创建查询规范，包含时间范围
            PerfQuerySpec qSpec = createEnhancedPerfQuerySpec(vm, perfMetricIds, refreshInterval);

            // 5. 执行查询并处理结果 - 优化结果处理
            PerfEntityMetricBase[] pValues = perfMgr.queryPerf(new PerfQuerySpec[]{qSpec});
            if (pValues != null && pValues.length > 0) {
                processMetricResults(pValues, counterNames, counterIdMap, metricsMap);
            } else {
                log.debug("未获取到性能数据: {}", name);
            }

            // 6. 验证结果完整性 - 确保所有计数器都有数据
            validateAndSupplementResults(metricsMap, counterNames);

        } catch (Exception e) {
            log.error("获取性能指标时发生错误: {} - {}", name, e.getMessage(), e);
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            if (executionTime > 1000) { // 记录执行时间超过1秒的查询
                log.info("性能指标查询耗时: {} ms, VM: {}", executionTime, name);
            }
        }

        return metricsMap;
    }

    /**
     * 获取或初始化计数器映射，优化缓存机制
     */
    private static Map<String, Integer> getOrInitializeCounters(PerformanceManager perfMgr,
                                                                List<String> counterNames) throws RemoteException {
        Map<String, Integer> counterIdMap = new HashMap<>();
        List<String> missingCounters = new ArrayList<>();
        
        // 首先检查缓存中是否已有计数器
        for (String counterName : counterNames) {
            Integer counterId = counterCache.get(counterName);
            if (counterId != null) {
                counterIdMap.put(counterName, counterId);
            } else {
                missingCounters.add(counterName);
            }
        }
        
        // 只查询缓存中不存在的计数器
        if (!missingCounters.isEmpty()) {
            PerfCounterInfo[] perfCounters = perfMgr.getPerfCounter();
            for (PerfCounterInfo counter : perfCounters) {
                String fullName = buildCounterFullName(counter);
                if (missingCounters.contains(fullName)) {
                    counterIdMap.put(fullName, counter.getKey());
                    counterCache.put(fullName, counter.getKey());
                    cacheTimestamps.put(fullName, System.currentTimeMillis());
                }
            }
        }

        return counterIdMap;
    }

    /**
     * 构建计数器全名
     */
    private static String buildCounterFullName(PerfCounterInfo counter) {
        return counter.getGroupInfo().getKey() + "." +
                counter.getNameInfo().getKey() + "." +
                counter.getRollupType();
    }

    /**
     * 根据计数器类型智能设置实例
     */
    private static List<PerfMetricId> buildPerfMetricIds(List<String> counterNames,
                                                         Map<String, Integer> counterIdMap) {
        List<PerfMetricId> perfMetricIds = new ArrayList<>();

        for (String counterName : counterNames) {
            Integer counterId = counterIdMap.get(counterName);
            if (counterId != null) {
                PerfMetricId perfMetricId = new PerfMetricId();
                perfMetricId.setCounterId(counterId);

                // 根据计数器类型设置适当的实例
                if (counterName.startsWith("virtualDisk") || counterName.startsWith("net")) {
                    perfMetricId.setInstance(""); // 虚拟磁盘和网络指标使用空实例
                } else {
                    perfMetricId.setInstance("*");
                }

                perfMetricIds.add(perfMetricId);
            }
        }

        return perfMetricIds;
    }

    /**
     * 创建增强的性能查询规范
     */
    private static PerfQuerySpec createEnhancedPerfQuerySpec(ManagedEntity me,
                                                             List<PerfMetricId> metricIds, int interval) {
        PerfQuerySpec qSpec = new PerfQuerySpec();
        qSpec.setEntity(me.getMOR());
        qSpec.setMetricId(metricIds.toArray(new PerfMetricId[0]));
        qSpec.setFormat("normal");
        qSpec.setIntervalId(interval);

        // 设置查询时间范围 - 查询最近5分钟的数据
        Calendar startTime = Calendar.getInstance();
        startTime.add(Calendar.MINUTE, -5);
        qSpec.setStartTime(startTime);

        // 设置采样数
        qSpec.setMaxSample(5);

        return qSpec;
    }

    /**
     * 处理性能指标结果，优化数据提取
     */
    private static void processMetricResults(PerfEntityMetricBase[] pValues,
                                             List<String> counterNames, Map<String, Integer> counterIdMap,
                                             Map<String, List<PerMonitorDO>> metricsMap) {
        // 创建反向映射，从counterId到counterName
        Map<Integer, String> reverseMap = new HashMap<>();
        counterIdMap.forEach((name, id) -> reverseMap.put(id, name));

        for (PerfEntityMetricBase pValue : pValues) {
            if (pValue instanceof PerfEntityMetric) {
                PerfEntityMetric pem = (PerfEntityMetric) pValue;
                PerfMetricSeries[] vals = pem.getValue();
                PerfSampleInfo[] infos = pem.getSampleInfo();

                if (vals != null && infos != null) {
                    for (PerfMetricSeries val : vals) {
                        if (val instanceof PerfMetricIntSeries) {
                            PerfMetricIntSeries series = (PerfMetricIntSeries) val;
                            int counterId = series.getId().getCounterId();
                            String counterName = reverseMap.get(counterId);
                            
                            if (counterName != null) {
                                List<PerMonitorDO> metrics = convertToMetrics(series, infos);
                                metricsMap.put(counterName, metrics);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 将性能数据转换为监控对象
     */
    private static List<PerMonitorDO> convertToMetrics(PerfMetricIntSeries series,
                                                       PerfSampleInfo[] infos) {
        List<PerMonitorDO> metrics = new ArrayList<>();
        long[] values = series.getValue();
        String instance = series.getId().getInstance();
        long counterId = series.getId().getCounterId();

        // 预分配容量，提高性能
        int size = Math.min(values.length, infos.length);
        metrics = new ArrayList<>(size);

        for (int i = 0; i < size; i++) {
            PerMonitorDO metric = new PerMonitorDO();
            metric.setDateTime(infos[i].getTimestamp().getTime());
            metric.setInstance(instance == null || instance.isEmpty() ? "0" : instance);
            metric.setValue(values[i]);
            metric.setCounterId(counterId);
            metrics.add(metric);
        }

        return metrics;
    }

    /**
     * 验证结果并补充缺失数据
     */
    private static void validateAndSupplementResults(Map<String, List<PerMonitorDO>> metricsMap,
                                                     List<String> counterNames) {
        // 检查是否所有计数器都有数据
        for (String counterName : counterNames) {
            if (!metricsMap.containsKey(counterName) || metricsMap.get(counterName).isEmpty()) {
                // 添加默认值
                List<PerMonitorDO> defaultMetrics = createDefaultMetrics(counterName);
                metricsMap.put(counterName, defaultMetrics);
            }
        }
    }

    /**
     * 创建默认指标数据
     */
    private static List<PerMonitorDO> createDefaultMetrics(String counterName) {
        List<PerMonitorDO> metrics = new ArrayList<>(1);
        PerMonitorDO metric = new PerMonitorDO();
        metric.setDateTime(new Date());
        metric.setInstance("0");
        metric.setValue(0L);
        metric.setCounterId(Long.valueOf(counterCache.getOrDefault(counterName, 0)));
        metrics.add(metric);
        return metrics;
    }

    /**
     * 清理过期缓存
     */
    private static void cleanExpiredCache() {
        long currentTime = System.currentTimeMillis();
        List<String> keysToRemove = new ArrayList<>();
        
        cacheTimestamps.forEach((key, timestamp) -> {
            if (currentTime - timestamp > CACHE_EXPIRY_TIME) {
                keysToRemove.add(key);
            }
        });
        
        for (String key : keysToRemove) {
            counterCache.remove(key);
            cacheTimestamps.remove(key);
        }
    }

    private static boolean isVersionLessThan(String version1, String version2) {
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        int length = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < length; i++) {
            int v1 = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2 = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1 < v2) {
                return true;
            } else if (v1 > v2) {
                return false;
            }
        }

        return false;
    }
}
