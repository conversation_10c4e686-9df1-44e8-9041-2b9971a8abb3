package cn.coder.zj.module.collector.remoting.netty.handler;

import cn.coder.zj.module.collector.job.service.ScheduleTaskService;
import cn.coder.zj.module.collector.remoting.netty.config.CollectorConfig;
import cn.coder.zj.module.collector.remoting.netty.handler.processor.ClientMessageProcessor;
import cn.coder.zj.module.collector.remoting.netty.handler.processor.ClientMessageProcessorFactory;
import cn.coder.zj.module.collector.service.autoDiscovery.AutoDiscoveryService;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 业务数据推送处理器
 * 优化：增强消息处理的准确性和可靠性
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
public class BusinessHandler extends SimpleChannelInboundHandler<ClusterMsg.Message> {

    private final CollectorConfig collectorConfig;
    private final ClientMessageProcessorFactory processorFactory;

    public BusinessHandler(TaskExecutor taskExecutor, ScheduleTaskService scheduleTaskService, 
                          CollectorConfig collectorConfig, AutoDiscoveryService autoDiscoveryService) {
        this.collectorConfig = collectorConfig;
        this.processorFactory = new ClientMessageProcessorFactory(taskExecutor, scheduleTaskService, collectorConfig, autoDiscoveryService);
    }

    /**
     * 读取到消息后进行处理
     * 优化：使用处理器模式重构消息处理逻辑
     *
     * @param ctx         channel上下文
     * @param messageInfo 发送消息
     */
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, ClusterMsg.Message messageInfo) {
        try {
            String messageClientId = messageInfo.getClientId();
            String localClientId = collectorConfig.getClientId();
            
            // 验证消息是否属于当前客户端
            if (!StringUtils.hasText(messageClientId) || !messageClientId.equals(localClientId)) {
                log.warn("[消息验证失败] 收到不属于本客户端的消息 - 消息客户端ID: {}, 本地客户端ID: {}, 消息类型: {}", 
                        messageClientId, localClientId, messageInfo.getType());
                return;
            }
            
            log.debug("[消息处理] 客户端: {}, 消息类型: {}, 数据长度: {}", 
                    messageClientId, messageInfo.getType(), 
                    messageInfo.getData() != null ? messageInfo.getData().length() : 0);
            
            // 根据消息类型获取对应的处理器
            ClientMessageProcessor processor = processorFactory.getProcessor(messageInfo.getType());
            
            if (processor != null) {
                // 使用对应的处理器处理消息
                ClusterMsg.Message response = processor.process(ctx, messageInfo);
                
                // 如果处理器返回了响应消息，则发送响应
                if (response != null) {
                    ctx.writeAndFlush(response);
                }
                
                log.debug("[消息处理完成] 客户端: {}, 消息类型: {}, 处理器: {}", 
                        messageClientId, messageInfo.getType(), processor.getClass().getSimpleName());
            } else {
                log.warn("[无处理器] 客户端: {}, 消息类型: {}, 未找到对应的处理器",
                        messageClientId, messageInfo.getType());
            }
            
        } catch (Exception e) {
            log.error("[消息处理异常] 客户端: {}, 消息类型: {}, 异常: {}", 
                    messageInfo.getClientId(), messageInfo.getType(), e.getMessage(), e);
        } finally {
            ReferenceCountUtil.release(messageInfo);
        }
    }


}
