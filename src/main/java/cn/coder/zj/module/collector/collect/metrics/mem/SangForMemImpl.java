package cn.coder.zj.module.collector.collect.metrics.mem;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.ApiCacheService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.MemType.PROTOCOL_SXF_MEM;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getDoubleFromJson;

@Slf4j
public class SangForMemImpl extends AbstractMetrics {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();
                Platform platform = (Platform) o;
                // 获取所有VM的UUID并组合成标签
                List<MonitorInfo> vmUuids = platform.getSxfPlatform().getVmUuids();
                List<MonitorInfo> hostUuids = platform.getSxfPlatform().getHostUuids();

                getMetricVmData(vmUuids, platform,metricDataList);
                getMetricHostData(hostUuids, platform, metricDataList);

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.MEM_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                log.info("collectVmData name: {}", Thread.currentThread().getName());
            });
        }
    }


    private List<MetricData> getMetricVmData(List<MonitorInfo> vmUuids, Platform platform, List<MetricData> metricDataList) {
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie, "CSRFPreventionToken", token);
        String platformUrl = platform.getPlatformUrl();

        vmUuids.forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(MEM_USED_TASK.code());
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("vm");

            String url = platformUrl + SangForApiConstant.GET_VM_DETAIL.replace("{vmid}", info.getUuid());
            JsonObject obj = ApiCacheService.getJsonObject(url, null, headers);

            if (obj.size() > 0 && obj.has("mem_sheet")) {
                JsonArray memSheet = obj.getAsJsonObject("mem_sheet").getAsJsonArray("hour");
                Double memTotal = dealInfo(memSheet, "总内存");
                Double memUsed = dealInfo(memSheet, "已使用内存");

                if (memUsed != null && memTotal != null) {
                    metricData.setValues(Arrays.asList(memUsed));
                    metricData.setTimestamps(Arrays.asList(System.currentTimeMillis()));
                    metricDataList.add(metricData);

                    MetricData freeMemData = BeanUtil.copyProperties(metricData, MetricData.class);
                    freeMemData.setMetricName(MEM_FREE_TASK.code());
                    freeMemData.setValues(Arrays.asList(memTotal - memUsed));
                    freeMemData.setTimestamps(metricData.getTimestamps());
                    freeMemData.setType("vm");
                    metricDataList.add(freeMemData);
                }
            }

            if (obj.size() > 0 && obj.has("mem_status")) {
                JsonObject mem_status = obj.getAsJsonObject("mem_status");
                Double ratio = getDoubleFromJson(mem_status, "ratio");
                MetricData useage = BeanUtil.copyProperties(metricData, MetricData.class);
                useage.setMetricName(MEM_USAGE_TASK.code());
                useage.setValues(Arrays.asList(ratio * 100));
                useage.setTimestamps(metricData.getTimestamps());
                useage.setType("vm");
                metricDataList.add(useage);
            }
        });

        return metricDataList;
    }

    private List<MetricData> getMetricHostData(List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie, "CSRFPreventionToken", token);
        String platformUrl = platform.getPlatformUrl();

        hostUuids.forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(MEM_USED_TASK.code());
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("host");

            String url = platformUrl + SangForApiConstant.GET_HOST_DETAIL.replace("{nodeId}", info.getUuid()) + "mem";
            JsonArray obj = getJsonArrayFromApi(url, null, headers);

            if (!obj.isEmpty()) {
                Double memTotal = dealInfo(obj, "总内存");
                Double memUsed = dealInfo(obj, "已使用内存");

                if (memUsed != null && memTotal != null && memTotal > 0 && memUsed >= 0) {
                    metricData.setValues(Arrays.asList(memUsed));
                    metricData.setTimestamps(Arrays.asList(System.currentTimeMillis()));
                    metricData.setType("host");
                    metricDataList.add(metricData);

                    MetricData freeMemData = BeanUtil.copyProperties(metricData, MetricData.class);
                    freeMemData.setMetricName(MEM_FREE_TASK.code());
                    freeMemData.setValues(Arrays.asList(memTotal - memUsed));
                    freeMemData.setTimestamps(metricData.getTimestamps());
                    freeMemData.setType("host");
                    metricDataList.add(freeMemData);

                    MetricData useage = BeanUtil.copyProperties(metricData, MetricData.class);
                    useage.setMetricName(MEM_USAGE_TASK.code());
                    useage.setValues(Arrays.asList(NumberUtil.div(memUsed, memTotal, 2) * 100));
                    useage.setTimestamps(metricData.getTimestamps());
                    useage.setType("host");
                    metricDataList.add(useage);
                } else {
                    log.warn("物理机 {} 获取到无效的内存数据: memTotal={}, memUsed={}, 跳过本次采集", 
                             info.getName(), memTotal, memUsed);
                }
            } else {
                log.warn("物理机 {} API返回空数据，跳过本次采集, URL: {}", info.getName(), url);
            }
        });

        return metricDataList;
    }

    private Double dealInfo(JsonArray data, String label) {
        if (data == null || data.isEmpty()) {
            return null;
        }
        // 处理VM的数据格式
        if (data.get(0).getAsJsonObject().has("name")) {
            JsonArray valArr = new JsonArray();

            for (JsonElement element : data) {
                JsonObject item = element.getAsJsonObject();
                String name = item.get("name").getAsString();

                if (name.contains(label)) {
                    valArr = item.get("data").getAsJsonArray();
                    break;
                }
            }

            if (!valArr.isEmpty()) {
                double value = valArr.get(valArr.size() - 1).getAsDouble();
                return Double.parseDouble(String.format("%.2f", value));
            }
        }
        // 处理Host的数据格式
        else {
            for (JsonElement element : data) {
                JsonObject item = element.getAsJsonObject();
                if (item.get("name").getAsString().contains(label)) {
                    JsonArray cpuData = item.get("data").getAsJsonArray();
                    if (cpuData != null && !cpuData.isEmpty()) {
                        double value = cpuData.get(cpuData.size() - 1).getAsDouble();
                        return Double.parseDouble(String.format("%.2f", value));
                    }
                    break;
                }
            }
        }

        return null;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_SXF_MEM.code();
    }
}
