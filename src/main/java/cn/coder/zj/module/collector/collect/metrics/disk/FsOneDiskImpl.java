package cn.coder.zj.module.collector.collect.metrics.disk;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static cn.coder.zj.module.collector.enums.DiskType.PROTOCOL_FUSION_COMPUTE_DISK;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class FsOneDiskImpl extends AbstractMetrics implements MetricsCollectHelper.MetricsHandler {
    protected long startTime;
    
    @Override
    public void preCheck(Platform platform) {
        // 预检查逻辑
    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) platformObj;
                List<MetricData> metricDataList = collectData(platform);

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.DISK_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("Fs-DISK性能采集 {} 秒,{} 平台", endTimeFormatted,platform.getPlatformName());
            });
        }
    }

    private List<MetricData> collectData(Platform platform) {
        List<MetricData> list = new ArrayList<>();
        list.addAll(MetricsCollectHelper.collectVmData(platform, "DISK", this));
        list.addAll(MetricsCollectHelper.collectHostData(platform, "DISK", this));
        list.addAll(collectStorageData(platform));
        return list;
    }

    @Override
    public List<MetricData> handleVmMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonObject cloudInfo = FsApiCacheService.getJsonObject(platformUrl + getStringFromJson(cloud, "uri"), null, headers);

        JsonArray valueArray = MetricsCollectHelper.getMetricsValueArray(platform, cloudInfo, headers);
        MetricData metricData = MetricsCollectHelper.createBaseMetricData(platform, cloud, DISK_READ_TASK.code(),"vm");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonArray values = element.getAsJsonObject().getAsJsonArray("value");
            for (JsonElement value : values) {
                JsonObject asJsonObject = value.getAsJsonObject();
                String metricId = getStringFromJson(asJsonObject, "metricId");
                if(metricId.equals("disk_io_in")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    metricData.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricData.setType("vm");
                    metricDataList.add(metricData);
                }

                if(metricId.equals("disk_io_out")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_WRITE_TASK.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("vm");
                    metricDataList.add(data);
                }

//                if(metricId.equals("disk_usage")){
//                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
//                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
//                    data.setMetricName(DISK_USED_TASK.code());
//                    data.setValues(Arrays.asList(metricValue.doubleValue()));
//                    metricDataList.add(data);
//                }

                if(metricId.equals("disk_req_in")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_READ_OPS.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("vm");
                    metricDataList.add(data);
                }

                if(metricId.equals("disk_req_out")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_WRITE_OPS.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("vm");
                    metricDataList.add(data);
                }
            }
        }
        return metricDataList;
    }

    @Override
    public List<MetricData> handleHostMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject host = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonArray valueArray = MetricsCollectHelper.getMetricsValueArray(platform, host, headers);
        MetricData metricData = MetricsCollectHelper.createBaseMetricData(platform, host, DISK_READ_TASK.code(),"host");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonArray values = element.getAsJsonObject().getAsJsonArray("value");
            for (JsonElement value : values) {
                JsonObject asJsonObject = value.getAsJsonObject();
                String metricId = getStringFromJson(asJsonObject, "metricId");
                if(metricId.equals("disk_io_in")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    metricData.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricData.setType("host");
                    metricDataList.add(metricData);
                }

                if(metricId.equals("disk_io_out")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_WRITE_TASK.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("host");
                    metricDataList.add(data);
                }

//                if(metricId.equals("disk_usage")){
//                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
//                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
//                    data.setMetricName(DISK_USED_TASK.code());
//                    data.setValues(Arrays.asList(metricValue.doubleValue()));
//                    metricDataList.add(data);
//                }

                if(metricId.equals("disk_req_in")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_READ_OPS.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("host");
                    metricDataList.add(data);
                }

                if(metricId.equals("disk_req_out")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_WRITE_OPS.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("host");
                    metricDataList.add(data);
                }
            }
        }
        return metricDataList;
    }

    protected List<MetricData> collectStorageData(Platform platform) {
        List<MetricData> metricDataList = new ArrayList<>();
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String url = platform.getPlatformUrl() + FusionOneApiConstant.DATASTORES_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject storageJson = FsApiCacheService.getJsonObject(url, null, header);
        if (storageJson != null) {
            JsonArray storageArray = storageJson.getAsJsonArray("datastores");
            storageArray.forEach(storage -> {
                JsonObject storageInfo = (JsonObject) storage;
                String urn = getStringFromJson(storageInfo, "urn");
                String name = getStringFromJson(storageInfo, "name");
                //容量使用率
                BigDecimal usageRate = BigDecimal.ZERO;
                BigDecimal totalCapacity = getBigFromJson(storageInfo,"actualCapacityGB");
                Long usedCapacity = getLongFromJson(storageInfo,"usedSizeGB");
                if (totalCapacity != null && totalCapacity.compareTo(BigDecimal.ZERO) > 0) {
                    usageRate = BigDecimal.valueOf(usedCapacity).divide(totalCapacity, 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100));
                }
                MetricData metricData = new MetricData();
                metricData.setPlatformId(platform.getPlatformId());
                metricData.setMetricName(DISK_USED_TASK.code());
                metricData.setTimestamps(List.of(System.currentTimeMillis() / 1000));
                metricData.setResourceId(urn);
                metricData.setResourceName(name);
                metricData.setValues(List.of(usageRate.doubleValue()));
                metricDataList.add(metricData);
            });
        }else {
            return new ArrayList<>();
        }
        return metricDataList;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_FUSION_COMPUTE_DISK.code();
    }
}
