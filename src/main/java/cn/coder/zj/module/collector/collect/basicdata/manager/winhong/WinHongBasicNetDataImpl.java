package cn.coder.zj.module.collector.collect.basicdata.manager.winhong;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL2Data;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL3Data;
import cn.iocoder.zj.framework.common.enums.MetricsType;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import io.netty.channel.ChannelHandlerContext;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.util.*;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_NET;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_WIN_HONG_NET;


@Log4j2
public class WinHongBasicNetDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                NetworkData resData = collectData(platformObj);

                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(resData.getNetWorkL2Data())
                        .metricsName(BASIC_NET.code())
                        .metricsType(MetricsType.BASIC_NET_L2.code())
                        .build()));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());


                ChannelHandlerContext ctx = CacheService.getCtx("ctx");
                ClusterMsg.Message.Builder messageL3 = ClusterMsg.Message.newBuilder().setType(ClusterMsg.MessageType.BASIC)
                        .setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(resData.getNetWorkL3Data())
                                .metricsName(BASIC_NET.code())
                                .metricsType(MetricsType.BASIC_NET_L3.code())
                                .build()));
                messageL3.setTime(System.currentTimeMillis());
                ctx.writeAndFlush(messageL3);
            });
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_WIN_HONG_NET.code();
    }

    private NetworkData collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new NetworkData();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);
        Map<String, String> param = new HashMap<>();
        param.put("needMonitorInfo", "true");
        param.put("domainFlag", "4");

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HARDWARE_LIST, null, headers).getAsJsonObject().get("data").getAsJsonArray();
        if (ObjectUtil.isNull(hostArray)) return new NetworkData();
        List<NetWorkL2Data> netWorkL2DTOS = new ArrayList<>();
        List<NetWorkL3Data> netWorkL3DTOS = new ArrayList<>();
        NetworkData resData = new NetworkData();
        List<Map> hostList = GSON.fromJson(hostArray, new TypeToken<List<Map>>() {
        }.getType());
        if (CollUtil.isNotEmpty(hostList)) {
            for (Map hostMap : hostList) {

                    String uuid = Convert.toStr(hostMap.get("id"));
                    //网络
                    JsonArray groups = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_PORT_GROUPS_VSWITCHS.replace("{hostId}", uuid), new HashMap<>(), headers).getAsJsonArray();
                    for (Object group : groups) {
                        //二级
                        JsonObject groupObj = (JsonObject) group;
                        String portGroupsId = groupObj.get("id").getAsString();
                        String ip = groupObj.get("ip").getAsString();
                        //三级
                        JsonArray portGroups = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_PORT_GROUPS.replace("{portGroupsId}", portGroupsId), new HashMap<>(), headers).getAsJsonObject().get("data").getAsJsonArray();
                        if (CollectionUtil.isNotEmpty(portGroups)) {
                            for (Object item : portGroups) {
                                JsonObject obj = (JsonObject) item;
                                NetWorkL2Data netWorkL2 = new NetWorkL2Data();
                                netWorkL2.setName(obj.get("name").getAsString());
                                netWorkL2.setUuid(obj.get("id").getAsString());
                                netWorkL2.setPlatformName(platform.getPlatformName());
                                netWorkL2.setPlatformId(platform.getPlatformId());
                                netWorkL2.setRegionId(platform.getRegionId());
                                netWorkL2.setTypeName("winHong");
                                if (StringUtil.isNotEmpty(obj.get("vlanId").getAsString())) {
                                    netWorkL2.setVlan(obj.get("vlanId").getAsString());
                                    netWorkL2.setType("VLAN");
                                } else {
                                    netWorkL2.setType("FLAT");
                                }
                                netWorkL2DTOS.add(netWorkL2);
                            }
                        }


                        NetWorkL3Data netWorkL3DTO = new NetWorkL3Data();
                        netWorkL3DTO.setUuid(groupObj.get("id").getAsString() + "_3");
                        netWorkL3DTO.setName(groupObj.get("name").getAsString());
                        netWorkL3DTO.setType("L3BasicNetwork");
                        netWorkL3DTO.setNetworkSegment(groupObj.get("ip").getAsString() + "-" + (groupObj.get("netmask").isJsonNull() ? "" : groupObj.get("netmask").getAsString()));
                        netWorkL3DTO.setNetmask(groupObj.get("netmask").isJsonNull() ? "" : groupObj.get("netmask").getAsString());
                        // 处理 gateway 字段
                        JsonElement gatewayElement = groupObj.get("gateWay");
                        netWorkL3DTO.setGateway(gatewayElement != null && !gatewayElement.isJsonNull() ?
                                gatewayElement.getAsString() : "");
                        netWorkL3DTO.setPlatformId(platform.getPlatformId());
                        netWorkL3DTO.setPlatformName(platform.getPlatformName());
                        netWorkL3DTO.setL2NetworkUuid(groupObj.get("id").getAsString());
                        netWorkL3DTO.setL2NetworkName(groupObj.get("name").getAsString());
                        netWorkL3DTO.setTypeName("winHong");
                        netWorkL3DTOS.add(netWorkL3DTO);

                    }

            }
        }
        resData.setNetWorkL2Data(netWorkL2DTOS);
        resData.setNetWorkL3Data(netWorkL3DTOS);
        return resData;
    }

    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            return GSON.fromJson(response.body().string(), JsonElement.class);
        } catch (IOException e) {
            log.error("error collecting basic data: {}", e.getMessage());
            return new JsonObject();
        }
    }

    @Data
    class NetworkData {
        private List<NetWorkL2Data> netWorkL2Data;
        private List<NetWorkL3Data> netWorkL3Data;
    }
}
