package cn.coder.zj.module.collector.constants.istack;
public class IStackApiConstant {
    public static final String ISTACK_API_PREFIX = "/api";
    public static final String GET_VM_REALTIME = "/biz/zstack/vm_vm_monitor/aggregationQuery/";
    public static final String GET_VM_DETAIL = ISTACK_API_PREFIX + "/api/instances/list/";
    public static final String GET_VOLUMES_DETAIL = ISTACK_API_PREFIX + "/api/volumes/list/";
    public static final String GET_VM_USE = "/biz/zstack/upgrade/resource/statistics/summary/listByPage/";
    public static final String GET_VM_USAGE = "/biz/zstack/vm_vm_monitor/query/";
    public static final String GET_HARDWARE = ISTACK_API_PREFIX + "/api/hosts/query/";
    public static final String GET_HOST_REALTIME = "/biz/zstack/vm_host_monitor/query/";
    public static final String GET_STORAGE_LIST = ISTACK_API_PREFIX + "/api/storage_cluster/query/";
    public static final String GET_V_NETWORK_LIST = ISTACK_API_PREFIX + "/api/networks/query/";
    public static final String GET_V_NETWORK3_LIST = ISTACK_API_PREFIX + "/api/subnets/query/";
    public static final String GET_V_DHCP_LIST = ISTACK_API_PREFIX + "/api/subnets/get_used_ip/";
    public static final String GET_VM_NICS = ISTACK_API_PREFIX + "/api/instances/getNics/";
    public static final String GET_EIPS = ISTACK_API_PREFIX + "/api/floatings/list/";
    public static final String GET_SECGROUPS = ISTACK_API_PREFIX + "/api/firewalls/query/";
    //查询镜像列表
    public static final String GET_IMAGE_LIST = ISTACK_API_PREFIX + "/api/images/list/";
    //查询云盘快照
    public static final String GET_VOLUME_SNAPSHOT_LIST = ISTACK_API_PREFIX + "/api/volumesnapshots/query/";
    //查询主机快照
    public static final String GET_INSTANCE_SNAPSHOT_LIST = ISTACK_API_PREFIX + "/api/instancesnapshots/query/";
    public static final String GET_ALL_ZONE = "/api/cloudAvailabilityZone/listAllZone/";
}
