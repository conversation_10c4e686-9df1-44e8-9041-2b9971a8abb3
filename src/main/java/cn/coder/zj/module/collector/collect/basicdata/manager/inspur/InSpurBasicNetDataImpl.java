package cn.coder.zj.module.collector.collect.basicdata.manager.inspur;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL2Data;
import cn.iocoder.zj.framework.common.enums.MetricsType;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.*;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromFsApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_NET;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IN_SPUR_NET;

@Slf4j
public class InSpurBasicNetDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                List<NetWorkL2Data> netWorkL2List = new ArrayList<>();
                collectData(platform, netWorkL2List);
                if (!netWorkL2List.isEmpty()) {
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GSON.toJson(BasicCollectData.builder()
                            .basicDataMap(netWorkL2List)
                            .metricsName(BASIC_NET.code())
                            .metricsType(MetricsType.BASIC_NET_L2.code())
                            .build()));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                }
                double costTime = (System.currentTimeMillis() - startTime) / 1000.0;
                log.info("浪潮 {} 网络数据收集完成，耗时：{} 秒", platform.getPlatformName(), String.format("%.2f", costTime));
            });
        }
    }

    private void collectData(Platform platform, List<NetWorkL2Data> netWorkL2List) {
        //获取网络
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return;
        }

        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization", token
        );
        JsonObject netWorkObj = getJsonObjectFromFsApi(platform.getPlatformUrl() + InSpurApiConstant.VM_NETWORK_LIST,null,header);
        JsonArray array = netWorkObj.getAsJsonArray("items");
        if (ObjectUtil.isNull(array)) return ;

        if (CollUtil.isNotEmpty(array)) {
            for (JsonElement jsonElement : array) {
                processNetWork(jsonElement, platform,netWorkL2List);
            }
        }
    }

    private void processNetWork(JsonElement jsonElement, Platform platform,List<NetWorkL2Data> netWorkL2List) {
        JsonObject netWork = GSON.toJsonTree(jsonElement).getAsJsonObject();

        NetWorkL2Data NetWorkL2Data = new NetWorkL2Data();
        NetWorkL2Data.setName(getStringFromJson(netWork,"name"));
        NetWorkL2Data.setUuid(getStringFromJson(netWork,"id"));
        NetWorkL2Data.setVlan(getStringFromJson(netWork,"vlan"));
        if (StringUtil.isNotEmpty(getStringFromJson(netWork,"vlan"))) {
            NetWorkL2Data.setVlan(getStringFromJson(netWork,"vlan"));
            NetWorkL2Data.setType("VLAN");
        } else {
            NetWorkL2Data.setType("FLAT");
        }
        NetWorkL2Data.setVirtualNetworkId(0);
        NetWorkL2Data.setPlatformId(platform.getPlatformId());
        NetWorkL2Data.setPlatformName(platform.getPlatformName());
        NetWorkL2Data.setRegionId(platform.getRegionId());
        NetWorkL2Data.setTypeName("inspur");
        NetWorkL2Data.setCreateTime(new Date());
        NetWorkL2Data.setPhysicalInterface(getStringFromJson(netWork,"type"));
        netWorkL2List.add(NetWorkL2Data);
    }

    @Override
    public String supportProtocol() {
        return BASIC_IN_SPUR_NET.code();
    }
}
