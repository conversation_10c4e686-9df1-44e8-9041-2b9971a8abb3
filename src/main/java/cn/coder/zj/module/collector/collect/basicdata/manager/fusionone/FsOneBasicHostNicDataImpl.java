package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromFsApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getIntFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.MetricsType.BASIC_HOST_VIC_FUSIONONE;

@Slf4j
public class FsOneBasicHostNicDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                List<HostNicData> list = collectData(platform);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_HOST_VIC.code())
                        .metricsType(BASIC_HOST_VIC_FUSIONONE.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<HostNicData> collectData(Platform platform) {
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_HOST_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject hosts = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray hostArray = hosts.getAsJsonArray("hosts");
        if (ObjectUtil.isNull(hostArray)) return null;
        List<HostNicData> hostNicDataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    List<HostNicData> hostNicData = collectHostNicInfo(platform, jsonElement,header);
                    if (hostNicData != null) {
                        hostNicDataList.addAll(hostNicData);
                    }
                } catch (Exception e) {
                    log.error("处理Fusionone宿主机网络数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return hostNicDataList;
    }

    private List<HostNicData> collectHostNicInfo(Platform platform, JsonElement jsonElement, Map<String, String> header) {
        List<HostNicData>  hostNicDataList = new ArrayList<>();
        String platformUrl = platform.getPlatformUrl();
        String siteId = platform.getFsOnePlatform().getSiteId();

        JsonObject hostBase = GSON.toJsonTree(jsonElement).getAsJsonObject();
        if (ObjectUtil.isNull(hostBase)) return null;
        String urn = getStringFromJson(hostBase,"urn").split("hosts:")[1];

        //获取物理网卡信息
        JsonObject systemIntFsObj = getJsonObjectFromFsApi(
                platformUrl + FusionOneApiConstant.GET_HOST_SYSTEMINTFS.replace("{siteId}", siteId).replace("{urn}", urn),
                null,
                header);
        if (ObjectUtil.isNull(systemIntFsObj)) return null;
        JsonArray systemIntFs = systemIntFsObj.getAsJsonArray("systemIntfs");

        //添加网络信息
        for (Object systemIntF : systemIntFs) {
            JsonObject jsonObject = (JsonObject) systemIntF;
            HostNicData nicRespDTO = new HostNicData();
            nicRespDTO.setUuid(getStringFromJson(jsonObject,"urn"));
            nicRespDTO.setHardwareUuid(getStringFromJson(hostBase,"uuid"));
            nicRespDTO.setIpAddresses(getStringFromJson(jsonObject,"netAddr"));
            nicRespDTO.setIpSubnet(getStringFromJson(jsonObject,"name"));
            nicRespDTO.setL2NetworkUuid(getStringFromJson(jsonObject,"portUrn"));
            nicRespDTO.setL2NetworkName(getStringFromJson(jsonObject,"portName"));
            Integer flag = getIntFromJson(jsonObject,"flag");
            if (flag==0){
                nicRespDTO.setNetworkType("上行口");
            }else {
                nicRespDTO.setNetworkType("上行链路口");
            }
            nicRespDTO.setState(true);
            nicRespDTO.setPlatformId(platform.getPlatformId());
            nicRespDTO.setPlatformName(platform.getPlatformName());
            hostNicDataList.add(nicRespDTO);
        }
        return hostNicDataList;
    }

    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_HOST_VIC.code();
    }
}
