package cn.coder.zj.module.collector.service.asyncDemo;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
public class TestController {

    private final AsyncTaskService asyncTaskService;

    public TestController(AsyncTaskService asyncTaskService) {
        this.asyncTaskService = asyncTaskService;
    }

    @GetMapping("/test")
    public void testAsyncTask() {
        asyncTaskService.asyncTask();
        asyncTaskService.asyncTaskTest();
    }
}
