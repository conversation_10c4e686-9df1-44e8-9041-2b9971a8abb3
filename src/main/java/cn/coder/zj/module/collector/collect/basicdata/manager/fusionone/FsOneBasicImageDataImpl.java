package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import java.math.BigDecimal;
import java.util.*;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_IMAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_IMAGE;
@Slf4j
public class FsOneBasicImageDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<ImageData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_IMAGE.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<ImageData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_CLOUD_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("vms");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<ImageData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    List<ImageData> vmData = collectImageData(platform, jsonElement,header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理Fusionone云盘数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private List<ImageData> collectImageData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        List<ImageData> list = new ArrayList<>();

        // 基础信息获取
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonObject cloudInfo = FsApiCacheService.getJsonObject(platformUrl + getStringFromJson(cloud, "uri"), null, headers);
        JsonObject vmConfig = cloudInfo.getAsJsonObject("vmConfig");
        JsonObject disk = vmConfig.getAsJsonArray("disks").get(0).getAsJsonObject();
        JsonObject memory = vmConfig.getAsJsonObject("memory");
        String osType = cloudInfo.getAsJsonObject("osOptions").get("osType").getAsString().toLowerCase();

        if(getBooleanFromJson(cloud,"isTemplate")) {
            ImageData imageInfo = new ImageData();

            // 设置基本信息
            imageInfo.setUuid(getStringFromJson(cloudInfo,"uuid"));
            imageInfo.setName(getStringFromJson(cloudInfo,"name"));
            imageInfo.setStatus("Enabled");
            imageInfo.setCpuArch(getStringFromJson(cloudInfo,"arch"));
            imageInfo.setSharingScope("不共享");
            imageInfo.setOsType(osType);
            imageInfo.setApplicationPlatform(osType.contains("linux") || osType.contains("centos") ? "Linux" :
                    osType.contains("windows") ? "Windows" : "");
            imageInfo.setFormat(getStringFromJson(disk,"volumeFormat"));
            imageInfo.setImageType("RootVolumeTemplate");
            imageInfo.setOsLanguage("");

            // 设置资源信息
            long memoryMB = getLongFromJsonDouble(memory,"quantityMB");
            long diskGB = getLongFromJsonDouble(disk,"quantityGB");
            imageInfo.setMinMemory(BigDecimal.valueOf(memoryMB * 1024 * 1024));
            imageInfo.setMinDisk(BigDecimal.valueOf(diskGB * 1024 * 1024 * 1024));

            // 设置其他属性
            imageInfo.setDiskDriver("");
            imageInfo.setNetworkDriver("");
            imageInfo.setBootMode("");
            imageInfo.setRemoteProtocol("");

            // 设置平台信息
            imageInfo.setPlatformId(platform.getPlatformId());
            imageInfo.setPlatformName(platform.getPlatformName());

            // 设置创建时间
            String createTime = getStringFromJson(cloudInfo, "createTime");
            try {
                imageInfo.setVCreateDate(StrUtil.isNumeric(createTime) ?
                        DateUtil.date(Long.parseLong(createTime) * 1000L) :
                        DateUtil.parseDateTime(createTime));
            } catch (Exception e) {
                imageInfo.setVCreateDate(DateUtil.date());
            }

            list.add(imageInfo);
        }

        return list;
    }


    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_IMAGE.code();
    }
}
