package cn.coder.zj.module.collector.service.apicache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromApi;

@Service
@Slf4j
public class ApiCacheService {
    private static final long CACHE_EXPIRATION_SECONDS = 30;
    private static final long CACHE_MAX_SIZE = 1024;
    private static final Gson gson = new Gson();
    private static final AtomicLong cacheVersion = new AtomicLong(0);
    private static final ConcurrentHashMap<String, CacheMetadata> cacheMetadataMap = new ConcurrentHashMap<>();

    private static final LoadingCache<String, JsonObject> jsonObjectCache = CacheBuilder.newBuilder()
            .expireAfterWrite(CACHE_EXPIRATION_SECONDS, TimeUnit.SECONDS)
            .maximumSize(CACHE_MAX_SIZE)
            .recordStats()
            .build(new CacheLoader<String, JsonObject>() {
                @Override
                public JsonObject load(String key) throws Exception {
                    CacheKey cacheKey = gson.fromJson(key, CacheKey.class);
                    JsonObject result = getJsonObjectFromApi(cacheKey.getUrl(), cacheKey.getParams(), cacheKey.getHeaders());
                    if (result != null) {
                        cacheMetadataMap.put(key, new CacheMetadata(cacheVersion.get()));
                        return result;
                    }
                    throw new Exception("API合并请求失败");
                }
            });

    public static JsonObject getJsonObject(String url, Map<String, String> params, Map<String, String> headers) {
        try {
            String cacheKey = buildCacheKey(url, params, headers);
            JsonObject result = jsonObjectCache.get(cacheKey);
            return result;
        } catch (ExecutionException e) {
            log.error("获取API或缓存数据异常: {}", url, e.getMessage());
            return null;
        }
    }


    private static String buildCacheKey(String url, Map<String, String> params, Map<String, String> headers) {
        CacheKey cacheKey = new CacheKey(url, params, headers);
        return gson.toJson(cacheKey);
    }

    @Data
    @AllArgsConstructor
    private static class CacheKey {
        private String url;
        private Map<String, String> params;
        private Map<String, String> headers;
    }

    @Data
    private static class CacheMetadata {
        private final long version;
        private final long timestamp;

        public CacheMetadata(long version) {
            this.version = version;
            this.timestamp = System.currentTimeMillis();
        }
    }
}
