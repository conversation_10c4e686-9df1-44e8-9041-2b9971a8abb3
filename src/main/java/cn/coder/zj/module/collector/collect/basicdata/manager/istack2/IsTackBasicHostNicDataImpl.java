package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_HOST_VIC;

@Slf4j
public class IsTackBasicHostNicDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);

        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK2.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(TimeUtils.withExecutionTime(
                    String.format("プラットフォーム[%s]のデータ処理", platform.getPlatformName()), IsTackBasicHostNicDataImpl.class.getSimpleName(), startTime, () -> {
                        List<HostNicData> list = new ArrayList<>();

                        // 平台异常
                        if (platform.getState() == 1) {
                            return;
                        }
                        managerData(platform, list);
                        if (!list.isEmpty()) {
                            BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                                    .metricsName(BASIC_HOST_VIC.code())
                                    .build();
                            message.setType(ClusterMsg.MessageType.BASIC);
                            message.setData(GsonUtil.GSON.toJson(build));
                            message.setTime(System.currentTimeMillis());
                            sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        }
                    }));
        }
    }

    private void managerData(Platform platform, List<HostNicData> list) {
        platform.getIsTackPlatform().getHostUuids().forEach(
                info -> {
                    Map<String, String> param = new HashMap<>();
                    param.put("os_id", platform.getPassword());
                    param.put("ct_user_id", platform.getUsername());
                    try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_V_NETWORK_LIST, param, null)) {
                        JsonObject jsonObject = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
                        if (!jsonObject.has("results") || jsonObject.get("results").isJsonNull()) {
                            log.warn("平台 {} 主机 {} 没有数据", platform.getPlatformName());
                            return;
                        }
                        JsonArray asJsonArray = jsonObject.get("results").getAsJsonArray();

                        if (asJsonArray.isEmpty()) {
                            log.warn("平台 {} 主机 {} 返回空数据集", platform.getPlatformName());
                            return;
                        }
                        asJsonArray.forEach(
                                jsonElement -> {
                                    JsonObject json = jsonElement.getAsJsonObject();
                                    String l2Uuid = getJsonString(json, "id");
                                    String l2Name = getJsonString(json, "name");
                                    String ip = getJsonString(json, "cidr");

                                    boolean state = "ACTIVE".equals(getJsonString(json, "status"));
                                    String ipSubnet = getJsonString(json, "netmask");
                                    if (ipSubnet.isEmpty()) {
                                        ipSubnet = "-";
                                    }
                                    HostNicData nicData = HostNicData.builder()
                                            .hardwareUuid(info.getUuid())
                                            .l2NetworkUuid(l2Uuid)
                                            .l2NetworkName(l2Name)
                                            .mac("-")
                                            .networkType("业务网")
                                            .ipAddresses(ip)
                                            .ipSubnet(ipSubnet)
                                            .platformId(platform.getPlatformId())
                                            .platformName(platform.getPlatformName())
                                            .state(state)
                                            .networkType("管理口")
                                            .platformId(platform.getPlatformId())
                                            .platformName(platform.getPlatformName())
                                            .build();
                                    if (json.has("subnet_list") && !json.get("subnet_list").getAsJsonArray().isEmpty()) {
                                        JsonArray subnetList = json.get("subnet_list").getAsJsonArray();
                                        for (JsonElement subnetElement : subnetList) {
                                            JsonObject subnet = subnetElement.getAsJsonObject();
                                            HostNicData hostNicData = BeanUtil.copyProperties(nicData, HostNicData.class);
                                            hostNicData.setUuid(getJsonString(subnet, "id"));
                                            list.add(hostNicData);
                                        }
                                    }
                                }
                        );
                    } catch (IOException e) {
                        log.error("error collecting host basic data: {}", e.getMessage());
                    }
                }
        );

    }

    private String getJsonString(JsonObject json, String key) {
        if (json == null || !json.has(key) || json.get(key).isJsonNull()) {
            return "";
        }
        return json.get(key).getAsString();
    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_HOST_VIC.code();
    }
}
