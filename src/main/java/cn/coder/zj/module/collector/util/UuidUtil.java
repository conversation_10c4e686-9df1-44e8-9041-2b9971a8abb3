package cn.coder.zj.module.collector.util;

import java.util.UUID;

public class UuidUtil {
    /**
     * 生成不带连字符的随机UUID
     *
     * @return 32位UUID字符串
     */
    public static String generateUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成带连字符的随机UUID
     *
     * @return 36位UUID字符串
     */
    public static String generateUuidWithHyphen() {
        return UUID.randomUUID().toString();
    }

    /**
     * 根据指定字符串生成UUID
     *
     * @param name 用于生成UUID的字符串
     * @return 32位UUID字符串
     */
    public static String generateNameBasedUuid(String name) {
        return UUID.nameUUIDFromBytes(name.getBytes()).toString().replace("-", "");
    }
}
