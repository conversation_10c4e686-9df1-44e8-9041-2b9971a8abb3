package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.*;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_SNAPSHOT;
@Slf4j
public class FsOneBasicSnapshotDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VolumeSnapshotData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_SNAPSHOT.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VolumeSnapshotData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_CLOUD_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("vms");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<VolumeSnapshotData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    List<VolumeSnapshotData> vmData = collectSnapshotData(platform, jsonElement,header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理Fusionone云盘数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private List<VolumeSnapshotData> collectSnapshotData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        List<VolumeSnapshotData> list = new ArrayList<>();

        // 基础信息获取
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String cloudUri = getStringFromJson(cloud, "uri");
        String cloudUuid = getStringFromJson(cloud, "uuid");
        String cloudName = getStringFromJson(cloud, "name");

        JsonObject snap = FsApiCacheService.getJsonObject(platformUrl + cloudUri + "/snapshots", null, headers);
        JsonArray rootSnapshots = snap.getAsJsonArray("rootSnapshots");

        for (JsonElement rootSnapshot : rootSnapshots) {
            JsonObject snapshot = rootSnapshot.getAsJsonObject();
            String snapshotUri = getStringFromJson(snapshot, "uri");
            String createTimeStr = getStringFromJson(snapshot, "createTime");
            Date createTime = convertStringToDate(createTimeStr);

            // 创建快照DTO并设置基本属性
            VolumeSnapshotData volumeSnapshotDTO = new VolumeSnapshotData();
            volumeSnapshotDTO.setName(getStringFromJson(snapshot, "name"));
            volumeSnapshotDTO.setDescription(getStringFromJson(snapshot, "description"));
            volumeSnapshotDTO.setCreateTime(createTime);
            volumeSnapshotDTO.setHostUuid(cloudUuid);
            volumeSnapshotDTO.setHostName(cloudName);
            volumeSnapshotDTO.setType("主机快照");
            volumeSnapshotDTO.setLatest("true");
            volumeSnapshotDTO.setPlatformName(platform.getPlatformName());
            volumeSnapshotDTO.setPlatformId(platform.getPlatformId());
            volumeSnapshotDTO.setTypeName("fusionOne");
            volumeSnapshotDTO.setStatus("Enabled");
            volumeSnapshotDTO.setVCreateDate(createTime);
            volumeSnapshotDTO.setVUpdateDate(new Date());
            volumeSnapshotDTO.setFormat("vmsn");
            volumeSnapshotDTO.setDeleted(0);

            // 获取快照详细信息
            JsonObject snapshotsInfo = FsApiCacheService.getJsonObject(platformUrl + snapshotUri, null, headers);
            if (snapshotsInfo != null) {
                JsonArray volSnapshots = snapshotsInfo.getAsJsonArray("volsnapshots");
                if (volSnapshots != null && !volSnapshots.isEmpty()) {
                    JsonObject volSnapshot = volSnapshots.get(0).getAsJsonObject();
                    String datastoreUrn = getStringFromJson(volSnapshot, "datastoreUrn");
                    String volumeUri = getStringFromJson(volSnapshot, "volumeUri");

                    // 设置存储相关信息
                    volumeSnapshotDTO.setUuid(getStringFromJson(volSnapshot, "snapUuid"));
                    volumeSnapshotDTO.setPrimaryStorageUuid(datastoreUrn);

                    // 获取卷信息
                    JsonObject volume = FsApiCacheService.getJsonObject(platformUrl + volumeUri, null, headers);
                    if (volume != null) {
                        volumeSnapshotDTO.setVolumeUuid(getStringFromJson(volume, "uuid"));
                        volumeSnapshotDTO.setVolumeType(getStringFromJson(volume, "type"));

                        String volumeUrl = getStringFromJson(volume, "volumeUrl");
                        int lastSlashIndex = volumeUrl.lastIndexOf('/');
                        volumeSnapshotDTO.setInstallPath(lastSlashIndex == -1 ? volumeUrl :
                                volumeUrl.substring(0, lastSlashIndex));
                    }

                    // 设置大小和内存信息
                    Long size = getLongFromJsonDouble(snapshotsInfo, "snapProvisionSize");
                    volumeSnapshotDTO.setSize(size < 0 ? 0L : size);
                    volumeSnapshotDTO.setIsMemory(getBooleanFromJson(snapshotsInfo, "includingMemorySnapshot"));
                }
            }
            list.add(volumeSnapshotDTO);
        }

        return list;
    }

    public static Date convertStringToDate(String dateString) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateString);
        } catch (ParseException e) {
            return new Date();
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_SNAPSHOT.code();
    }
}
