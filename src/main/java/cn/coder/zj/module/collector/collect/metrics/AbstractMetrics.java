package cn.coder.zj.module.collector.collect.metrics;

import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;

/**
 * 指标收集抽象基类
 * 定义了指标收集的核心接口
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractMetrics {

    /**
     * 预检查方法
     * 在开始数据收集之前进行必要的检查
     * 
     * @param platform 平台信息
     */
    public abstract void preCheck(Platform platform);

    /**
     * 收集数据方法
     * 执行具体的数据收集逻辑
     * 
     * @param message 消息构建器
     */
    public abstract void collectData(ClusterMsg.Message.Builder message);

    /**
     * 支持的协议
     * 返回当前实现类支持的协议类型
     * 
     * @return 协议标识符
     */
    public abstract String supportProtocol();

}
