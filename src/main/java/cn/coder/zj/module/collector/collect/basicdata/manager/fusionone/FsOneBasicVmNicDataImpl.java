package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import java.util.*;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_VM_VIC;
@Slf4j
public class FsOneBasicVmNicDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VmNicData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VM_VIC.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VmNicData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_CLOUD_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("vms");

        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<VmNicData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    List<VmNicData> vmData = collectVmNicInfo(platform, jsonElement,header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理Fusionone云主机网卡数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private List<VmNicData> collectVmNicInfo(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        List<VmNicData> list = new ArrayList<>();
        String platformUrl = platform.getPlatformUrl();

        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonObject cloudInfo = FsApiCacheService.getJsonObject(platformUrl + getStringFromJson(cloud, "uri"), null, headers);
        JsonObject vmConfig = cloudInfo.getAsJsonObject("vmConfig");
        // 网络信息设置
        JsonArray nics = vmConfig.getAsJsonArray("nics");
        if (ObjectUtil.isNull(nics)) return new ArrayList<>();

        for (JsonElement nic : nics) {
            JsonObject nicInfo = nic.getAsJsonObject();
            VmNicData vmNicData = new VmNicData();
            vmNicData.setUuid(getStringFromJson(nicInfo,"urn"));
            vmNicData.setName(getStringFromJson(nicInfo,"name"));
            vmNicData.setHostUuid(getStringFromJson(cloud, "uuid"));
            vmNicData.setIp(getStringFromJson(nicInfo, "ip"));
            JsonArray ips6 = nicInfo.get("ips6").getAsJsonArray();
            if(ips6.size() > 0){
                vmNicData.setIp6(nicInfo.get("ips6").getAsJsonArray().get(0).getAsString());
            }
            vmNicData.setPlatformId(platform.getPlatformId());
            vmNicData.setPlatformName(platform.getPlatformName());
            vmNicData.setMac(getStringFromJson(nicInfo, "mac"));
            vmNicData.setDriver("virtio");
            vmNicData.setInClassicNetwork((byte) 0);
            list.add(vmNicData);
        }

        return list;
    }

    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_VM_VIC.code();
    }
}
