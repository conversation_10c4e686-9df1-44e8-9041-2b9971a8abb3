package cn.coder.zj.module.collector.collect.metrics.disk;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static cn.coder.zj.module.collector.enums.DiskType.PROTOCOL_WIN_HONG_DISK;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.CommonUtil.getBigFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class WinHongDiskImpl extends AbstractMetrics {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();
                Platform platform = (Platform) o;
                String token = platform.getWinHongPlatform().getToken();
                if (token == null) {
                    log.error("平台 {} token为空", platform.getPlatformName());
                    return;
                }
                metricDataList.addAll(vmdata(platform));
                metricDataList.addAll(hostData(platform));
                metricDataList.addAll(storageData(platform));

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.DISK_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                log.info("collectVmData name: {}", Thread.currentThread().getName());
            });
        }
    }

    private List<MetricData> storageData(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);
        Map<String, String> param = new HashMap<>();
        List<MetricData> metricDataList = new ArrayList<>();
        //存储使用率
        JsonArray storageUsePList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_STORAGE_POOLS, param, headers)
                .getAsJsonObject().get("data").getAsJsonArray();
        if (!storageUsePList.isEmpty()) {
            for (JsonElement rate : storageUsePList) {
                MetricData metricData = new MetricData();
                metricData.setPlatformId(platform.getPlatformId());
                JsonObject usageItem = rate.getAsJsonObject();
                String storageUuid = getStringFromJson(usageItem,"id");
                String name = getStringFromJson(usageItem,"name");
                BigDecimal usedCapacity = getBigFromJson(usageItem,  "usedCapacity");
                BigDecimal capacity = getBigFromJson(usageItem,  "capacity");
                BigDecimal memoryFree = usedCapacity.divide(capacity, 2, RoundingMode.HALF_UP);
                metricData.setResourceId(storageUuid);
                metricData.setResourceName(name);
                metricData.setMetricName(DISK_USED_TASK.code());
                metricData.setTimestamps(List.of(System.currentTimeMillis() / 1000));
                metricData.setValues(List.of(memoryFree.doubleValue()));
                metricDataList.add(metricData);
            }
        }
        return metricDataList;
    }

    public List<MetricData> vmdata(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_CLOUDS, null, headers).getAsJsonObject().get("data").getAsJsonArray();

        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();

        //
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);
        List<MetricData> metricDataList = new ArrayList<>();
        for (JsonElement host1 : hostArray) {
            JsonObject host = host1.getAsJsonObject();
            if (!Objects.equals(host.getAsJsonObject().get("status").getAsString(), "1")) {
                continue;
            }
            String uuid = host.get("id").getAsString();

            HashMap<Object, String> domainDevVolId = new HashMap<>();
            String url = platform.getPlatformUrl() + WinHongApiConstant.GET_DOMAIN_DISK_INFO.replace("{domainId}", uuid);

            JsonElement element = getJsonArrayFromApi(url, null, headers);
            if (element != null && element.isJsonObject()) {
                JsonObject obj = element.getAsJsonObject();
                JsonArray diskDevices = obj.getAsJsonArray("diskDevices");
                if (CollUtil.isNotEmpty(diskDevices)) {
                    diskDevices.forEach(disk -> {
                        JsonObject diskJson = disk.getAsJsonObject();
                        String dev = diskJson.get("dev").getAsString();
                        String volId = diskJson.get("volId").getAsString();
                        domainDevVolId.put(dev, volId);
                    });
                }
            }

            // 磁盘io
            Map<String, String> param = new HashMap<>();
            param.put("domainIds", uuid);
            param.put("startTime", startTime);
            param.put("endTime", endTime);
            // 磁盘
            JsonArray diskStateList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HOST_IO_STAT, param, headers).getAsJsonArray();
            if (!diskStateList.isEmpty()) {


                int diskI = 0;
                for (JsonElement rate : diskStateList) {
                    JsonObject usageItem = rate.getAsJsonObject();
                    JsonArray usageItemData = usageItem.get("diskIORsps").getAsJsonArray();
                    for (JsonElement item2 : usageItemData) {
                        JsonObject itemJson = item2.getAsJsonObject();
                        if (!domainDevVolId.containsKey(itemJson.get("devName").getAsString())) {
                            continue;
                        }

//                        metricData.setResourceId(domainDevVolId.get(itemJson.get("devName").getAsString()));
//                        metricData.setResourceName(itemJson.get("devName").getAsString());

                        JsonArray readBytes = itemJson.get("read").getAsJsonObject().get("data").getAsJsonArray();
                        JsonArray writeBytes = itemJson.get("write").getAsJsonObject().get("data").getAsJsonArray();

                        if (!readBytes.isEmpty()) {
                            for (JsonElement item : readBytes) {
                                MetricData metricData = new MetricData();
                                metricData.setResourceId(itemJson.get("domainId").getAsString());
                                metricData.setResourceName(itemJson.get("domainName").getAsString());
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setType("vm");
                                JsonObject cpu = item.getAsJsonObject();
                                metricData.setMetricName(DISK_READ_TASK.code());
                                metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));
                                metricDataList.add(metricData);
                                break;
                            }
                        }
                        if (!writeBytes.isEmpty()) {
                            for (JsonElement item : writeBytes) {
                                MetricData metricData = new MetricData();
                                metricData.setResourceId(itemJson.get("domainId").getAsString());
                                metricData.setResourceName(itemJson.get("domainName").getAsString());
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setType("vm");
                                JsonObject cpu = item.getAsJsonObject();
                                metricData.setMetricName(DISK_WRITE_TASK.code());
                                metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));
                                metricDataList.add(metricData);
                                break;
                            }
                        }
                    }
                }

            }

            // 磁盘ops
            JsonArray diskOpsList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HOST_IO_REQ, param, headers).getAsJsonArray();
            if (!diskOpsList.isEmpty()) {

                int diskI = 0;
                for (JsonElement rate : diskOpsList) {
                    JsonObject usageItem = rate.getAsJsonObject();
                    JsonArray usageItemData = usageItem.get("diskIORsps").getAsJsonArray();
                    for (JsonElement item2 : usageItemData) {
                        JsonObject itemJson = item2.getAsJsonObject();
                        if (!domainDevVolId.containsKey(itemJson.get("devName").getAsString())) {
                            continue;
                        }
//                        metricData.setResourceId(domainDevVolId.get(itemJson.get("devName").getAsString()));
//                        metricData.setResourceName(itemJson.get("devName").getAsString());


                        JsonArray readBytes = itemJson.get("read").getAsJsonObject().get("data").getAsJsonArray();

                        JsonArray writeBytes = itemJson.get("write").getAsJsonObject().get("data").getAsJsonArray();

                        if (!readBytes.isEmpty()) {
                            for (JsonElement item : readBytes) {
                                MetricData metricData = new MetricData();
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setResourceId(itemJson.get("domainId").getAsString());
                                metricData.setResourceName(itemJson.get("domainName").getAsString());
                                metricData.setType("vm");
                                JsonObject cpu = item.getAsJsonObject();
                                metricData.setMetricName(DISK_READ_OPS.code());
                                metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));
                                metricDataList.add(metricData);
                                break;
                            }
                        }
                        if (!writeBytes.isEmpty()) {

                            for (JsonElement item : writeBytes) {
                                MetricData metricData = new MetricData();
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setResourceId(itemJson.get("domainId").getAsString());
                                metricData.setResourceName(itemJson.get("domainName").getAsString());
                                metricData.setType("vm");
                                JsonObject cpu = item.getAsJsonObject();
                                metricData.setMetricName(DISK_WRITE_OPS.code());
                                metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));
                                metricDataList.add(metricData);
                                break;
                            }
                        }
                    }

                }

            }
        }
        return metricDataList;
    }

    public List<MetricData> hostData(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HARDWARE_LIST, null, headers).getAsJsonObject().get("data").getAsJsonArray();;
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();

        List<MetricData> metricDataList = new ArrayList<>();
        for (JsonElement jsonElement : hostArray) {
            JsonObject host = jsonElement.getAsJsonObject();
            String uuid = host.get("id").getAsString();
            String endTime = Convert.toStr(DateUtil.currentSeconds());
            String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);

            Map<String, String> param = new HashMap<>();
            param.put("hostIds", uuid);
            param.put("endTime", endTime);
            param.put("startTime", startTime);
            JsonArray diskIoList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_IO_STAT, param, headers).getAsJsonArray();;
            if (!diskIoList.isEmpty()) {

                int diskI = 0;
                for (JsonElement rate : diskIoList) {
                    JsonObject usageItem = rate.getAsJsonObject();
                    JsonArray usageItemData = usageItem.get("diskIORsps").getAsJsonArray();
                    for (JsonElement item2 : usageItemData) {
                        JsonObject itemJson = item2.getAsJsonObject();
//                        metricData.setResourceId(uuid + diskI++);
//                        metricData.setResourceName(itemJson.get("devName").getAsString());


                        JsonArray readBytes = itemJson.get("read").getAsJsonObject().get("data").getAsJsonArray();
                        JsonArray writeBytes = itemJson.get("write").getAsJsonObject().get("data").getAsJsonArray();
                        if (!readBytes.isEmpty()) {
                            for (JsonElement item : readBytes) {
                                JsonObject cpu = item.getAsJsonObject();
                                MetricData metricData = new MetricData();
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setResourceId(itemJson.get("hostId").getAsString());
                                metricData.setResourceName(itemJson.get("hostName").getAsString());
                                metricData.setType("host");
                                metricData.setMetricName(DISK_READ_TASK.code());
                                metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));
                                metricDataList.add(metricData);
                                break;
                            }

                        }
                        if (!writeBytes.isEmpty()) {
                            for (JsonElement item : writeBytes) {
                                JsonObject cpu = item.getAsJsonObject();
                                MetricData metricData = new MetricData();
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setResourceId(itemJson.get("hostId").getAsString());
                                metricData.setResourceName(itemJson.get("hostName").getAsString());
                                metricData.setType("host");
                                metricData.setMetricName(DISK_WRITE_TASK.code());
                                metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));
                                metricDataList.add(metricData);
                                break;
                            }

                        }
                    }

                }

            }


            JsonArray diskOpsList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_IO_REQ, param, headers).getAsJsonArray();;
            if (!diskOpsList.isEmpty()) {

                int diskI = 0;
                for (JsonElement rate : diskOpsList) {
                    JsonObject usageItem = rate.getAsJsonObject();
                    JsonArray usageItemData = usageItem.get("diskIORsps").getAsJsonArray();
                    for (JsonElement item2 : usageItemData) {
                        JsonObject itemJson = item2.getAsJsonObject();

//                        metricData.setResourceId(uuid + diskI++);
//                        metricData.setResourceName(itemJson.get("devName").getAsString());


                        JsonArray readBytes = itemJson.get("read").getAsJsonObject().get("data").getAsJsonArray();

                        JsonArray writeBytes = itemJson.get("write").getAsJsonObject().get("data").getAsJsonArray();

                        if (!readBytes.isEmpty()) {
                            List<Long> timestamps = new ArrayList<>();
                            List<Double> values = new ArrayList<>();
                            for (JsonElement item : readBytes) {
                                JsonObject cpu = item.getAsJsonObject();
                                timestamps.add( DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000);
                                values.add(cpu.get("value").getAsDouble());
                                MetricData metricData = new MetricData();
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setResourceId(itemJson.get("hostId").getAsString());
                                metricData.setResourceName(itemJson.get("hostName").getAsString());
                                metricData.setType("host");
                                metricData.setMetricName(DISK_READ_OPS.code());
                                metricData.setTimestamps(timestamps);
                                metricData.setValues(values);
                                metricDataList.add(metricData);
                                break;
                            }
                        }
                        if (!writeBytes.isEmpty()) {
                            List<Long> timestamps = new ArrayList<>();
                            List<Double> values = new ArrayList<>();
                            for (JsonElement item : readBytes) {
                                JsonObject cpu = item.getAsJsonObject();
                                timestamps.add( DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000);
                                values.add(cpu.get("value").getAsDouble());
                                MetricData metricData = new MetricData();
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setResourceId(itemJson.get("hostId").getAsString());
                                metricData.setResourceName(itemJson.get("hostName").getAsString());
                                metricData.setType("host");
                                metricData.setMetricName(DISK_WRITE_OPS.code());
                                metricData.setTimestamps(timestamps);
                                metricData.setValues(values);
                                metricDataList.add(metricData);
                                break;
                            }
                        }
                    }
                }

            }
        }
        return metricDataList;
    }
    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            return GSON.fromJson(responseBody, JsonElement.class);
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }


    @Override
    public String supportProtocol() {
        return PROTOCOL_WIN_HONG_DISK.code();
    }
}
