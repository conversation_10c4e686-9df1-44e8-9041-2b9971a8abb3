package cn.coder.zj.module.collector.collect.token.zstack;


import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.util.NetworkUtil;
import cn.coder.zj.module.collector.util.Sha256;
import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.zstack.sdk.*;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;


@Slf4j
public class ZStackClientWrapper {


    private static class PlatformClientInstance {
        private final String platformKey;
        private final String hostname;
        private final int port;
        private final String contextPath;
        private final ReentrantLock instanceLock;
        private volatile boolean configured;
        private volatile long lastConfigTime;

        public PlatformClientInstance(String platformKey, String hostname, int port, String contextPath) {
            this.platformKey = platformKey;
            this.hostname = hostname;
            this.port = port;
            this.contextPath = contextPath;
            this.instanceLock = new ReentrantLock();
            this.configured = false;
            this.lastConfigTime = 0;
        }

        public String getPlatformKey() { return platformKey; }
        public String getHostname() { return hostname; }
        public int getPort() { return port; }
        public String getContextPath() { return contextPath; }
        public ReentrantLock getInstanceLock() { return instanceLock; }
        public boolean isConfigured() { return configured; }
        public void setConfigured(boolean configured) {
            this.configured = configured;
            this.lastConfigTime = System.currentTimeMillis();
        }
        public long getLastConfigTime() { return lastConfigTime; }
    }

    // ==================== 多平台支持相关字段 ====================

    // 平台客户端实例缓存，每个平台独立管理
    private static final ConcurrentHashMap<String, PlatformClientInstance> PLATFORM_INSTANCES = new ConcurrentHashMap<>();

    // 全局锁，用于实例创建的同步
    private static final ReentrantLock GLOBAL_INSTANCE_LOCK = new ReentrantLock();

    // 当前活跃的平台实例（用于跟踪当前ZSClient配置）
    private static volatile PlatformClientInstance currentActiveInstance = null;

    // ==================== 兼容性：保留原有字段 ====================

    // 全局锁，确保ZSClient配置和API调用的原子性（向后兼容）
    private static final ReentrantLock GLOBAL_LOCK = new ReentrantLock();

    // 平台配置缓存，避免重复配置（向后兼容）
    private static final ConcurrentHashMap<String, String> PLATFORM_CONFIG_CACHE = new ConcurrentHashMap<>();

    /**
     * 线程安全的ZStack API执行方法
     * 确保配置和调用的原子性，避免多线程竞争
     */
    public static <T> T executeWithClient(Platform platform, Supplier<T> operation) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform不能为空");
        }

        GLOBAL_LOCK.lock();
        try {
            // 配置ZStack客户端
            configureZStackClient(platform);

            // 执行操作
            return operation.get();

        } catch (Exception e) {
            log.error("ZStack API调用失败: 平台={}, 错误={}", platform.getPlatformName(), e.getMessage());
            throw new RuntimeException("ZSClient操作执行失败: " + e.getMessage(), e);
        } finally {
            GLOBAL_LOCK.unlock();
        }
    }

    /**
     * 异步安全的ZStack API执行方法
     * 在异步线程中调用前会进行额外的验证
     */
    public static <T> T executeWithClientAsync(Platform platform, Supplier<T> operation) {
        return executeWithMultiPlatformClient(platform, operation);
    }

    // ==================== 多平台支持方法 ====================

    /**
     * 多平台安全的ZStack API执行方法
     * 为每个平台提供独立的配置隔离，解决多平台配置冲突问题
     */
    public static <T> T executeWithMultiPlatformClient(Platform platform, Supplier<T> operation) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform不能为空");
        }

        // 获取或创建平台实例
        PlatformClientInstance instance = getOrCreatePlatformInstance(platform);

        // 使用平台专用锁确保该平台的操作原子性
        instance.getInstanceLock().lock();
        try {
            // 确保ZSClient配置为当前平台
            ensurePlatformConfigured(instance);

            // 执行操作
            return operation.get();

        } catch (Exception e) {
            log.error("ZStack多平台API调用失败: 平台={}, 错误={}", platform.getPlatformName(), e.getMessage());
            throw new RuntimeException("ZSClient多平台操作执行失败: " + e.getMessage(), e);
        } finally {
            instance.getInstanceLock().unlock();
        }
    }

    /**
     * 获取或创建平台客户端实例
     */
    private static PlatformClientInstance getOrCreatePlatformInstance(Platform platform) {
        String platformKey = generatePlatformKey(platform);

        PlatformClientInstance instance = PLATFORM_INSTANCES.get(platformKey);
        if (instance == null) {
            GLOBAL_INSTANCE_LOCK.lock();
            try {
                // 双重检查锁定模式
                instance = PLATFORM_INSTANCES.get(platformKey);
                if (instance == null) {
                    // 解析平台URL和端口
                    String originalUrl = platform.getPlatformUrl();
                    if (originalUrl == null || originalUrl.trim().isEmpty()) {
                        throw new IllegalArgumentException("平台URL不能为空: " + platform.getPlatformName());
                    }

                    String hostname = removeProtocolAndPort(originalUrl);
                    String portStr = extractPort(originalUrl);
                    int port = Convert.toInt(portStr);

                    // 验证参数
                    if (hostname == null || hostname.trim().isEmpty()) {
                        throw new IllegalArgumentException("处理后的主机名为空，原始URL: " + originalUrl);
                    }
                    if (port <= 0 || port > 65535) {
                        throw new IllegalArgumentException("端口号无效: " + port);
                    }

                    // 创建新实例
                    instance = new PlatformClientInstance(platformKey, hostname, port, "zstack");
                    PLATFORM_INSTANCES.put(platformKey, instance);

                    log.info("创建新的ZStack平台实例: 平台={}, 主机名={}, 端口={}",
                            platform.getPlatformName(), hostname, port);
                }
            } finally {
                GLOBAL_INSTANCE_LOCK.unlock();
            }
        }

        return instance;
    }


    /**
     * 用户名密码登录方法
     * 线程安全的登录实现，避免配置冲突
     */
    public static LogInByAccountAction.Result loginByAccount(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            try {
                LogInByAccountAction action = new LogInByAccountAction();
                action.accountName = platform.getUsername();
                action.password = Sha256.SHA512(platform.getPassword());

                LogInByAccountAction.Result result = action.call();

                if (result == null || result.value == null || result.value.getInventory() == null) {
                    throw new RuntimeException("登录失败: 返回结果为空，平台: " + platform.getPlatformName());
                }

                log.debug("平台 {} 登录成功，token: {}",
                        platform.getPlatformName(),
                        result.value.getInventory().uuid.substring(0, Math.min(8, result.value.getInventory().uuid.length())) + "...");

                return result;

            } catch (Exception e) {
                log.error("平台 {} 登录失败: {}", platform.getPlatformName(), e.getMessage());
                throw new RuntimeException("ZStack登录失败: " + e.getMessage(), e);
            }
        });
    }


    /**
     * 确保ZSClient配置为指定平台
     * 只有当需要切换到不同平台时才重新配置
     */
    private static void ensurePlatformConfigured(PlatformClientInstance instance) {
        // 检查是否需要重新配置ZSClient
        boolean needReconfigure = false;

        if (currentActiveInstance == null ||
            !currentActiveInstance.getPlatformKey().equals(instance.getPlatformKey()) ||
            !instance.isConfigured()) {
            needReconfigure = true;
        }

        if (needReconfigure) {
            log.debug("切换ZSClient配置到平台: {}", instance.getPlatformKey());

            // 配置ZSClient为当前平台
            ZSClient.configure(new ZSConfig.Builder()
                    .setHostname(instance.getHostname())
                    .setPort(instance.getPort())
                    .setContextPath(instance.getContextPath())
                    .build());

            // 更新状态
            instance.setConfigured(true);
            currentActiveInstance = instance;
        }
    }

    /**
     * 生成平台唯一标识符
     */
    private static String generatePlatformKey(Platform platform) {
        return platform.getPlatformId() + "_" + platform.getPlatformUrl() + "_" + platform.getAkType();
    }

    /**
     * 配置ZStack客户端
     * 增强版本：包含URL验证和详细日志
     */
    private static void configureZStackClient(Platform platform) {
        String platformKey = platform.getPlatformId() + "_" + platform.getPlatformUrl();

        // 检查是否需要重新配置
        if (!PLATFORM_CONFIG_CACHE.containsKey(platformKey)) {
            String originalUrl = platform.getPlatformUrl();
            if (originalUrl == null || originalUrl.trim().isEmpty()) {
                throw new IllegalArgumentException("平台URL不能为空: " + platform.getPlatformName());
            }

            String processedUrl = removeProtocolAndPort(originalUrl);
            String port = extractPort(originalUrl);

            // 验证处理后的URL和端口
            if (processedUrl == null || processedUrl.trim().isEmpty()) {
                throw new IllegalArgumentException("处理后的主机名为空，原始URL: " + originalUrl);
            }

            int portInt;
            try {
                portInt = Convert.toInt(port);
                if (portInt <= 0 || portInt > 65535) {
                    throw new IllegalArgumentException("端口号无效: " + port);
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("端口号解析失败: " + port + ", 原始URL: " + originalUrl);
            }

            log.info("配置ZStack客户端: 平台={}, 原始URL={}, 主机名={}, 端口={}",
                    platform.getPlatformName(), originalUrl, processedUrl, portInt);


            PLATFORM_CONFIG_CACHE.put(platformKey, processedUrl + ":" + port);
            log.info("ZStack客户端配置完成: 平台={}, 配置地址={}:{}",
                    platform.getPlatformName(), processedUrl, portInt);
        } else {
            log.debug("使用缓存的ZStack客户端配置: 平台={}, 配置={}",
                     platform.getPlatformName(), PLATFORM_CONFIG_CACHE.get(platformKey));
        }
    }

    /**
     * 为Action设置认证信息
     * 支持sessionId和AccessKey两种认证方式
     */
    public static void setAuthentication(Object action, Platform platform) {
        if (action == null || platform == null) {
            throw new IllegalArgumentException("Action和Platform不能为空");
        }

        try {
            if (platform.getAkType() == 0) {
                // 使用反射设置sessionId
                action.getClass().getField("sessionId").set(action, platform.getZsTackPlatform().getToken());

            } else {
                // AccessKey认证模式
                if (platform.getUsername() == null || platform.getPassword() == null) {
                    log.error("AccessKey认证信息不完整: 平台={}, username={}, hasPassword={}",
                             platform.getPlatformName(), platform.getUsername(),
                             platform.getPassword() != null && !platform.getPassword().isEmpty());
                    throw new RuntimeException("AccessKey认证信息不完整，平台: " + platform.getPlatformName());
                }
                action.getClass().getField("accessKeyId").set(action, platform.getUsername());
                action.getClass().getField("accessKeySecret").set(action, platform.getPassword());
            }

        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("设置认证信息失败: " + e.getMessage(), e);
        }
    }



    /**
     * 验证token是否有效
     */
    private static boolean isTokenValid(Platform platform, String token) {
        try {
            // 使用一个轻量级的API调用来验证token有效性
            QueryZoneAction testAction = new QueryZoneAction();
            testAction.sessionId = token;

            // 配置客户端
            configureZStackClient(platform);

            // 执行测试调用
            testAction.call();

            log.debug("Token验证成功: 平台={}", platform.getPlatformName());
            return true;

        } catch (Exception e) {
            String errorMsg = e.getMessage();
            if (errorMsg != null && (errorMsg.contains("session") ||
                                   errorMsg.contains("ID.1001") ||
                                   errorMsg.contains("expired"))) {
                log.debug("Token已过期: 平台={}, 错误={}", platform.getPlatformName(), errorMsg);
                return false;
            }
            return true;
        }
    }



    /**
     * 异步调用前的平台验证
     */
    private static void validatePlatformForAsync(Platform platform) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform不能为空");
        }

        // 验证网络连通性
        String processedUrl = removeProtocolAndPort(platform.getPlatformUrl());
        String port = extractPort(platform.getPlatformUrl());

        if (!NetworkUtil.isTcpPortOpen(processedUrl, Convert.toInt(port))) {
            throw new RuntimeException("平台网络不可达: " + platform.getPlatformName() + " " + processedUrl + ":" + port);
        }

        log.debug("平台 {} 异步调用验证通过", platform.getPlatformName());
    }

    /**
     * 异步安全的安全组查询
     */
    public static QuerySecurityGroupAction.Result querySecurityGroupsAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QuerySecurityGroupAction action = new QuerySecurityGroupAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的虚拟机查询
     */
    public static QueryVmInstanceAction.Result queryVmInstancesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryVmInstanceAction action = new QueryVmInstanceAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    // ==================== 基础数据收集相关API ====================

    /**
     * 异步安全的主机查询
     */
    public static QueryHostAction.Result queryHostsAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的区域查询（带重试机制）
     */
    public static QueryZoneAction.Result queryZonesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryZoneAction action = new QueryZoneAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }


    /**
     * 异步安全的集群查询
     */
    public static QueryClusterAction.Result queryClustersAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryClusterAction action = new QueryClusterAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的存储卷查询
     */
    public static QueryVolumeAction.Result queryVolumesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryVolumeAction action = new QueryVolumeAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的主存储查询
     */
    public static QueryPrimaryStorageAction.Result queryPrimaryStoragesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryPrimaryStorageAction action = new QueryPrimaryStorageAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的镜像查询
     */
    public static QueryImageAction.Result queryImagesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryImageAction action = new QueryImageAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的VPC路由器查询
     */
    public static QueryVpcRouterAction.Result queryVpcRoutersAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryVpcRouterAction action = new QueryVpcRouterAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的L2网络查询
     */
    public static QueryL2NetworkAction.Result queryL2NetworksAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryL2NetworkAction action = new QueryL2NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的L3网络查询
     */
    public static QueryL3NetworkAction.Result queryL3NetworksAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryL3NetworkAction action = new QueryL3NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的卷快照查询
     */
    public static QueryVolumeSnapshotAction.Result queryVolumeSnapshotsAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryVolumeSnapshotAction action = new QueryVolumeSnapshotAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的主机网络接口查询
     */
    public static QueryHostNetworkInterfaceAction.Result queryHostNetworkInterfacesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryHostNetworkInterfaceAction action = new QueryHostNetworkInterfaceAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的虚拟机Guest Tools信息查询
     */
    public static GetVmGuestToolsInfoAction.Result getVmGuestToolsInfoAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            GetVmGuestToolsInfoAction action = new GetVmGuestToolsInfoAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    // ==================== 指标数据收集相关API ====================

    /**
     * 异步安全的指标数据查询
     */
    public static GetMetricDataAction.Result getMetricDataAsync(Platform platform, String namespace, String metricName, List<String> labels) {
        return executeWithClientAsync(platform, () -> {
            GetMetricDataAction action = new GetMetricDataAction();
            action.namespace = namespace;
            action.metricName = metricName;
            action.labels = labels;

            action.period = 20;
            action.startTime = System.currentTimeMillis() / 1000 - 60;  // 60秒前
            action.endTime = System.currentTimeMillis() / 1000;


            setAuthentication(action, platform);
            return action.call();
        });
    }


    // ==================== 带条件查询的异步安全方法 ====================

    /**
     * 异步安全的主机查询（带条件）
     */
    public static QueryHostAction.Result queryHostsWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的集群查询（带条件）
     */
    public static QueryClusterAction.Result queryClustersWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryClusterAction action = new QueryClusterAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的用户标签查询
     * 支持根据资源UUID和资源类型查询用户标签
     *
     * @param platform 平台信息
     * @param resourceUuid 资源UUID
     * @param resourceType 资源类型（如"VolumeVO", "VmInstanceVO", "HostVO"等）
     * @return QueryUserTagAction.Result 查询结果
     */
    public static QueryUserTagAction.Result queryUserTagsAsync(Platform platform, String resourceUuid, String resourceType) {
        return executeWithClientAsync(platform, () -> {
            QueryUserTagAction action = new QueryUserTagAction();

            // 设置查询条件
            if (resourceUuid != null && resourceType != null) {
                action.conditions = java.util.Arrays.asList(
                    "resourceUuid=" + resourceUuid,
                    "resourceType=" + resourceType
                );
            }

            // 设置认证信息（自动根据platform.getAkType()选择认证方式）
            setAuthentication(action, platform);

            return action.call();
        });
    }

    /**
     * 异步安全的用户标签查询（带自定义条件）
     * 支持自定义查询条件的用户标签查询
     *
     * @param platform 平台信息
     * @param conditions 查询条件列表
     * @return QueryUserTagAction.Result 查询结果
     */
    public static QueryUserTagAction.Result queryUserTagsWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryUserTagAction action = new QueryUserTagAction();

            // 设置查询条件
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }

            // 设置认证信息（自动根据platform.getAkType()选择认证方式）
            setAuthentication(action, platform);

            return action.call();
        });
    }

    /**
     * 异步安全的L3网络查询（带条件）
     */
    public static QueryL3NetworkAction.Result queryL3NetworksWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryL3NetworkAction action = new QueryL3NetworkAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }



    /**
     * 异步安全的主机网络接口查询（带条件）
     */
    public static QueryHostNetworkInterfaceAction.Result queryHostNetworkInterfacesWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryHostNetworkInterfaceAction action = new QueryHostNetworkInterfaceAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的存储卷查询（带条件）
     */
    public static QueryVolumeAction.Result queryVolumesWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryVolumeAction action = new QueryVolumeAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的卷快照查询（带条件）
     */
    public static QueryVolumeSnapshotAction.Result queryVolumeSnapshotsWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryVolumeSnapshotAction action = new QueryVolumeSnapshotAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的虚拟机Guest Tools信息查询（带UUID）
     */
    public static GetVmGuestToolsInfoAction.Result getVmGuestToolsInfoWithUuidAsync(Platform platform, String vmUuid) {
        return executeWithClientAsync(platform, () -> {
            GetVmGuestToolsInfoAction action = new GetVmGuestToolsInfoAction();
            if (vmUuid != null && !vmUuid.trim().isEmpty()) {
                action.uuid = vmUuid;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    // ==================== 自定义指标查询方法 ====================

    /**
     * 异步安全的VM指标数据查询
     */
    public static GetMetricDataAction.Result getVmMetricDataAsync(Platform platform, String metricName, String vmUuid) {
        return getMetricDataAsync(platform, "ZStack/VM", metricName, List.of("VMUuid=" + vmUuid));
    }

    /**
     * 异步安全的主机指标数据查询
     */
    public static GetMetricDataAction.Result getHostMetricDataAsync(Platform platform, String metricName, String hostUuid) {
        return getMetricDataAsync(platform, "ZStack/Host", metricName, List.of("HostUuid=" + hostUuid));
    }

    /**
     * 异步安全的主存储指标数据查询
     */
    public static GetMetricDataAction.Result getPrimaryStorageMetricDataAsync(Platform platform, String metricName, String storageUuid) {
        return getMetricDataAsync(platform, "ZStack/PrimaryStorage", metricName, List.of("PrimaryStorageUuid=" + storageUuid));
    }

    /**
     * 验证登录状态
     */
    public static boolean validateLogin(Platform platform) {
        try {
            if (platform.getAkType() == 0) {
                // sessionId模式：尝试调用一个简单的API验证token有效性
                QueryZoneAction action = new QueryZoneAction();
                setAuthentication(action, platform);
                action.call();
                return true;
            } else {
                // AccessKey模式：尝试查询虚拟机
                queryVmInstancesAsync(platform);
                return true;
            }
        } catch (Exception e) {
            log.debug("登录验证失败: 平台={}, 错误={}", platform.getPlatformName(), e.getMessage());
            return false;
        }
    }

    /**
     * 详细的认证信息诊断
     */
    public static void diagnoseAuthentication(Platform platform) {
        log.info("=== 认证信息诊断开始 ===");
        log.info("平台名称: {}", platform.getPlatformName());
        log.info("平台ID: {}", platform.getPlatformId());
        log.info("平台URL: {}", platform.getPlatformUrl());
        log.info("认证类型 (akType): {}", platform.getAkType());
        log.info("平台状态: {}", platform.getState());

        if (platform.getAkType() == 0) {
            // sessionId模式
            log.info("使用sessionId认证模式");
            if (platform.getZsTackPlatform() != null) {
                String token = platform.getZsTackPlatform().getToken();
                log.info("Token存在: {}", token != null);
                if (token != null) {
                    log.info("Token长度: {}", token.length());
                    log.info("Token前缀: {}...", token.substring(0, Math.min(16, token.length())));
                }
            } else {
                log.error("ZsTackPlatform对象为空");
            }
        } else {
            // AccessKey模式
            log.info("使用AccessKey认证模式");
            log.info("Username (AccessKey ID): {}", platform.getUsername());
            log.info("Password存在: {}", platform.getPassword() != null && !platform.getPassword().isEmpty());
            if (platform.getPassword() != null) {
                log.info("Password长度: {}", platform.getPassword().length());
                log.info("Password前缀: {}...", platform.getPassword().substring(0, Math.min(8, platform.getPassword().length())));
            }
        }
        log.info("=== 认证信息诊断结束 ===");
    }

    // ==================== 多平台版本的API方法 ====================

    /**
     * 多平台安全的虚拟机查询
     */
    public static QueryVmInstanceAction.Result queryVmInstancesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryVmInstanceAction action = new QueryVmInstanceAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的主机查询
     */
    public static QueryHostAction.Result queryHostsMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的区域查询
     */
    public static QueryZoneAction.Result queryZonesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryZoneAction action = new QueryZoneAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的安全组查询
     */
    public static QuerySecurityGroupAction.Result querySecurityGroupsMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QuerySecurityGroupAction action = new QuerySecurityGroupAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的集群查询
     */
    public static QueryClusterAction.Result queryClustersMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryClusterAction action = new QueryClusterAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的存储卷查询
     */
    public static QueryVolumeAction.Result queryVolumesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryVolumeAction action = new QueryVolumeAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的主存储查询
     */
    public static QueryPrimaryStorageAction.Result queryPrimaryStoragesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryPrimaryStorageAction action = new QueryPrimaryStorageAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的镜像查询
     */
    public static QueryImageAction.Result queryImagesMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryImageAction action = new QueryImageAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的L2网络查询
     */
    public static QueryL2NetworkAction.Result queryL2NetworksMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryL2NetworkAction action = new QueryL2NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 多平台安全的L3网络查询
     */
    public static QueryL3NetworkAction.Result queryL3NetworksMultiPlatform(Platform platform) {
        return executeWithMultiPlatformClient(platform, () -> {
            QueryL3NetworkAction action = new QueryL3NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 验证登录状态（多平台版本）
     */
    public static boolean validateLoginMultiPlatform(Platform platform) {
        try {
            if (platform.getAkType() == 0) {
                // sessionId模式：尝试调用一个简单的API验证token有效性
                queryZonesMultiPlatform(platform);
                return true;
            } else {
                // AccessKey模式：尝试查询虚拟机
                queryVmInstancesMultiPlatform(platform);
                return true;
            }
        } catch (Exception e) {
            log.debug("多平台登录验证失败: 平台={}, 错误={}", platform.getPlatformName(), e.getMessage());
            return false;
        }
    }

    /**
     * 获取平台实例统计信息
     */
    public static String getPlatformInstanceStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("ZStack多平台实例统计:\n");
        stats.append("总实例数: ").append(PLATFORM_INSTANCES.size()).append("\n");

        for (PlatformClientInstance instance : PLATFORM_INSTANCES.values()) {
            stats.append("- 平台: ").append(instance.getPlatformKey())
                 .append(", 配置状态: ").append(instance.isConfigured())
                 .append(", 最后配置时间: ").append(instance.getLastConfigTime())
                 .append("\n");
        }

        if (currentActiveInstance != null) {
            stats.append("当前活跃实例: ").append(currentActiveInstance.getPlatformKey()).append("\n");
        }

        return stats.toString();
    }

    /**
     * 清理指定平台的实例缓存
     */
    public static void clearPlatformInstance(Platform platform) {
        String platformKey = generatePlatformKey(platform);
        PlatformClientInstance removed = PLATFORM_INSTANCES.remove(platformKey);
        if (removed != null) {
            log.info("已清理平台实例缓存: {}", platformKey);

            // 如果清理的是当前活跃实例，重置当前活跃实例
            if (currentActiveInstance != null &&
                currentActiveInstance.getPlatformKey().equals(platformKey)) {
                currentActiveInstance = null;
            }
        }
    }

    /**
     * 清理所有平台实例缓存
     */
    public static void clearAllPlatformInstances() {
        GLOBAL_INSTANCE_LOCK.lock();
        try {
            int count = PLATFORM_INSTANCES.size();
            PLATFORM_INSTANCES.clear();
            currentActiveInstance = null;
            log.info("已清理所有平台实例缓存，共{}个实例", count);
        } finally {
            GLOBAL_INSTANCE_LOCK.unlock();
        }
    }

    // URL处理工具方法
    private static String removeProtocolAndPort(String url) {
        return url.replaceAll("^https?://", "").replaceAll(":\\d+.*$", "");
    }

    private static String extractPort(String url) {
        if (url.contains(":") && url.matches(".*:\\d+.*")) {
            return url.replaceAll("^.*:(\\d+).*$", "$1");
        }
        return url.startsWith("https://") ? "443" : "8080";
    }
}
