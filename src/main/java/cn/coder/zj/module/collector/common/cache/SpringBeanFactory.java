package cn.coder.zj.module.collector.common.cache;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
public final class SpringBeanFactory implements ApplicationContextAware {

    private static class ApplicationContextHolder {
        private static ApplicationContext context;
    }

    /**
     * 获取指定类型的Bean实例
     */
    public static <T> T getBean(Class<T> clazz) {
        return getContext().getBean(clazz);
    }

    /**
     * 获取指定名称和类型的Bean实例
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        return getContext().getBean(name, clazz);
    }

    private static ApplicationContext getContext() {
        if (ApplicationContextHolder.context == null) {
            throw new IllegalStateException("ApplicationContext initialization failed!");
        }
        return ApplicationContextHolder.context;
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        synchronized (ApplicationContextHolder.class) {
            if (ApplicationContextHolder.context != null) {
                throw new IllegalStateException("ApplicationContext has already been initialized and cannot be set repeatedly!");
            }
            ApplicationContextHolder.context = applicationContext;
        }
    }

}
