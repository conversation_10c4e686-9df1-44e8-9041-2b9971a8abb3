package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL2Data;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL3Data;
import cn.iocoder.zj.framework.common.enums.MetricsType;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_NET;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_NET;

@Slf4j
public class IsTackBasicNetDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK2.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformInfo : platformList) {
            Platform platform = (Platform) platformInfo;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(TimeUtils.withExecutionTime(
                    String.format("プラットフォーム[%s]のデータ処理", platform.getPlatformName()), IsTackBasicNetDataImpl.class.getSimpleName(), startTime, () -> {
                        try {
                            // L2网卡数据收集
                            List<NetWorkL2Data> listL2 = new ArrayList<>();
                            collectDataL2(platform, listL2);
                            if (!listL2.isEmpty()) {
                                message.setType(ClusterMsg.MessageType.BASIC);
                                message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder()
                                        .basicDataMap(listL2)
                                        .metricsName(BASIC_NET.code())
                                        .metricsType(MetricsType.BASIC_NET_L2.code())
                                        .build()));
                                message.setTime(System.currentTimeMillis());
                                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                            }

                            // L3网卡数据收集
                            List<NetWorkL3Data> listL3 = new ArrayList<>();
                            collectDataL3(platform, listL3);
                            log.debug("平台 {} L3网卡数据收集完成，数量：{}", platform.getPlatformName(), listL3.size());

                            if (!listL3.isEmpty()) {
                                ChannelHandlerContext ctx = CacheService.getCtx("ctx");
                                ClusterMsg.Message.Builder messageL3 = ClusterMsg.Message.newBuilder()
                                        .setType(ClusterMsg.MessageType.BASIC)
                                        .setData(GsonUtil.GSON.toJson(BasicCollectData.builder()
                                                .basicDataMap(listL3)
                                                .metricsName(BASIC_NET.code())
                                                .metricsType(MetricsType.BASIC_NET_L3.code())
                                                .build()));
                                messageL3.setTime(System.currentTimeMillis());
                                ctx.writeAndFlush(messageL3);
                            }

                            double costTime = (System.currentTimeMillis() - startTime) / 1000.0;
                            log.info("平台 {} 网络数据收集完成，耗时：{} 秒", platform.getPlatformName(), String.format("%.2f", costTime));
                        } catch (Exception e) {
                            log.error("平台 {} 网络数据收集异常：{}", platform.getPlatformName(), e.getMessage(), e);
                        }
                    }));
        }
    }

    private void collectDataL2(Platform platform, List<NetWorkL2Data> listL2) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_V_NETWORK_LIST, param, null)) {
            JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
            if (asJsonArray.isEmpty()) {
                log.warn("平台 {} L2网卡数据为空", platform.getPlatformName());
                return;
            }
            asJsonArray.forEach(jsonElement -> {
                try {
                    JsonObject resultObj = jsonElement.getAsJsonObject();
                    NetWorkL2Data netWorkL2Data = NetWorkL2Data.builder()
                            .name(resultObj.get("name").getAsString())
                            .uuid(resultObj.get("uuid").getAsString())
                            .platformName(platform.getPlatformName())
                            .platformId(platform.getPlatformId())
                            .regionId(platform.getRegionId())
                            .type(resultObj.get("network_type").getAsString().equals("vlan")
                                    ? "L2VlanNetwork"
                                    : "L2NoVlanNetwork")
                            .typeName("istack")
                            .vlan(resultObj.get("vlan_id").toString())
                            .virtualNetworkId(Integer.valueOf(resultObj.get("vlan_id").toString()))
                            .build();
                    listL2.add(netWorkL2Data);
                } catch (Exception e) {
                    log.error("平台 {} L2网卡数据解析异常：{}", platform.getPlatformName(), e.getMessage());
                }
            });
        } catch (IOException e) {
            log.error("平台 {} L2网卡数据获取失败：{}", platform.getPlatformName(), e.getMessage());
        }
    }

    private void collectDataL3(Platform platform, List<NetWorkL3Data> listL3) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_V_NETWORK3_LIST, param, null)) {
            JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
            if (asJsonArray.isEmpty()) {
                log.warn("平台 {} L3网卡数据为空", platform.getPlatformName());
                return;
            }
            asJsonArray.forEach(jsonElement -> {
                try {
                    JsonObject resultObj = jsonElement.getAsJsonObject();
                    NetWorkL3Data netWorkL3Data = new NetWorkL3Data();
                    netWorkL3Data.setUuid(resultObj.get("uuid").getAsString());
                    netWorkL3Data.setName(resultObj.get("name").getAsString());
                    if (resultObj.get("dns_nameservers").getAsJsonArray().size() > 0) {
                        netWorkL3Data.setDns(resultObj.get("dns_nameservers").getAsJsonArray().get(0).toString());
                    }
                    netWorkL3Data.setType("L3BasicNetwork");
                    String cidr = getNetworkFromCIDR(resultObj.get("cidr").getAsString());
                    netWorkL3Data.setStartIp(cidr + ".1");
                    netWorkL3Data.setEndIp(cidr + ".254");
                    netWorkL3Data.setNetworkSegment(netWorkL3Data.getStartIp() + "-" + netWorkL3Data.getEndIp());
                    netWorkL3Data.setGateway(resultObj.get("gateway_ip").getAsString());
                    netWorkL3Data.setNetworkCidr(resultObj.get("cidr").getAsString());

                    netDhcp(platform, resultObj.get("uuid").getAsString(), netWorkL3Data);

                    netWorkL3Data.setPlatformId(platform.getPlatformId());
                    netWorkL3Data.setPlatformName(platform.getPlatformName());
                    netWorkL3Data.setCreateTime(DateUtil.parse(Convert.toStr(resultObj.get("create_time").getAsString())));
                    netWorkL3Data.setTypeName("istack");
                    listL3.add(netWorkL3Data);
                } catch (Exception e) {
                    log.error("平台 {} L3网卡数据解析异常：{}", platform.getPlatformName(), e.getMessage());
                }
            });
        } catch (IOException e) {
            log.error("平台 {} L3网卡数据获取失败：{}", platform.getPlatformName(), e.getMessage());
        }
    }

    private void netDhcp(Platform platform, String uuid, NetWorkL3Data netWorkL3Data) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        param.put("subnet_uuid", uuid);
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_V_DHCP_LIST, param, null)) {
            JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
            if (asJsonArray.isEmpty()) {
                log.debug("平台 {} DHCP数据为空，uuid: {}", platform.getPlatformName(), uuid);
                return;
            }
            asJsonArray.forEach(jsonElement -> {
                try {
                    JsonObject resultObj = jsonElement.getAsJsonObject();
                    if (resultObj.get("device_owner").getAsString().equals("network:dhcp")) {
                        netWorkL3Data.setNextHopIp(resultObj.get("ipv4_address").getAsString());
                    }
                } catch (Exception e) {
                    log.error("平台 {} DHCP数据解析异常：{}", platform.getPlatformName(), e.getMessage());
                }
            });
        } catch (IOException e) {
            log.error("平台 {} DHCP数据获取失败：{}", platform.getPlatformName(), e.getMessage());
        }
    }


    private String getNetworkFromCIDR(String cidr) {
        if (cidr == null || cidr.isEmpty()) {
            return "";
        }
        String ipPart = cidr.split("/")[0];
        String[] parts = ipPart.split("\\.");
        if (parts.length >= 3) {
            return parts[0] + "." + parts[1] + "." + parts[2];
        }
        return "";
    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_NET.code();
    }
}
