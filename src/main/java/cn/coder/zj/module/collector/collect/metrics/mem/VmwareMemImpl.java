package cn.coder.zj.module.collector.collect.metrics.mem;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.vmware.VmwareMetricsUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.vmware.vim25.HostHardwareSummary;
import com.vmware.vim25.mo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static cn.coder.zj.module.collector.enums.MemType.PROTOCOL_VMWARE_MEM;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.service.vmware.HostComputerResourceSummary.getHostList;
import static cn.coder.zj.module.collector.service.vmware.VmComputerResourceSummary.getVmList;

@Slf4j
public class VmwareMemImpl extends AbstractMetrics {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {
        // 预检查逻辑，暂时不需要实现
    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            Platform platform = (Platform) o;
            taskExecutor.execute(() -> {
                        List<MetricData> metricDataList = new ArrayList<>();
                        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();

                        if (serviceInstance == null) {
                            log.error("平台 {} serviceInstance为空", platform.getPlatformName());
                            return;
                        }
                        // 宿主机指标
                        List<MetricData> hostMetrics = hostMemMetric(serviceInstance, platform);
                        if (hostMetrics != null && !hostMetrics.isEmpty()) {
                            metricDataList.addAll(hostMetrics);
                        }

                        // 虚拟机指标
                        List<MetricData> vmMetrics = vmMemMetric(serviceInstance, platform);
                        if (vmMetrics != null && !vmMetrics.isEmpty()) {
                            metricDataList.addAll(vmMetrics);
                        }

                        message.setData(new Gson().toJson(metricDataList));
                        message.setTime(System.currentTimeMillis());
                        message.setType(ClusterMsg.MessageType.MEM_TASK);
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
            );
        }
    }

    private List<MetricData> hostMemMetric(ServiceInstance serviceInstance, Platform platform) {
        List<MetricData> metricDataList = new ArrayList<>();
        try {
            // 宿主机列表
            List<HostSystem> hostSystems = getHostList(serviceInstance);

            if (hostSystems == null || hostSystems.isEmpty()) {
                log.warn("平台 {} 未找到宿主机系统", platform.getPlatformName());
                return metricDataList;
            }

            // 循环宿主机列表
            for (HostSystem hostSystem : hostSystems) {
                String clusterUuid = hostSystem.getParent().getMOR().getVal();
                HostHardwareSummary hostHardwareSummary = hostSystem.getSummary().getHardware();
                String vms = hostSystem.getMOR().getVal();
                String uuid = clusterUuid + hostHardwareSummary.getUuid() + vms;
                String hostName = hostSystem.getName();

                ManagedEntity managedEntity = new InventoryNavigator(serviceInstance.getRootFolder())
                        .searchManagedEntity("HostSystem", hostName);

                if (managedEntity == null) {
                    log.warn("平台{} 找不到宿主机: {}", platform.getPlatformName(), hostName);
                    continue;
                }

                // 获取宿主机总内存
                long totalMemoryBytes = hostHardwareSummary.getMemorySize();

                // 收集内存使用指标
                MetricData usedMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, hostName, MEM_USED_TASK.code());
                VmwareMetricsUtil.collectMetric(
                        serviceInstance,
                        platform,
                        usedMetric,
                        managedEntity,
                        hostName,
                        "mem.consumed.average",
                        VmwareMetricsUtil::memValueConverter,
                        "host"
                );

                // 如果收集到了使用量，计算空闲量并添加到结果中
                if (!usedMetric.getValues().isEmpty()) {
                    metricDataList.add(usedMetric);

                    // 计算内存空闲量
                    MetricData freeMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, hostName, MEM_FREE_TASK.code());
                    calculateFreeMemory(freeMetric, usedMetric, totalMemoryBytes);
                    metricDataList.add(freeMetric);

                    MetricData data = BeanUtil.copyProperties(freeMetric, MetricData.class);
                    calculateUseage(data, usedMetric, totalMemoryBytes);
                    data.setMetricName(MEM_USAGE_TASK.code());

                    metricDataList.add(data);
                }
            }

            return metricDataList;
        } catch (Exception e) {
            log.error("收集宿主机内存性能数据失败: {}", e.getMessage(), e);
        }

        return metricDataList;
    }

    private List<MetricData> vmMemMetric(ServiceInstance serviceInstance, Platform platform) {
        List<MetricData> metricDataList = new ArrayList<>();
        try {
            // 虚拟机列表
            List<VirtualMachine> virtualMachines = getVmList(serviceInstance);

            if (virtualMachines == null || virtualMachines.isEmpty()) {
                log.warn("平台 {} 未找到虚拟机", platform.getPlatformName());
                return metricDataList;
            }

            for (VirtualMachine virtualMachine : virtualMachines) {
                // 跳过模板和未开机的虚拟机
                if (virtualMachine.getConfig() != null && virtualMachine.getConfig().isTemplate()) {
                    continue;
                }

                if (virtualMachine.getRuntime().getPowerState() != com.vmware.vim25.VirtualMachinePowerState.poweredOn) {
                    continue;
                }

                String vms = virtualMachine.getMOR().getVal();
                String uuid = vms + "-" + virtualMachine.getSummary().getConfig().getUuid();
                String vmName = virtualMachine.getName();

                ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                        .searchManagedEntity("VirtualMachine", vmName);

                if (managedEntities == null) {
                    log.warn("平台{} 找不到虚拟机: {}", platform.getPlatformName(), vmName);
                    continue;
                }

                // 获取虚拟机总内存 (MB转换为B)
                long totalMemoryBytes = virtualMachine.getSummary().getConfig().getMemorySizeMB() * 1024L * 1024L;

                // 收集内存使用指标
                MetricData usedMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, vmName, MEM_USED_TASK.code());
                VmwareMetricsUtil.collectMetric(
                        serviceInstance,
                        platform,
                        usedMetric,
                        managedEntities,
                        vmName,
                        "mem.active.average",
                        VmwareMetricsUtil::memValueConverter,
                        "vm"
                );

                // 如果收集到了使用量，计算空闲量并添加到结果中
                if (!usedMetric.getValues().isEmpty()) {
                    metricDataList.add(usedMetric);

                    // 计算内存空闲量
                    MetricData freeMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, vmName, MEM_FREE_TASK.code());
                    calculateFreeMemory(freeMetric, usedMetric, totalMemoryBytes);
                    metricDataList.add(freeMetric);

                    MetricData data = BeanUtil.copyProperties(freeMetric, MetricData.class);
                    calculateUseage(data, usedMetric, totalMemoryBytes);
                    data.setMetricName(MEM_USAGE_TASK.code());
                    metricDataList.add(data);
                }
            }

            return metricDataList;
        } catch (Exception e) {
            log.error("收集虚拟机内存性能数据失败: {}", e.getMessage(), e);
        }

        return metricDataList;
    }

    private void calculateFreeMemory(MetricData freeMetric, MetricData usedMetric, long totalMemoryBytes) {
        // 确保时间戳和值的列表大小相同
        int size = usedMetric.getTimestamps().size();
        for (int i = 0; i < size; i++) {
            Long timestamp = usedMetric.getTimestamps().get(i);
            Double usedValue = usedMetric.getValues().get(i);

            // 计算空闲内存 = 总内存 - 已用内存
            Double freeValue = Math.max(0, totalMemoryBytes - usedValue);

            freeMetric.getTimestamps().add(timestamp);
            freeMetric.getValues().add(freeValue);
        }
    }

    private void calculateUseage(MetricData freeMetric, MetricData usedMetric, long totalMemoryBytes) {
        int size = usedMetric.getTimestamps().size();
        for (int i = 0; i < size; i++) {
            Long timestamp = usedMetric.getTimestamps().get(i);
            Double usedValue = usedMetric.getValues().get(i);
            Double aDouble = Double.valueOf(totalMemoryBytes);
            double div = NumberUtil.div(usedValue, aDouble, 2) * 100;
            freeMetric.getValues().clear();
            freeMetric.getTimestamps().clear();
            freeMetric.setValues(Collections.singletonList(div));
            freeMetric.setTimestamps(Collections.singletonList(timestamp));
        }
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_VMWARE_MEM.code();
    }
}
