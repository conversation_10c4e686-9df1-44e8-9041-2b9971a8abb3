package cn.coder.zj.module.collector.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public enum MemType {

    PROTOCOL_ZS_TACK_MEM("zstackMem", "zstack内存"),
    PROTOCOL_FUSION_COMPUTE_MEM("fusionComputeMem", "fusionCompute内存"),
    PROTOCOL_IS_TACK_MEM("istackMem", "istack内存"),
    PROTOCOL_VMWARE_MEM("vmwareMem", "vmware内存"),
    PROTOCOL_SXF_MEM("sxfMem", "深信服内存"),
    PROTOCOL_WIN_HONG_MEM("winHongMem", "winHong内存"),
    PROTOCOL_IN_SPUR_MEM("inSpurMem", "浪潮内存");


    private final String code;

    private final String desc;


    public static MemType fromCode(String code) {
        for (MemType typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String code() {
        return this.code;
    }

    public static List<String> getAllMemTypeCodes() {
        List<String> codes = new ArrayList<>();
        for (MemType memType : MemType.values()) {
            codes.add(memType.code());
        }
        return codes;
    }
}
