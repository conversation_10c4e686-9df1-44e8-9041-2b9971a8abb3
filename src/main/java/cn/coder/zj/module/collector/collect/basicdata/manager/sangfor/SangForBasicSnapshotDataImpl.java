package cn.coder.zj.module.collector.collect.basicdata.manager.sangfor;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SNAPSHOT;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_SXF_SNAPSHOT;

@Slf4j
public class SangForBasicSnapshotDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VolumeSnapshotData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_SNAPSHOT.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VolumeSnapshotData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie,"CSRFPreventionToken",token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_CLOUDS, null, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<VolumeSnapshotData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    List<VolumeSnapshotData> vmData = collectSnapshotData(platform, jsonElement,headers);
                    if (CollUtil.isNotEmpty(vmData)) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理深信服快照数据异常, hostMap: {}, error: {}", jsonElement.toString(), e.getMessage());
                }
            }
        }
        return dataList;
    }

    private List<VolumeSnapshotData> collectSnapshotData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
        List<VolumeSnapshotData> list = new ArrayList<>();

        String vmid = getStringFromJson(jsonObject, "vmid", "");
        String hostUuid = StrUtil.isNotEmpty(getStringFromJson(jsonObject,"uuid","")) ?
                getStringFromJson(jsonObject,"uuid","") : getStringFromJson(jsonObject,"vmid","");
        String hostName = getStringFromJson(jsonObject, "name", "");

        JsonArray snapshots = getJsonArrayFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_VM_SNAPSHOT_LIST.replace("{vmid}", vmid), null, headers);
        if(CollUtil.isEmpty(snapshots)) return list;

        for (JsonElement snapshot : snapshots) {
            JsonObject vmSnapshot = snapshot.getAsJsonObject();
            String snapId = getStringFromJson(vmSnapshot, "snapid", "");
            if(StrUtil.isEmpty(snapId)) continue;

            VolumeSnapshotData volumeSnapshotDTO = new VolumeSnapshotData();
            volumeSnapshotDTO.setUuid(snapId);
            volumeSnapshotDTO.setName(getStringFromJson(vmSnapshot, "name", ""));
            volumeSnapshotDTO.setHostUuid(hostUuid);
            volumeSnapshotDTO.setHostName(hostName);
            volumeSnapshotDTO.setStatus("Enabled");

            String volumeId = getStringFromJson(jsonObject, "volume_id", "");
            String host = getStringFromJson(jsonObject, "host", "");
            volumeSnapshotDTO.setPrimaryStorageUuid(StrUtil.isNotEmpty(volumeId) ?
                    volumeId.equals("local") && StrUtil.isNotEmpty(host) ? host + "_local" : volumeId : null);

            volumeSnapshotDTO.setPrimaryStorageName(getStringFromJson(jsonObject, "storagename", ""));
            volumeSnapshotDTO.setDescription(getStringFromJson(vmSnapshot,"description",""));
            volumeSnapshotDTO.setLatest("true");
            volumeSnapshotDTO.setInstallPath(getStringFromJson(vmSnapshot,"backstoragename",""));

            Long snapTime = getLongFromJson(vmSnapshot, "snaptime")*1000;
            Date updateTime = (snapTime != null && snapTime != 0) ? new Date(snapTime) : null;
            volumeSnapshotDTO.setVUpdateDate(updateTime);
            volumeSnapshotDTO.setVCreateDate(updateTime);
            volumeSnapshotDTO.setCreateTime(updateTime);
            volumeSnapshotDTO.setFormat("qcow2");

            // 平台信息
            volumeSnapshotDTO.setType("主机快照");
            volumeSnapshotDTO.setPlatformName(platform.getPlatformName());
            volumeSnapshotDTO.setPlatformId(platform.getPlatformId());
            volumeSnapshotDTO.setTypeName("sangFor");
            volumeSnapshotDTO.setIsMemory(getIntFromJson(vmSnapshot, "snappowerstatus") == 1);

            list.add(volumeSnapshotDTO);
        }
        return list;
    }


    @Override
    public String supportProtocol() {
        return BASIC_SXF_SNAPSHOT.code();
    }
}
