package cn.coder.zj.module.collector.collect.basicdata.manager.istack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;

import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_VPC;

// isTack 暂时没有vpc
@Slf4j
public class IsTackBasicVpcDataImpl extends AbstractBasicData {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {

    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_VPC.code();
    }
}
