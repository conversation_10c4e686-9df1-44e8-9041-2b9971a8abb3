package cn.coder.zj.module.collector.collect.metrics.mem;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper.createBaseMetricData;
import static cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper.getMetricsValueArray;
import static cn.coder.zj.module.collector.enums.MemType.PROTOCOL_FUSION_COMPUTE_MEM;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class FsOneMemImpl extends AbstractMetrics implements MetricsCollectHelper.MetricsHandler {
    protected long startTime;
    
    @Override
    public void preCheck(Platform platform) {
        // 预检查逻辑
    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) platformObj;
                List<MetricData> metricDataList = collectData(platform);
                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.MEM_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
            });
        }
    }

    private List<MetricData> collectData(Platform platform) {
        List<MetricData> list = new ArrayList<>();
        list.addAll(MetricsCollectHelper.collectVmData(platform, "MEM", this));
        list.addAll(MetricsCollectHelper.collectHostData(platform, "MEM", this));
        return list;
    }



    @Override
    public String supportProtocol() {
        return PROTOCOL_FUSION_COMPUTE_MEM.code();
    }

    @Override
    public List<MetricData> handleVmMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonObject cloudInfo = FsApiCacheService.getJsonObject(platformUrl + getStringFromJson(cloud, "uri"), null, headers);
        // 基础信息获取
        JsonObject memory = cloudInfo.getAsJsonObject("vmConfig").getAsJsonObject("memory");
        BigDecimal quantity = getBigFromJson(memory, "quantityMB").multiply(new BigDecimal(1024 * 1024));


        JsonArray valueArray = getMetricsValueArray(platform, cloudInfo, headers);
        MetricData metricData = createBaseMetricData(platform, cloud, MEM_USED_TASK.code(),"vm");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonArray values = element.getAsJsonObject().getAsJsonArray("value");
            for (JsonElement value : values) {
                JsonObject asJsonObject = value.getAsJsonObject();
                String metricId = getStringFromJson(asJsonObject, "metricId");
                if(metricId.equals("mem_usage")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    BigDecimal memoryUsed = quantity.multiply(metricValue).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal memoryFree = quantity.subtract(memoryUsed).setScale(2, RoundingMode.HALF_UP);
                    if(memoryFree.compareTo(BigDecimal.ZERO) < 0){
                        memoryFree = BigDecimal.ZERO;
                    }
                    MetricData used = BeanUtil.copyProperties(metricData, MetricData.class);
                    used.setValues(Arrays.asList(memoryUsed.doubleValue()));
                    metricDataList.add(used);

                    MetricData free = BeanUtil.copyProperties(metricData, MetricData.class);
                    free.setMetricName(MEM_FREE_TASK.code());
                    free.setValues(Arrays.asList(memoryFree.doubleValue()));
                    metricDataList.add(free);

                    MetricData use = BeanUtil.copyProperties(metricData, MetricData.class);
                    use.setMetricName(MEM_USAGE_TASK.code());
                    use.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricDataList.add(use);
                }
            }
        }
        return metricDataList;
    }

    @Override
    public List<MetricData> handleHostMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject host = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonObject obj = FsApiCacheService.getJsonObject(platformUrl + getStringFromJson(host, "computeResourceStatics"), null, headers);
        JsonObject memoryInfo = obj.getAsJsonObject("memResource");
        Long totalSizeMB = getLongFromJsonDouble(memoryInfo, "totalSizeMB")*1024 * 1024;
        JsonArray valueArray = getMetricsValueArray(platform, host, headers);
        MetricData metricData = createBaseMetricData(platform, host, MEM_USED_TASK.code(),"host");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonArray values = element.getAsJsonObject().getAsJsonArray("value");
            for (JsonElement value : values) {
                JsonObject asJsonObject = value.getAsJsonObject();
                String metricId = getStringFromJson(asJsonObject, "metricId");
                BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                if(metricId.equals("mem_usage")){
                    BigDecimal memoryUsed = new BigDecimal(totalSizeMB).multiply(metricValue).divide(new BigDecimal(100));
                    BigDecimal memoryFree = new BigDecimal(totalSizeMB).subtract(memoryUsed).setScale(2, RoundingMode.HALF_UP);
                    if(memoryFree.compareTo(BigDecimal.ZERO) < 0){
                        memoryFree = BigDecimal.ZERO;
                    }
                    MetricData used = BeanUtil.copyProperties(metricData, MetricData.class);
                    used.setValues(Arrays.asList(memoryUsed.doubleValue()));
                    metricDataList.add(used);

                    MetricData free = BeanUtil.copyProperties(metricData, MetricData.class);
                    free.setMetricName(MEM_FREE_TASK.code());
                    free.setValues(Arrays.asList(memoryFree.doubleValue()));
                    metricDataList.add(free);

                    MetricData use = BeanUtil.copyProperties(metricData, MetricData.class);
                    use.setMetricName(MEM_USAGE_TASK.code());
                    use.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricDataList.add(use);
                }
            }
        }
        return metricDataList;
    }
}
