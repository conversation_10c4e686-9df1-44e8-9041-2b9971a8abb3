package cn.coder.zj.module.collector.collect.metrics.mem;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.util.*;

import static cn.coder.zj.module.collector.enums.MemType.PROTOCOL_WIN_HONG_MEM;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class WinHongMemImpl extends AbstractMetrics {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();
                Platform platform = (Platform) o;
                String token = platform.getWinHongPlatform().getToken();
                if (token == null) {
                    log.error("平台 {} token为空", platform.getPlatformName());
                    return;
                }
                metricDataList.addAll(vmdata(platform));
                metricDataList.addAll(hostData(platform));

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.MEM_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                log.info("collectVmData name: {}", Thread.currentThread().getName());
            });
        }
    }

    public List<MetricData> vmdata(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_CLOUDS, null, headers).getAsJsonObject().get("data").getAsJsonArray();

        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();

        List<String> domainIds = new ArrayList<>();
        for (JsonElement jsonElement : hostArray) {
            if (Objects.equals(jsonElement.getAsJsonObject().get("status").getAsString(), "1")) {
                domainIds.add(jsonElement.getAsJsonObject().get("uuid").getAsString());
            }
        }

        String result = String.join(",", domainIds);
        //所有cup使用率
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);
        List<MetricData> metricDataList = new ArrayList<>();
        for (JsonElement host1 : hostArray) {
            JsonObject host = host1.getAsJsonObject();
            if (!Objects.equals(host.getAsJsonObject().get("status").getAsString(), "1")) {
                continue;
            }
            double total = host.get("memory").getAsDouble();


            Map<String, String> param = new HashMap<>();
            param.put("domainIds", host.get("id").getAsString());
            param.put("startTime", startTime);
            param.put("endTime", endTime);
            JsonArray memRateList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_DOMAIN_MEM_RATE, param, headers).getAsJsonArray();

            if (!memRateList.isEmpty()) {
                List<Long> timestamps = new ArrayList<>();
                List<Double> usedvalues = new ArrayList<>();
                List<Double> freevalues = new ArrayList<>();
                List<Double> usagevalues = new ArrayList<>();
                for (JsonElement memRate : memRateList) {
                    JsonObject usageItem = memRate.getAsJsonObject();

                    MetricData metricUsedData = new MetricData();
                    metricUsedData.setPlatformId(platform.getPlatformId());
                    metricUsedData.setResourceId(usageItem.get("domainId").getAsString());
                    metricUsedData.setResourceName(usageItem.get("domainName").getAsString());
                    metricUsedData.setMetricName(MEM_USED_TASK.code());
                    metricUsedData.setType("vm");

                    MetricData metricFreeData = new MetricData();
                    metricFreeData.setPlatformId(platform.getPlatformId());
                    metricFreeData.setResourceId(usageItem.get("domainId").getAsString());
                    metricFreeData.setResourceName(host.get("domainName").getAsString());
                    metricFreeData.setMetricName(MEM_FREE_TASK.code());
                    metricFreeData.setType("vm");

                    MetricData metricUseRateData = new MetricData();
                    metricUseRateData.setPlatformId(platform.getPlatformId());
                    metricUseRateData.setResourceId(usageItem.get("domainId").getAsString());
                    metricUseRateData.setResourceName(host.get("domainName").getAsString());
                    metricUseRateData.setMetricName(MEM_USAGE_TASK.code());
                    metricUseRateData.setType("vm");




                    JsonArray data = usageItem.get("data").getAsJsonArray();
                    if (!data.isEmpty()) {
                        for (JsonElement item : data) {
                            JsonObject cpu = item.getAsJsonObject();
                            timestamps.add(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000);
                            usedvalues.add(cpu.get("value").getAsDouble() * total / 100);
                            freevalues.add(total - cpu.get("value").getAsDouble() * total / 100);
                            usagevalues.add( cpu.get("value").getAsDouble());

                            metricUsedData.setTimestamps(timestamps);
                            metricUsedData.setValues(usedvalues);
                            metricFreeData.setTimestamps(timestamps);
                            metricFreeData.setValues(freevalues);
                            metricUseRateData.setTimestamps(timestamps);
                            metricUseRateData.setValues(usagevalues);
                            break;
                        }
                    }
                    metricDataList.add(metricUsedData);
                    metricDataList.add(metricFreeData);
                    metricDataList.add(metricUseRateData);
                }
            }
        }

        return metricDataList;
    }

    public List<MetricData> hostData(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HARDWARE_LIST, null, headers).getAsJsonObject().get("data").getAsJsonArray();
        ;
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);

        List<MetricData> metricDataList = new ArrayList<>();
        for (JsonElement jsonElement : hostArray) {
            JsonObject host = jsonElement.getAsJsonObject();
            String uuid = host.get("id").getAsString();

            MetricData metricUsedData = new MetricData();
            metricUsedData.setPlatformId(platform.getPlatformId());
            metricUsedData.setResourceId(uuid);
            metricUsedData.setResourceName(host.get("name").getAsString());
            metricUsedData.setMetricName(MEM_USED_TASK.code());
            metricUsedData.setType("host");

            MetricData metricFreeData = new MetricData();
            metricFreeData.setPlatformId(platform.getPlatformId());
            metricFreeData.setResourceId(host.get("id").getAsString());
            metricFreeData.setResourceName(host.get("name").getAsString());
            metricFreeData.setMetricName(MEM_FREE_TASK.code());
            metricFreeData.setType("host");

            MetricData metricUseRateData = new MetricData();
            metricUseRateData.setPlatformId(platform.getPlatformId());
            metricUseRateData.setResourceId(host.get("id").getAsString());
            metricUseRateData.setResourceName(host.get("name").getAsString());
            metricUseRateData.setMetricName(MEM_USAGE_TASK.code());
            metricUseRateData.setType("host");


            Map<String, String> param = new HashMap<>();
            param.put("hostId", host.get("id").getAsString());
            param.put("startTime", startTime);
            param.put("endTime", endTime);
            JsonArray memRateList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HOST_MEM_RATE, param, headers).getAsJsonObject().get("data").getAsJsonArray();

            Double totalMemoryCapacity = host.get("memory").getAsDouble();


            if (!memRateList.isEmpty()) {
                List<Long> timestamps = new ArrayList<>();
                List<Double> usedvalues = new ArrayList<>();
                List<Double> freevalues = new ArrayList<>();
                List<Double> usagevalues = new ArrayList<>();
                for (JsonElement memRate : memRateList) {
                    JsonObject usageItem = memRate.getAsJsonObject();
                    timestamps.add(DateUtil.parse(usageItem.get("time").getAsString()).getTime() / 1000);
                    usedvalues.add(usageItem.get("value").getAsDouble() * totalMemoryCapacity / 100);
                    freevalues.add(totalMemoryCapacity - usageItem.get("value").getAsDouble() * totalMemoryCapacity / 100);
                    usagevalues.add( usageItem.get("value").getAsDouble());
                    break;
                }
                metricUsedData.setTimestamps(timestamps);
                metricUsedData.setValues(usedvalues);
                metricFreeData.setTimestamps(timestamps);
                metricFreeData.setValues(freevalues);
                metricUseRateData.setTimestamps(timestamps);
                metricUseRateData.setValues(usagevalues);

                metricDataList.add(metricUsedData);
                metricDataList.add(metricFreeData);
                metricDataList.add(metricUseRateData);
            }
        }
        return metricDataList;
    }

    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            return GSON.fromJson(responseBody, JsonElement.class);
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_WIN_HONG_MEM.code();
    }
}
