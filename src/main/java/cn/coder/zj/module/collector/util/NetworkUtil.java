package cn.coder.zj.module.collector.util;

import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPRequest;
import cn.iocoder.zj.framework.common.enums.OtherEnum;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;
import org.snmp4j.CommunityTarget;
import org.snmp4j.PDU;
import org.snmp4j.Snmp;
import org.snmp4j.TransportMapping;
import org.snmp4j.event.ResponseEvent;
import org.snmp4j.mp.SnmpConstants;
import org.snmp4j.smi.GenericAddress;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.OctetString;
import org.snmp4j.smi.VariableBinding;
import org.snmp4j.transport.DefaultUdpTransportMapping;
import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class NetworkUtil {
    private static final int CONNECT_TIMEOUT = 1000;

    public static boolean isPingable(String ipAddress) {
        try {
            InetAddress address = InetAddress.getByName(ipAddress);
            return address.isReachable(CONNECT_TIMEOUT);
        } catch (IOException e) {
            log.error("Ping IP地址 {} 失败: {}", ipAddress, e.getMessage());
            return false;
        }
    }

    /**
     * 使用系统命令ping指定IP
     * @return 是否可以ping通
     */
    public static boolean pingWithCommand(String ipAddress) {
        try {
            Process process = Runtime.getRuntime().exec("ping -n 2 -w 500 " + ipAddress);
            boolean completed = process.waitFor(800, TimeUnit.MILLISECONDS);

            if (!completed) {
                process.destroyForcibly();
                return false;
            }

            int exitValue = process.exitValue();
            return exitValue == 0;
        } catch (IOException | InterruptedException e) {
            log.error("执行ping命令失败: {}", e.getMessage());
            return false;
        }
    }

    public static boolean isTcpPortOpen(String ip, int port) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), CONNECT_TIMEOUT);
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    public static boolean isSnmpReachable(String ip, int port, String community, int version) {
        try (TransportMapping<?> transport = new DefaultUdpTransportMapping()) {
            Snmp snmp = new Snmp(transport);
            transport.listen();

            CommunityTarget target = new CommunityTarget();
            target.setCommunity(new OctetString(community));
            target.setAddress(GenericAddress.parse(String.format("udp:%s/%d", ip, port)));
            target.setRetries(1);
            target.setTimeout(1000);
            target.setVersion(version);

            PDU pdu = new PDU();
            pdu.setType(PDU.GET);
            pdu.add(new VariableBinding(new OID(".*******.*******.0")));

            ResponseEvent response = snmp.send(pdu, target);
            return response != null && response.getResponse() != null;

        } catch (Exception e) {
            log.debug("SNMP检测失败 {}:{} [{}] version:{}", ip, port, community, version);
            return false;
        }
    }

    public static <T> List<List<T>> splitIntoBatches(List<T> items, int batchSize) {
        if (items.size() <= batchSize) {
            return Collections.singletonList(items);
        }

        int numBatches = (int) Math.ceil((double) items.size() / batchSize);
        return IntStream.range(0, numBatches)
                .mapToObj(i -> items.subList(
                        i * batchSize,
                        Math.min((i + 1) * batchSize, items.size())))
                .collect(Collectors.toList());
    }

    public static int convertVersion(String versionStr) {
        if (StrUtil.isBlank(versionStr)) {
            return SnmpConstants.version2c; // 默认使用v2c
        }

        switch (versionStr.toLowerCase()) {
            case "1":
            case "v1":
                return SnmpConstants.version1;
            case "2c":
            case "v2c":
                return SnmpConstants.version2c;
            case "3":
            case "v3":
                return SnmpConstants.version3;
            default:
                return SnmpConstants.version2c;
        }
    }

    public static void sendMessage(Long ipRangeId, int total, int dealCount,String type) {
        ScanIPRequest data = new ScanIPRequest();
        data.setIpRangeId(ipRangeId);
        data.setTotal(total);
        String otherCode;

        switch (type) {
            case "ping":
                data.setPingCount(dealCount);
                otherCode = OtherEnum.ADVANCES_PING.code();
                break;
            case "snmp":
                data.setSnmpCount(dealCount);
                otherCode = OtherEnum.ADVANCES_SNMP.code();
                break;
            case "tcp":
                data.setTcpCount(dealCount);
                otherCode = OtherEnum.ADVANCES_TCP.code();
                break;
            default:
                return;
        }

        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                .setType(ClusterMsg.MessageType.OTHER)
                .setData(GsonUtil.GSON.toJson(data))
                .setMetrics(otherCode)
                .setTime(System.currentTimeMillis())
                .build();
        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message);
    }
}
