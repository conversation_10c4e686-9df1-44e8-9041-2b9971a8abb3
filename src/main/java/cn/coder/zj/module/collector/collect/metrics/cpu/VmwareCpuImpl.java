package cn.coder.zj.module.collector.collect.metrics.cpu;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.vmware.VmwareMetricsUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.vmware.vim25.HostHardwareSummary;
import com.vmware.vim25.mo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.List;

import static cn.coder.zj.module.collector.enums.CpuType.PROTOCOL_VMWARE_CPU;
import static cn.coder.zj.module.collector.enums.MetricNameType.CPU_USED_TASK;
import static cn.coder.zj.module.collector.service.vmware.HostComputerResourceSummary.getHostList;
import static cn.coder.zj.module.collector.service.vmware.VmComputerResourceSummary.getVmList;

@Slf4j
public class VmwareCpuImpl extends AbstractMetrics {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {
        // 预检查逻辑，暂时不需要实现
    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            Platform platform = (Platform) o;
            taskExecutor.execute(() -> {
                        List<MetricData> metricDataList = new ArrayList<>();

                        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();

                        if (serviceInstance == null) {
                            log.error("平台 {} serviceInstance为空", platform.getPlatformName());
                            return;
                        }

                        // 宿主机指标
                        List<MetricData> hostMetrics = hostCpuMetric(serviceInstance, platform);
                        if (hostMetrics != null && !hostMetrics.isEmpty()) {
                            metricDataList.addAll(hostMetrics);
                        }

                        // 虚拟机指标
                        List<MetricData> vmMetrics = vmCpuMetric(serviceInstance, platform);
                        if (vmMetrics != null && !vmMetrics.isEmpty()) {
                            metricDataList.addAll(vmMetrics);
                        }

                        message.setData(new Gson().toJson(metricDataList));
                        message.setTime(System.currentTimeMillis());
                        message.setType(ClusterMsg.MessageType.CPU_TASK);
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
            );
        }
    }

    private List<MetricData> hostCpuMetric(ServiceInstance serviceInstance, Platform platform) {
        List<MetricData> metricDataList = new ArrayList<>();
        try {
            // 宿主机列表
            List<HostSystem> hostSystems = getHostList(serviceInstance);

            if (hostSystems == null || hostSystems.isEmpty()) {
                log.warn("平台 {} 未找到宿主机系统", platform.getPlatformName());
                return metricDataList;
            }

            // 循环宿主机列表
            for (HostSystem hostSystem : hostSystems) {
                String clusterUuid = hostSystem.getParent().getMOR().getVal();
                HostHardwareSummary hostHardwareSummary = hostSystem.getSummary().getHardware();
                String vms = hostSystem.getMOR().getVal();
                String uuid = clusterUuid + hostHardwareSummary.getUuid() + vms;
                String hostName = hostSystem.getName();

                ManagedEntity managedEntity = new InventoryNavigator(serviceInstance.getRootFolder())
                        .searchManagedEntity("HostSystem", hostName);

                if (managedEntity == null) {
                    log.warn("找不到宿主机: {}", hostName);
                    continue;
                }

                // 收集CPU使用率指标
                MetricData cpuMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, hostName, CPU_USED_TASK.code());
                VmwareMetricsUtil.collectMetric(
                        serviceInstance,
                        platform,
                        cpuMetric,
                        managedEntity,
                        hostName,
                        "cpu.usage.average",
                        VmwareMetricsUtil::cpuValueConverter,
                        "host"
                );

                if (!cpuMetric.getValues().isEmpty()) {
                    metricDataList.add(cpuMetric);
                }
            }
        } catch (Exception e) {
            log.error("收集宿主机CPU性能数据失败: {}", e.getMessage(), e);
        }

        return metricDataList;
    }

    private List<MetricData> vmCpuMetric(ServiceInstance serviceInstance, Platform platform) {
        List<MetricData> metricDataList = new ArrayList<>();
        try {
            // 虚拟机列表
            List<VirtualMachine> virtualMachines = getVmList(serviceInstance);
            if (virtualMachines == null || virtualMachines.isEmpty()) {
                log.warn("平台 {} 未找到虚拟机", platform.getPlatformName());
                return metricDataList;
            }

            for (VirtualMachine virtualMachine : virtualMachines) {
                // 跳过模板和未开机的虚拟机
                if (virtualMachine.getConfig() != null && virtualMachine.getConfig().isTemplate()) {
                    continue;
                }

                if (virtualMachine.getRuntime().getPowerState() != com.vmware.vim25.VirtualMachinePowerState.poweredOn) {
                    continue;
                }

                String vms = virtualMachine.getMOR().getVal();
                String uuid = vms + "-" + virtualMachine.getSummary().getConfig().getUuid();
                String vmName = virtualMachine.getName();

                ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                        .searchManagedEntity("VirtualMachine", vmName);

                if (managedEntities == null) {
                    log.warn("找不到虚拟机: {}", vmName);
                    continue;
                }

                // 收集CPU使用率指标
                MetricData cpuMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, vmName, CPU_USED_TASK.code());
                VmwareMetricsUtil.collectMetric(
                        serviceInstance,
                        platform,
                        cpuMetric,
                        managedEntities,
                        vmName,
                        "cpu.usage.average",
                        VmwareMetricsUtil::cpuValueConverter,
                        "vm"
                );

                if (!cpuMetric.getValues().isEmpty()) {
                    metricDataList.add(cpuMetric);
                }
            }
        } catch (Exception e) {
            log.error("收集虚拟机CPU性能数据失败: {}", e.getMessage(), e);
        }

        return metricDataList;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_VMWARE_CPU.code();
    }
}
