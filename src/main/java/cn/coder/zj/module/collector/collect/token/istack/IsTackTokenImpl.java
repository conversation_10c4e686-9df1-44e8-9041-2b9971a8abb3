package cn.coder.zj.module.collector.collect.token.istack;

import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.dal.platform.istack.IsTackPlatform;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.log4j.Log4j2;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

import static cn.coder.zj.module.collector.enums.PlatformType.IS_TACK;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Log4j2
public class IsTackTokenImpl extends AbstractToken {

    @Override
    public void preCheck(Platform platform) {
        AsyncTaskExecutor asyncTaskExecutor = SpringBeanUtils.getBean(AsyncTaskExecutor.class);
        final int MAX_ATTEMPTS = 3;
        boolean anySuccess = false;

        for (int attempt = 1; attempt <= MAX_ATTEMPTS; attempt++) {
            FutureTask<Boolean> task = new FutureTask<>(() -> {
                Map<String, String> param = new HashMap<>();
                param.put("os_id", platform.getPassword());
                param.put("ct_user_id", platform.getUsername());
                param.put("page", "1");
                param.put("page_size", "1");

                try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_DETAIL, param, null)) {
                    if (!response.isSuccessful()) {
                        log.error("平台 {} 连接失败, 状态码: {}", platform.getPlatformName(), response.code());
                        return false;
                    }
                    JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
                    if (asJsonArray.isEmpty()) {
                        log.error("平台 {} 校验失败: 返回结果为空", platform.getPlatformName());
                        return false;
                    }
                    log.info("平台 {} 连接检查成功", platform.getPlatformName());
                    return true;
                } catch (IOException e) {
                    log.error("平台 {} 连接超时", platform.getPlatformName());
                    return false;
                }
            });

            try {
                asyncTaskExecutor.submit(task);
                boolean isConnected = task.get(30, TimeUnit.SECONDS);

                if (isConnected) {
                    log.info("平台 {} 第{}次连接检查成功", platform.getPlatformName(), attempt);
                    anySuccess = true;
                    break; // 只要有一次成功就退出循环
                } else {
                    log.warn("平台 {} 第{}次连接检查失败", platform.getPlatformName(), attempt);
                }

            } catch (Exception e) {
                log.error("平台 {} 第{}次连接检查超时", platform.getPlatformName(), attempt);
                task.cancel(true);
            }

            // 如果不是最后一次尝试且当前尝试失败，等待后再试
            if (attempt < MAX_ATTEMPTS && !anySuccess) {
                try {
                    log.info("平台 {} 等待15秒后进行第{}次连接尝试", platform.getPlatformName(), attempt + 1);
                    Thread.sleep(15000); // 等待15秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }
        }

        // 三次尝试后，根据是否有任何一次成功来更新平台状态
        updatePlatformStatus(platform, anySuccess);
        if (!anySuccess) {
            log.error("平台 {} 经过3次尝试后仍无法连接，判定平台离线", platform.getPlatformName());
        }
    }

//    @Override
//    public void preCheck(Platform platform) {
//        AsyncTaskExecutor asyncTaskExecutor = SpringBeanUtils.getBean(AsyncTaskExecutor.class);
//
//        FutureTask<Boolean> task = new FutureTask<>(() -> {
//            Map<String, String> param = new HashMap<>();
//            param.put("os_id", platform.getPassword());
//            param.put("ct_user_id", platform.getUsername());
//            param.put("page", "1");
//            param.put("page_size", "1");
//
//            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_DETAIL, param, null)) {
//                if (!response.isSuccessful()) {
//                    log.error("平台 {} 连接失败, 状态码: {}", platform.getPlatformName(), response.code());
//                    return false;
//                }
//                JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
//                if (asJsonArray.isEmpty()) {
//                    log.error("平台 {} 校验失败: 返回结果为空", platform.getPlatformName());
//                    return false;
//                }
//                log.info("平台 {} 连接检查成功", platform.getPlatformName());
//                return true;
//            } catch (IOException e) {
//                log.error("平台 {} 连接超时", platform.getPlatformName());
//                return false;
//            }
//        });
//
//        try {
//            asyncTaskExecutor.submit(task);
//            boolean isConnected = task.get(30, TimeUnit.SECONDS);
//            updatePlatformStatus(platform, isConnected);
//        } catch (Exception e) {
//            log.error("平台 {} 连接超时", platform.getPlatformName());
//            updatePlatformStatus(platform, false);
//            task.cancel(true);
//        }
//
//    }

    @Override
    public void token(Platform platform) {
        List<MonitorInfo> vm = getUuids(platform, "VM");
        List<MonitorInfo> host = getUuids(platform, "HOST");
        platform.setIsTackPlatform(IsTackPlatform.builder()
                .vmUuids(vm)
                .hostUuids(host)
                .build());
        Map<String, Object> tokenMap = new HashMap<>();
        tokenMap.put(platform.getPlatformId().toString(), platform);
        CacheService.put(IS_TACK.code(), tokenMap);
    }

    private List<MonitorInfo> getUuids(Platform platform, String type) {
        List<MonitorInfo> uuIdList = new ArrayList<>();
        if ("VM".equals(type)) {
            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_DETAIL, param, null)) {
                GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray().forEach(jsonElement -> {
                    JsonObject jsonObject = jsonElement.getAsJsonObject();
                    String uuid = jsonObject.get("id").getAsString();
                    String name = jsonObject.get("name").getAsString();

                    if (StringUtils.isNotBlank(uuid)) {
                        uuIdList.add(MonitorInfo.builder()
                                .uuid(uuid)
                                .name(name)
                                .build());
                    }
                });

            } catch (IOException e) {
                log.error("error collecting vm basic data: {}", e.getMessage());
            }

        } else if ("HOST".equals(type)) {
            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_HARDWARE, param, null)) {
                GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray().forEach(jsonElement -> {
                    JsonObject jsonObject = jsonElement.getAsJsonObject();
                    String uuid = jsonObject.get("uuid").getAsString();
                    String name = jsonObject.get("name").getAsString();
                    if (StringUtils.isNotBlank(uuid)) {
                        uuIdList.add(MonitorInfo.builder()
                                .uuid(uuid)
                                .name(name)
                                .build());
                    }
                });
            } catch (IOException e) {
                log.error("error collecting host basic data: {}", e.getMessage());
            }
        } else {
            throw new IllegalArgumentException("Invalid action type: " + type);
        }
        return uuIdList;
    }

    /**
     * 发送平台异常信息
     */
    private void updatePlatformStatus(Platform platform, boolean isOnline) {
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        if (isOnline) {
            platform.setState(0L);
        } else {
            platform.setState(1L);
        }
        platform.setDateTime(new Date());
        sendMessageService.sendMessage(CacheService.getCtx("ctx"), ClusterMsg.Message.newBuilder().setData(GsonUtil.GSON.toJson(platform)).setType(ClusterMsg.MessageType.DETECT).setTime(System.currentTimeMillis()).build());
    }

    @Override
    public String supportProtocol() {
        return IS_TACK.code();
    }
}
