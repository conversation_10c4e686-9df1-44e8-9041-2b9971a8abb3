package cn.coder.zj.module.collector.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

@Getter
public enum OsType {
    LINUX("Linux", new String[]{"linux"}),
    WINDOWS("Windows", new String[]{"windows", "ws"}),
    OTHER("Other", new String[]{});

    private final String platform;
    private final String[] keywords;

    OsType(String platform, String[] keywords) {
        this.platform = platform;
        this.keywords = keywords;
    }

    public static String getPlatform(String osName) {
        if (StrUtil.isNotEmpty(osName)) {
            String osNameLower = osName.toLowerCase();
            for (OsType type : values()) {
                for (String keyword : type.keywords) {
                    if (osNameLower.contains(keyword)) {
                        return type.platform;
                    }
                }
            }
        }
        return OTHER.platform;
    }
}



