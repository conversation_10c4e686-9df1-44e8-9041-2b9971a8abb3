package cn.coder.zj.module.collector.job.cache;

import com.google.gson.annotations.Expose;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 **/
@Data
@AllArgsConstructor
public class TaskCacheModel {

    /**
     * 任务名称
     */
    @Expose
    private String jobName;

    /**
     * Bean 实例
     */
    private Object bean;

    /**
     * 被注解的方法
     */
    private Method method;

    /**
     * 任务的状态
     */
    @Expose
    private int status;

    /**
     * 要执行的任务
     */
    private Runnable task;

    /**
     * Cron 表达式
     */
    @Expose
    private String cronExpression;
}
