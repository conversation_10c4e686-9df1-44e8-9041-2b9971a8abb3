package cn.coder.zj.module.collector.dal;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * 客户端信息实体类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ClientInfo  {

    /**
     * 客户端id
     */
    private String clientId;

    /**
     * 命令类型
     */
    private String cmdType;

    /**
     * ip+prod
     */
    private String clientUrl;


}