package cn.coder.zj.module.collector.util;

import cn.coder.zj.module.collector.dal.platform.Platform;
import com.google.gson.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;


public class GsonUtil {

    private GsonUtil() {
    }

    public static final Gson GSON = new GsonBuilder()
            .registerTypeAdapter(LocalDateTime.class, (JsonSerializer<LocalDateTime>) (src, typeOfSrc, context) ->
                    new JsonPrimitive(src.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)))
            .registerTypeAdapter(LocalDateTime.class, (JsonDeserializer<LocalDateTime>) (json, typeOfT, context) ->
                    LocalDateTime.parse(json.getAsString(), DateTimeFormatter.ISO_LOCAL_DATE_TIME))
            // 添加 TimeZone 的序列化适配器
            .registerTypeAdapter(TimeZone.class, (JsonSerializer<TimeZone>) (src, typeOfSrc, context) ->
                    new JsonPrimitive(src.getID()))
            // 添加 TimeZone 的反序列化适配器
            .registerTypeAdapter(TimeZone.class, (JsonDeserializer<TimeZone>) (json, typeOfT, context) ->
                    TimeZone.getTimeZone(json.getAsString()))
            .create();


    public static final Gson GSON_EXCLUDE_VMWARE = new GsonBuilder()
            .setExclusionStrategies(new ExclusionStrategy() {
                @Override
                public boolean shouldSkipField(FieldAttributes f) {
                    return f.getDeclaringClass() == Platform.class &&
                            f.getName().equals("vmWarePlatform");
                }

                @Override
                public boolean shouldSkipClass(Class<?> clazz) {
                    return false;
                }
            })
            .create();
}
