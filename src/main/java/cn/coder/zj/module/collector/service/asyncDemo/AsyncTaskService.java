package cn.coder.zj.module.collector.service.asyncDemo;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class AsyncTaskService {

    TaskExecutor taskExecutor;

    public AsyncTaskService(TaskExecutor taskExecutor) {
        this.taskExecutor = taskExecutor;
    }

    /**
     * 基于@Async注解提交至线程池
     */
    @Async("asyncTaskExecutor")
    public void asyncTask() {
        String threadName = Thread.currentThread().getName();
        log.info("基于@Async注解提交至线程池：{}", threadName);
    }


    /**
     * 基于线程池提交任务
     */
    public void asyncTaskTest() {
        taskExecutor.execute(() -> log.info("基于线程池提交任务：{}", Thread.currentThread().getName()));
    }

}
