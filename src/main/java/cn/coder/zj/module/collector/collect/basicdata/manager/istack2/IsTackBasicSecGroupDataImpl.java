package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostSecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupData;
import cn.iocoder.zj.framework.common.dal.manager.SecGroupRuleData;
import cn.iocoder.zj.framework.common.dal.manager.aggregation.SecGroupAggData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.util.*;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SEC_GROUP;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_SEC_GROUP;

@Slf4j
public class IsTackBasicSecGroupDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK2.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(TimeUtils.withExecutionTime(
                    String.format("プラットフォーム[%s]のデータ処理", platform.getPlatformName()), IsTackBasicSecGroupDataImpl.class.getSimpleName(), startTime,() -> {
                List<SecGroupAggData> list = new ArrayList<>();
                list = managerData(platform, list);
                if (!list.isEmpty()) {
                    BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                            .metricsType("")
                            .metricsName(BASIC_SEC_GROUP.code())
                            .build();
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(build));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                }
            }));
        }
    }

    private List<SecGroupAggData> managerData(Platform platform, List<SecGroupAggData> list) {
        List<SecGroupAggData> aggData = Lists.newArrayList();

        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_SECGROUPS, param, null)) {
            JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();

            //组装对象
            SecGroupAggData groupInfos = new SecGroupAggData();
            List<SecGroupData> secGroups = Lists.newArrayList();
            List<SecGroupRuleData> secGroupRules = Lists.newArrayList();
            List<HostSecGroupData> hostSecGroups = Lists.newArrayList();

            if (!asJsonArray.isEmpty()) {
                asJsonArray.forEach(jsonElement -> {
                    JsonObject resultObj = jsonElement.getAsJsonObject();
                    secGroups.add(SecGroupData.builder()
                            .platformId(platform.getPlatformId())
                            .platformName(platform.getPlatformName())
                            .name(resultObj.get("name").getAsString())
                            .uuid(resultObj.get("id").getAsString())
                            .description(resultObj.get("description").getAsString())
                            .status("Enabled")
                            .build());
                    resultObj.get("security_group_rules").getAsJsonArray().forEach(rule -> {
                        JsonObject ruleAsJsonObject = rule.getAsJsonObject();

                        SecGroupRuleData.SecGroupRuleDataBuilder builder = SecGroupRuleData.builder()
                                .uuid(ruleAsJsonObject.get("uuid").getAsString())
                                .secgroupUuid(resultObj.get("id").getAsString())
                                .action(ruleAsJsonObject.get("action").getAsString().equals("permit") ||
                                        ruleAsJsonObject.get("action").getAsInt() == 0 ? "allow" : "deny")
                                .protocol(ruleAsJsonObject.get("protocol").getAsString().toLowerCase())
                                .platformId(platform.getPlatformId())
                                .platformName(platform.getPlatformName())
                                .cidr(getCidrFromJson(ruleAsJsonObject));
                        // 处理 port_range_min 和 port_range_max
                        if (ruleAsJsonObject.has("port_range_min") && !ruleAsJsonObject.get("port_range_min").isJsonNull()
                                && ruleAsJsonObject.has("port_range_max") && !ruleAsJsonObject.get("port_range_max").isJsonNull()) {
                            int portMin = ruleAsJsonObject.get("port_range_min").getAsInt();
                            int portMax = ruleAsJsonObject.get("port_range_max").getAsInt();
                            if (portMin > 0 && portMax > 0) {
                                builder.ports(portMin + "-" + portMax);
                            }
                        }
                        // 处理 port_start 和 port_end
                        if (ruleAsJsonObject.has("port_start") && !ruleAsJsonObject.get("port_start").isJsonNull()
                                && ruleAsJsonObject.has("port_end") && !ruleAsJsonObject.get("port_end").isJsonNull()) {
                            int portStart = ruleAsJsonObject.get("port_start").getAsInt();
                            int portEnd = ruleAsJsonObject.get("port_end").getAsInt();
                            if (portStart > 0 && portEnd > 0) {
                                builder.ports(portStart + "-" + portEnd);
                                if (ruleAsJsonObject.has("priority") && !ruleAsJsonObject.get("priority").isJsonNull()) {
                                    builder.priority(ruleAsJsonObject.get("priority").getAsInt());
                                }
                                if (ruleAsJsonObject.has("direction") && !ruleAsJsonObject.get("direction").isJsonNull()) {
                                    builder.direction(Objects.equals(ruleAsJsonObject.get("direction").getAsString(), "ingress") ? "in" : "out");
                                }
                            }
                        }
                        secGroupRules.add(builder.build());
                    });
                    resultObj.get("instance_info").getAsJsonArray().forEach(infos ->{
                        JsonObject hostSec = infos.getAsJsonObject();
                        hostSecGroups.add(HostSecGroupData.builder().hostUuid(hostSec.get("instance_uuid").getAsString())
                                .secgroupUuid(resultObj.get("id").getAsString())
                                .platformId(platform.getPlatformId())
                                .platformName(platform.getPlatformName()).build());

                    });
                });
                groupInfos.setSecGroups(secGroups);
                groupInfos.setSecGroupRules(secGroupRules);
                groupInfos.setHostSecGroups(hostSecGroups);
                aggData.add(groupInfos);
            }
        } catch (IOException e) {
            log.error("平台 {} 连接超时", platform.getPlatformName());
        }
        return aggData;
    }

    // 添加处理 CIDR 的辅助方法
    private String getCidrFromJson(JsonObject json) {
        if (json.has("remote_ip_prefix") && !json.get("remote_ip_prefix").isJsonNull()) {
            return json.get("remote_ip_prefix").getAsString();
        }
        if (json.has("remote_cidr") && !json.get("remote_cidr").isJsonNull()) {
            return json.get("remote_cidr").getAsString();
        }
        return "";
    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_SEC_GROUP.code();
    }
}
