package cn.coder.zj.module.collector.collect.basicdata.manager.istack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_VM_VIC;

@Slf4j
public class IsTackBasicVmNicDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(() -> {
                List<VmNicData> list = new ArrayList<>();
                managerData(platform, list);
                if (!list.isEmpty()) {
                    BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                            .metricsName(BASIC_VM_VIC.code())
                            .build();
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(build));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                }
            });
        }
    }

    private void managerData(Platform platform, List<VmNicData> list) {
        platform.getIsTackPlatform().getVmUuids().stream().forEach(info -> {
            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            param.put("instance_uuid", info.getUuid());
            try (Response response = OkHttpService.postSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_NICS, param)) {
                JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
                if (!asJsonArray.isEmpty()) {
                    asJsonArray.forEach(jsonElement -> {
                        JsonObject resultObj = jsonElement.getAsJsonObject();
                        VmNicData vmNicData = new VmNicData();
                        vmNicData.setUuid(info.getUuid());
                        vmNicData.setHostUuid(resultObj.get("instance_uuid").getAsString());
                        vmNicData.setUuid(resultObj.get("uuid").getAsString());
                        vmNicData.setIp("");
                        vmNicData.setIp6("");
                        vmNicData.setPlatformId(platform.getPlatformId());
                        vmNicData.setPlatformName(platform.getPlatformName());
                        vmNicData.setMac(resultObj.get("mac_addr").getAsString());
                        vmNicData.setDriver("virtio");
                        vmNicData.setInClassicNetwork((byte) 0);
                        vmNicData.setNetworkUuid(resultObj.get("sub_id").getAsString());
                        list.add(vmNicData);
                    });
                }
            } catch (IOException e) {
                log.error("平台 {} 连接超时", platform.getPlatformName());
            }
        });
    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_VM_VIC.code();
    }
}
