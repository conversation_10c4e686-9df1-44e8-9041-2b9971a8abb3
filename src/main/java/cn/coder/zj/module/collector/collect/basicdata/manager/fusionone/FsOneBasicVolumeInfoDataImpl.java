package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import java.math.BigDecimal;
import java.util.*;

import static cn.coder.zj.module.collector.util.CommonUtil.getLongFromJsonDouble;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VOLUME_INFO;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_VOLUME_INFO;

@Slf4j
public class FsOneBasicVolumeInfoDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VolumeInfoData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VOLUME_INFO.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VolumeInfoData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_CLOUD_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("vms");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<VolumeInfoData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    List<VolumeInfoData> vmData = collectVmInfoData(platform, jsonElement,header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理Fusionone云主机数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private List<VolumeInfoData> collectVmInfoData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        List<VolumeInfoData> list = new ArrayList<>();
        String platformUrl = platform.getPlatformUrl();
        String siteId = platform.getFsOnePlatform().getSiteId();

        JsonObject cloudInfo = FsApiCacheService.getJsonObject(platformUrl + getStringFromJson(cloud, "uri"), null, headers);
        JsonObject vmConfig = cloudInfo.getAsJsonObject("vmConfig");
        JsonArray disks = vmConfig.getAsJsonArray("disks");
        Long[] sizes = {0L, 0L};

        for (int i = 0; i < disks.size(); i++) {
            JsonObject disk = disks.get(i).getAsJsonObject();
            String volumeUrn = getStringFromJson(disk, "volumeUrn");

            // 获取卷信息
            String volumeUrl = platformUrl + FusionOneApiConstant.GET_VOLUMES.replace("{siteId}", siteId);
            JsonObject volumeResponse = FsApiCacheService.getJsonObject(volumeUrl,
                    Map.of("volUrns", volumeUrn, "limit", "100", "refreshflag", "true", "offset", "0"),
                    headers);
            JsonObject volume = volumeResponse.getAsJsonArray("volumes").get(0).getAsJsonObject();

            // 计算容量
            Long capacity = getLongFromJsonDouble(volume, "quantityGB")* 1024 * 1024 * 1024;
            Long allocation = getLongFromJsonDouble(volume, "userUsedSize")* 1024 * 1024;

            // 更新总容量统计
            if (i > 0) {
                sizes[0] = capacity;
                sizes[1] = allocation;
            }

            // 创建并填充卷信息对象
            VolumeInfoData volumeDTO = new VolumeInfoData();
            volumeDTO.setDescription("FusionOne云盘描述");
            volumeDTO.setName(getStringFromJson(volume, "name"));
            volumeDTO.setFormat(getStringFromJson(volume, "volumeFormat"));
            volumeDTO.setType(i == 0 ? "Root" : "Data");
            volumeDTO.setState("Enabled");
            volumeDTO.setStatus("Ready");
            volumeDTO.setUuid(getStringFromJson(volume, "uuid"));
            volumeDTO.setIsMount(true);
            volumeDTO.setMediaType("机械盘");

            // 设置关联信息
            volumeDTO.setVmInstanceUuid(getStringFromJson(cloud, "uuid"));
            volumeDTO.setVmInstanceName(getStringFromJson(cloud, "name"));
            volumeDTO.setPrimaryStorageUuid(getStringFromJson(volume, "datastoreUrn"));
            volumeDTO.setPrimaryStorageName(getStringFromJson(volume, "datastoreName"));
            volumeDTO.setPrimaryStorageType(getStringFromJson(volume, "storageType").toLowerCase());

            // 设置容量信息
            volumeDTO.setSize(capacity);
            volumeDTO.setActualSize(allocation);
            volumeDTO.setActualFree(capacity - allocation);
            volumeDTO.setActualUse(allocation);
            volumeDTO.setActualRatio(calculateRatio(allocation, capacity));

            // 设置平台信息
            volumeDTO.setPlatformId(String.valueOf(platform.getPlatformId()));
            volumeDTO.setPlatformName(platform.getPlatformName());

            // 设置创建时间
            String createTime = getStringFromJson(cloudInfo, "createTime");
            try {
                volumeDTO.setVCreateDate(StrUtil.isNumeric(createTime) ?
                        DateUtil.date(Long.parseLong(createTime) * 1000L) :
                        DateUtil.parseDateTime(createTime));
            } catch (Exception e) {
                volumeDTO.setVCreateDate(DateUtil.date());
            }

            list.add(volumeDTO);
        }

        return list;
    }

    private String calculateRatio(Long used, Long total) {
        if (total == 0) return "0";
        return new BigDecimal(used)
                .multiply(new BigDecimal(100))
                .divide(new BigDecimal(total), 2, BigDecimal.ROUND_HALF_UP)
                .toString();
    }

    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_VOLUME_INFO.code();
    }
}
