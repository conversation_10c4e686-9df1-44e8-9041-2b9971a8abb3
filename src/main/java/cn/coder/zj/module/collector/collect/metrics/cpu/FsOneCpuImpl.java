package cn.coder.zj.module.collector.collect.metrics.cpu;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.CpuType.PROTOCOL_FUSION_COMPUTE_CPU;
import static cn.coder.zj.module.collector.enums.MetricNameType.CPU_USED_TASK;
import static cn.coder.zj.module.collector.util.CommonUtil.getBigFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class FsOneCpuImpl extends AbstractMetrics implements MetricsCollectHelper.MetricsHandler {
    protected long startTime;
    
    @Override
    public void preCheck(Platform platform) {
        // 预检查逻辑
    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) platformObj;
                List<MetricData> metricDataList = collectData(platform);

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.CPU_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("Fs-CPU性能采集 {} 秒,{} 平台", endTimeFormatted,platform.getPlatformName());
            });
        }
    }

    private List<MetricData> collectData(Platform platform) {
        List<MetricData> list = new ArrayList<>();
        list.addAll(MetricsCollectHelper.collectVmData(platform, "CPU", this));
        list.addAll(MetricsCollectHelper.collectHostData(platform, "CPU", this));
        return list;
    }

    @Override
    public List<MetricData> handleVmMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonObject cloudInfo = FsApiCacheService.getJsonObject(platformUrl + getStringFromJson(cloud, "uri"), null, headers);

        JsonArray valueArray = MetricsCollectHelper.getMetricsValueArray(platform, cloudInfo, headers);
        MetricData metricData = MetricsCollectHelper.createBaseMetricData(platform, cloud, CPU_USED_TASK.code(),"vm");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonArray values = element.getAsJsonObject().getAsJsonArray("value");
            for (JsonElement value : values) {
                JsonObject asJsonObject = value.getAsJsonObject();
                String metricId = getStringFromJson(asJsonObject, "metricId");
                if(metricId.equals("cpu_usage")){
                    BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                    metricData.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricData.setType("vm");
                    metricDataList.add(metricData);
                }
            }
        }
        return metricDataList;
    }

    @Override
    public List<MetricData> handleHostMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject host = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonArray valueArray = MetricsCollectHelper.getMetricsValueArray(platform, host, headers);
        MetricData metricData = MetricsCollectHelper.createBaseMetricData(platform, host, CPU_USED_TASK.code(),"host");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonArray values = element.getAsJsonObject().getAsJsonArray("value");
            for (JsonElement value : values) {
                JsonObject asJsonObject = value.getAsJsonObject();
                String metricId = getStringFromJson(asJsonObject, "metricId");
                BigDecimal metricValue = getBigFromJson(asJsonObject, "metricValue");
                if(metricId.equals("cpu_usage")){
                    metricData.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricData.setType("host");
                    metricDataList.add(metricData);
                }
            }
        }
        return metricDataList;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_FUSION_COMPUTE_CPU.code();
    }
}
