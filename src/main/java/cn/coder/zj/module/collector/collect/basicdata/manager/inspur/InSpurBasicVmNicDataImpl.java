package cn.coder.zj.module.collector.collect.basicdata.manager.inspur;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IN_SPUR_VM_VIC;

@Slf4j
public class InSpurBasicVmNicDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VmNicData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VM_VIC.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮云主机网络采集 {} s", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VmNicData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + InSpurApiConstant.GET_CLOUD_LIST;
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization",token
        );

        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("items");

        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<VmNicData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    List<VmNicData> vmData = collectVmNicInfo(platform, jsonElement,header);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("浪潮云主机网卡数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private List<VmNicData> collectVmNicInfo(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        List<VmNicData> list = new ArrayList<>();
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        // 网络信息设置
        JsonArray nics = cloud.getAsJsonArray("nics");
        if (ObjectUtil.isNull(nics)) return new ArrayList<>();

        for (JsonElement nic : nics) {
            JsonObject nicdata = nic.getAsJsonObject();
            VmNicData vmNicData = new VmNicData();
            vmNicData.setUuid(getStringFromJson(nicdata,"deviceId"));
            vmNicData.setName(getStringFromJson(nicdata,"deviceName"));
            vmNicData.setHostUuid(getStringFromJson(cloud, "uuid"));
            vmNicData.setIp(getStringFromJson(nicdata, "ip"));
            vmNicData.setMac(getStringFromJson(nicdata, "mac"));
            vmNicData.setIp6(getStringFromJson(nicdata, "ipv6"));
            vmNicData.setPlatformId(platform.getPlatformId());
            vmNicData.setPlatformName(platform.getPlatformName());
            vmNicData.setDriver(getStringFromJson(nicdata, "model"));
            vmNicData.setInClassicNetwork((byte) 0);
            list.add(vmNicData);
        }

        return list;
    }

    @Override
    public String supportProtocol() {
        return BASIC_IN_SPUR_VM_VIC.code();
    }
}
