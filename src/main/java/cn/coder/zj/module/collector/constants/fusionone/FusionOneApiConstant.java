package cn.coder.zj.module.collector.constants.fusionone;

public class FusionOneApiConstant {


    public static final String FUSION_ONE_PROTAL_PREFIX = "/protal/v1";
    public static final String FUSION_ONE_API_PREFIX = "/api/v1";

    public static final String COMPUTE = "/compute";
    public static final String SERVICE = "/service";

    public static final String LOGIN = SERVICE + "/session";

    public static final String SITE = SERVICE + "/sites";




    //----------------虚拟机------------------------
    public static final String GET_CLOUD_LIST = SERVICE+"/sites/{siteId}/vms";

    public static final String GET_GROUP = SERVICE+"/sites/{siteId}/securitygroups";

    public static final String GET_CLOUD_INFO = "vm_url"+"/{vmId}";


    public static final String GET_REAL_TIME_DATA = SITE+"/{siteId}/monitors/objectmetric-realtimedata";



    public static final String GET_VOLUMES = SITE+"/{siteId}/volumes";


    //----------------物理机------------------------
    public static final String GET_HOST_LIST = SERVICE+"/sites/{siteId}/hosts";

    public static final String GET_HOST_INFO = SERVICE+"/sites/{siteId}/hosts" + "/{vmid}";

    public static final String CURVE_DATA = SERVICE+"/sites/{siteId}/monitors/objectmetric-curvedata";

    public static final String DATASTORES_LIST = SERVICE+"/sites/{siteId}/datastores";

    public static final String NETWORK_LIST = SERVICE+"/sites/{siteId}/portgroups";

    public static final String GET_REALTIME_DATA = SERVICE+"/sites/{siteId}/monitors/objectmetric-realtimedata";

    //----------------网络------------------------
    public static final String GET_HOST_SYSTEMINTFS = SERVICE+"/sites/{siteId}/hosts/{urn}/systemintfs";

    public static final String GET_DVSWITCHS = SERVICE+"/sites/{siteId}/dvswitchs";
    public static final String GET_DVSWITCH_INFO = SERVICE+"/sites/{siteId}/dvswitchs/{urn}";


}


