package cn.coder.zj.module.collector.util;
public class StateUtil {
    public enum VirtualMachineState {
        RUNNING("1", "Running", "on"),
        RUNNING_ONE("running", "Running", "on"),
        STARTING("starting", "Starting", "on"),
        STARTED("STARTED", "Running", "on"),
        STOPPING("Stopping", "Stopping", "off"),
        STOPPED_1("STOPPED", "Stopped", "off"),
        STOPPING_ONE("stoping", "Stopping", "off"),
        STOPPED("2", "Stopped", "off"),
        STOPPED_ONE("stopped", "Stopped", "off"),
        REBOOTING("resetting", "Rebooting", "on"),
        DESTROYING("deleting", "Destroying", "off"),
        SUSPEND("suspend", "Stopped", "off"),
        SUSPENDING("suspending", "Stopping", "off"),
        CREATED("Created", "Running", "on"),
        DESTROYED("Destroyed", "Stopped", "off"),
        MIGRATING("Migrating", "Running", "on"),
        EXPUNGING("Expunging", "Stopped", "off"),
        PAUNGING("Paunging", "Stopped", "off"),
        PAUSED("Paused", "Stopped", "off"),
        RESUMING("Resuming", "Starting", "on"),
        VOLUME_MIGRATING("VolumeMigrating", "Running", "on"),
        UNKNOWN("unknown", "Unknown", "unknown");

        private final String sourceState;
        private final String vmState;
        private final String powerState;

        VirtualMachineState(String sourceState, String vmState, String powerState) {
            this.sourceState = sourceState;
            this.vmState = vmState;
            this.powerState = powerState;
        }

        public String getVmState() {
            return vmState;
        }

        public String getPowerState() {
            return powerState;
        }

        public static VirtualMachineState fromSource(String state) {
            if (state == null) {
                return UNKNOWN;
            }
            for (VirtualMachineState vmState : values()) {
                if (vmState.sourceState.equalsIgnoreCase(state)) {
                    return vmState;
                }
            }
            return UNKNOWN;
        }
    }

    public static String stateConvert(String state) {
        return VirtualMachineState.fromSource(state).getVmState();
    }

    public static String powerStateConvert(String state) {
        return VirtualMachineState.fromSource(state).getPowerState();
    }

    /**
     * 获取完整的状态信息
     * @param state 源状态
     * @return VirtualMachineState 包含所有状态信息的枚举实例
     */
    public static VirtualMachineState getState(String state) {
        return VirtualMachineState.fromSource(state);
    }
}
