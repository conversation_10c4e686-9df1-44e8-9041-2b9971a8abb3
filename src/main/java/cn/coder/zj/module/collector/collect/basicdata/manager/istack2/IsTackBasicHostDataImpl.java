package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.CommonUtil.getBigFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getLongFromJsonDouble;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_HOST;

@Slf4j
public class IsTackBasicHostDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);

        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK2.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(TimeUtils.withExecutionTime(
                    String.format("istack2.0 物理机采集", platform.getPlatformName()), IsTackBasicHostDataImpl.class.getSimpleName(), startTime, () -> {
                        List<HostData> hostDataList = new ArrayList<>();
                        Platform platformInfo = (Platform) platformObj;
                        // 平台异常
                        if (platformInfo.getState() == 1) {
                            return;
                        }
                        processHostList(platformInfo, hostDataList);
                        if (!CollUtil.isEmpty(hostDataList)) {
                            BasicCollectData build = BasicCollectData.builder().basicDataMap(hostDataList)
                                    .metricsName(BASIC_HOST.code())
                                    .build();
                            message.setType(ClusterMsg.MessageType.BASIC);
                            message.setData(GSON.toJson(build));
                            message.setTime(System.currentTimeMillis());
                            sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        }
                    }));
        }
    }

    private void processHostList(Platform platformInfo, List<HostData> hostDataList) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platformInfo.getPassword());
        param.put("ct_user_id", platformInfo.getUsername());
        JsonArray results = new JsonArray();
        try (Response response = OkHttpService.getSync(platformInfo.getPlatformUrl() + IStackApiConstant.GET_HARDWARE, param, null)) {
            JsonObject jsonObject = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
            if (!jsonObject.has("results") || jsonObject.get("results").isJsonNull()) {
                log.warn("平台 {} 主机 {} 没有数据返回", platformInfo.getPlatformName());
                return;
            }
            results = jsonObject.get("results").getAsJsonArray();
            if (results.isEmpty()) {
                log.warn("平台 {} 主机 {} 返回空数据集", platformInfo.getPlatformName());
                return;
            }
        } catch (IOException e) {
            log.error("error collecting host basic data: {}", e.getMessage());
        }

        results.forEach(result -> {
            JsonObject resultObj = result.getAsJsonObject();
            String uuid = resultObj.get("uuid").getAsString();
            String name = resultObj.get("name").getAsString();
            JsonObject asJsonObject = resultObj.get("ratio").getAsJsonObject();

            // 获取CPU使用率数据
            double inInfo = getHostRealTimeData(platformInfo, uuid, "net_in_bytes_rate");
            double outInfo = getHostRealTimeData(platformInfo, uuid, "net_out_bytes_rate");

            long totalCpuCapacity = resultObj.get("cpu_allocate_size").getAsLong();
            long availableCpuCapacity = totalCpuCapacity - resultObj.get("cpu_allocate_used").getAsLong();
            long cpuNum = resultObj.get("vcpus").getAsLong();
            BigDecimal cpuCommitRate = getBigFromJson(resultObj, "cpu_allocate_used");
            String cpuInfoStr = resultObj.get("cpu_info").getAsString().replace("'", "\"");  // 将单引号替换为双引号
            JsonObject cpuInfo = GSON.fromJson(cpuInfoStr, JsonObject.class);

            BigDecimal cpuUsedRate = resultObj.get("cpu_allocate_used_rate").getAsBigDecimal()
                    .multiply(new BigDecimal("100"))
                    .stripTrailingZeros()
                    .setScale(2, RoundingMode.HALF_UP);
            if (cpuUsedRate.compareTo(new BigDecimal("100")) > 0) {
                cpuUsedRate = new BigDecimal("100").setScale(2, RoundingMode.HALF_UP);
            }
            cpuUsedRate = new BigDecimal(cpuUsedRate.toPlainString()).setScale(2, RoundingMode.HALF_UP);

            long totalMemoryCapacity = getLongFromJsonDouble(resultObj,"memory_size") * 1024 * 1024;
            long availableMemoryCapacity = getLongFromJsonDouble(resultObj,"memory_free") * 1024 * 1024;
            BigDecimal memoryUsedRate = resultObj.get("memory_used_rate").getAsBigDecimal();
            BigDecimal memCommitRate = getBigFromJson(resultObj, "memory_used");

            // disk
            double disk_used = getHostRealTimeData(platformInfo, uuid, "disk_used") * 1024 * 1024 * 1024;
            double disk_util = getHostRealTimeData(platformInfo, uuid, "disk_util");
            double disk_free = getHostRealTimeData(platformInfo, uuid, "disk_free") * 1024 * 1024 * 1024;
            double disk_total = getHostRealTimeData(platformInfo, uuid, "disk_total") * 1024 * 1024 * 1024;


            HostData hostData = HostData.builder()
                    .uuid(uuid)
                    .name(name)
                    .cpuOverPercent(asJsonObject.get("cpu").getAsBigDecimal())
                    .memoryOverPercent(asJsonObject.get("ram").getAsBigDecimal())
                    .availableManager(resultObj.get("dc_uuid").getAsString())
                    .manager(platformInfo.getPlatformName())
                    .ip(resultObj.get("host_ip").getAsString())
                    .status("enabled".equals(resultObj.get("status").getAsString()) ? "Connected" : "Disconnected")
                    .bandwidthDownstream(Convert.toBigDecimal(outInfo))
                    .bandwidthUpstream(Convert.toBigDecimal(inInfo))
                    .totalCpuCapacity(totalCpuCapacity)
                    .availableCpuCapacity(availableCpuCapacity)
                    .cpuNum(Convert.toInt(cpuNum))
                    .cpuUsed(cpuUsedRate)
                    .cpuSockets(cpuInfo.get("topology").getAsJsonObject().get("sockets").getAsInt())
                    .architecture(cpuInfo.get("arch").getAsString())
                    .cpuCommitRate(cpuCommitRate)
                    .state("Enabled")
                    .clusterName(resultObj.get("availability_zone").getAsString())
                    .clusterUuid(resultObj.get("availability_zone").getAsString())
                    .totalMemoryCapacity(totalMemoryCapacity)
                    .availableMemoryCapacity(availableMemoryCapacity)
                    .memoryUsed(memoryUsedRate)
                    .memoryCommitRate(memCommitRate)
                    .totalVirtualMemory(new BigDecimal(totalMemoryCapacity))
                    .reservedMemory(Convert.toBigDecimal(0))
                    .platformId(platformInfo.getPlatformId())
                    .platformName(platformInfo.getPlatformName())
                    .diskUsedBytes(Convert.toBigDecimal(disk_used))
                    .diskUsed(Convert.toBigDecimal(disk_util))
                    .diskFreeBytes(Convert.toBigDecimal(disk_free))
                    .totalDiskCapacity(Convert.toBigDecimal(disk_total))
                    .tenantId(1L)
                    .isMaintain(0)
                    .regionId(platformInfo.getRegionId())
                    .typeName(platformInfo.getTypeCode())
                    .deleted(0)
                    .build();

            hostDataList.add(hostData);
        });
    }


    /**
     * 获取主机实时数据
     *
     * @param platformInfo 平台信息
     * @param uuid         主机UUID
     * @param itemName     指标名称
     * @return JsonArray 结果集
     */
    private double getHostRealTimeData(Platform platformInfo, String uuid, String itemName) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platformInfo.getPassword());
        param.put("ct_user_id", platformInfo.getUsername());
        param.put("uuid", uuid);
        param.put("item_name", itemName);
        param.put("from", String.valueOf(DateUtil.currentSeconds() * 1000));
        param.put("to", String.valueOf(DateUtil.currentSeconds() * 1000));
        double asDouble = 0;
        try (Response response = OkHttpService.getSync(platformInfo.getPlatformUrl() + IStackApiConstant.GET_HOST_REALTIME, param, null)) {
            JsonObject jsonObject = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
            JsonArray results = jsonObject.has("results") ? jsonObject.get("results").getAsJsonArray() : new JsonArray();
            // 检查结果数组是否为空
            if (!results.isEmpty()) {
                asDouble = results.get(0).getAsJsonObject().get("sampling_value").getAsDouble();
            }
            if (itemName.contains("bytes_rate")) {
                asDouble = asDouble * 1024;
            }
        } catch (IOException e) {
            log.error("error collecting host basic data: {}", e.getMessage());
        }
        return asDouble;
    }


    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_HOST.code();
    }


}
