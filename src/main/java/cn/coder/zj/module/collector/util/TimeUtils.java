package cn.coder.zj.module.collector.util;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TimeUtils {

    public static Runnable withExecutionTime(String taskName, String name, long startTime, Runnable task) {
        return () -> {

            try {
                task.run();
            } finally {
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("{} {} execution time: {} seconds", name, taskName, endTimeFormatted);
            }
        };
    }
}