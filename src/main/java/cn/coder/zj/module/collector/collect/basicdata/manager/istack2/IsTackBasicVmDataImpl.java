package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.StreamSupport;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_VM;

@Slf4j
public class IsTackBasicVmDataImpl extends AbstractBasicData {
    protected long startTime;
    private static final String DATA_DISK_TYPE = "DATADISK";
    private static final BigDecimal HUNDRED = new BigDecimal(100);
    private static final BigDecimal BYTES_TO_GB = new BigDecimal(1024 * 1024 * 1024);

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK2.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            Platform platform = (Platform) o;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(() -> {
                    List<VmData> list = new ArrayList<>();
                    managerData(platform, list);
                    if (!list.isEmpty()) {
                        ClusterMsg.Message.Builder platformMessage = ClusterMsg.Message.newBuilder();
                        BasicCollectData build = BasicCollectData.builder()
                                .basicDataMap(list)
                                .metricsName(BASIC_VM.code())
                                .build();
                        platformMessage.setType(ClusterMsg.MessageType.BASIC);
                        platformMessage.setData(GsonUtil.GSON.toJson(build));
                        platformMessage.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), platformMessage.build());
                    }
                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("istack2.0 云主机采集 {} s", endTimeFormatted);
            });
        }
    }

    private void managerData(Platform platform, List<VmData> list) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_DETAIL, param, null)) {
            JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
            if (!asJsonArray.isEmpty()) {
                asJsonArray.forEach(jsonElement -> {
                    JsonObject resultObj = jsonElement.getAsJsonObject();
                    String uuid = resultObj.get("id").getAsString();
                    VmData vmData = buildVmData(platform, resultObj);
                    collectVmMetrics(platform, uuid, vmData);
                    JsonArray jsonArray = vmDetail(platform, uuid, vmData);
                    calculateAndSetDiskSizes(vmData, jsonArray);

                    list.add(vmData);
                });
            }
        } catch (IOException e) {
            log.error("istack2.0 平台 {} 连接超时", platform.getPlatformName());
        }
    }


    private VmData buildVmData(Platform platform, JsonObject resultObj) {
        String addresses = resultObj.get("addresses").getAsJsonObject().getAsJsonArray("flat-113").get(0).getAsJsonObject().get("OS-EXT-IPS-MAC:mac_addr").getAsString();
        BigDecimal multiply = resultObj.getAsJsonObject("flavor").get("ram").getAsBigDecimal().multiply(new BigDecimal(1024 * 1024 * 1024));
        BigDecimal disk = resultObj.getAsJsonObject("flavor").get("sys_disk_size").getAsBigDecimal().multiply(new BigDecimal(1024 * 1024 * 1024));
        int cpuNum = resultObj.get("flavor").getAsJsonObject().get("vcpus").getAsInt();
        String status = powerStateConvert(stateConvert(resultObj.get("status").getAsString()));
        JsonObject imageObj = resultObj.getAsJsonObject("image");
        String imageId = imageObj.isEmpty() ? "" : imageObj.get("id").getAsString();
        String imageName = imageObj.isEmpty() ? "" : imageObj.get("name").getAsString();
        JsonElement hostNameElement = resultObj.get("host_name");
        String hostName = (hostNameElement != null && !hostNameElement.isJsonNull())
                ? hostNameElement.getAsString()
                : "";


        return VmData.builder()
                .guestOsType(StringUtil.isNotEmpty(resultObj.get("ostypename").getAsString()) ? resultObj.get("ostypename").getAsString() : "")
                .ip(StringUtil.isNotEmpty(resultObj.get("private_ip").getAsString()) ? resultObj.get("private_ip").getAsString() : "")
                .vipIp("")
                .mac(StringUtil.isNotEmpty(addresses) ? addresses : "")
                .architecture("x86_64")
                .uuid(resultObj.get("id").getAsString())
                .state(stateConvert(resultObj.get("status").getAsString()))
                .name(resultObj.get("name").getAsString())
                .vCreateDate(convertStringToDate(resultObj.get("created").getAsString()))
                .clusterName(resultObj.get("availability_zone").getAsString())
                .clusterUuid(resultObj.get("availability_zone").getAsString())
                .zoneName(resultObj.get("zone_name").getAsString())
                .zoneUuid(resultObj.get("zone_id").getAsString())
                .hardwareUuid(resultObj.get("host_id").getAsString())
                .hardwareName(hostName)
                .imageUuid(imageId)
                .memorySize(multiply.longValue())
                .totalDiskCapacity(disk)
                .cpuNum(cpuNum)
                .platformId(platform.getPlatformId())
                .platformName(platform.getPlatformName())
                .typeName(platform.getTypeCode())
                .tenantId(0L)
                .regionId(platform.getRegionId())
                .type("UserVm")
                .deleted(0)
                .powerState(status)
                .imageName(imageName)
                .autoInitType("NeverStop")
                .guideMode("Legacy")
                .iso(imageName)
                .build();
    }


    private void calculateAndSetDiskSizes(VmData vmData, JsonArray jsonArray) {
        // 初始化云盘总大小
        BigDecimal cloudSize = BigDecimal.ZERO;
        if (!jsonArray.isEmpty()) {
            // 计算所有数据盘大小总和
            cloudSize = calculateTotalDataDiskSize(jsonArray);
            // 设置实际使用大小和云盘总大小(转换为字节)
            setDiskSizes(vmData, cloudSize);
        } else {
            // 如果没有磁盘，设置为0
            vmData.setActualSize(cloudSize);
            vmData.setCloudSize(cloudSize);
        }
    }

    private JsonArray vmDetail(Platform platform, String uuid, VmData vmData) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        if (uuid != null && !uuid.isEmpty()) {
            param.put("instance_uuid", uuid);
        }
        JsonArray results = null;
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_DETAIL, param, null)) {
            String responseBody = response.body().string();
            JsonObject jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();
            results = jsonObject.get("results").getAsJsonArray();
        } catch (IOException e) {
            log.error("虚拟机 {} 指标数据采集异常: {}", uuid, e.getMessage());
        }
        return results;

    }

    private void collectVmMetrics(Platform platform, String uuid, VmData vmData) {
        Map<String, String> vmParam = new HashMap<>();
        vmParam.put("os_id", platform.getPassword());
        vmParam.put("ct_user_id", platform.getUsername());
        vmParam.put("uuid", uuid);
        vmParam.put("item_names", "cpu_util,mem_util,disk_util,disk_read_bytes_rate,disk_read_requests_rate," +
                "disk_write_bytes_rate,disk_write_requests_rate,net_in_bytes_rate,net_out_bytes_rate");
        vmParam.put("device_type", "vm");
        long currentTime = DateUtil.currentSeconds() * 1000;
        vmParam.put("from", String.valueOf(currentTime));
        vmParam.put("to", String.valueOf(currentTime));

        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_REALTIME, vmParam, null)) {
            String responseBody = response.body().string();
            JsonObject jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();
            JsonArray results = jsonObject.get("results").getAsJsonArray();


                if (results != null && !results.isEmpty()) {
                    vmMetrics(results, vmData);
                }

        } catch (IOException e) {
            log.error("虚拟机 {} 指标数据采集异常: {}", uuid, e.getMessage());
        }
    }

    private void vmMetrics(JsonArray items, VmData vmData) {
        items.forEach(item -> {
            try {
                JsonObject metric = (JsonObject) item.getAsJsonObject().get("metric");
                if (metric == null) {
                    return;
                }

                String itemName = metric.get("item_name").getAsString();
                if (StrUtil.isEmpty(itemName)) {
                    return;
                }

                BigDecimal samplingValue = getSamplingValue((JsonObject) item);
                switch (itemName) {
                    case "cpu_util":
                        if (samplingValue.scale() >= 3) {
                            samplingValue = samplingValue.setScale(2, RoundingMode.HALF_UP);
                        }
                        vmData.setCpuUsed(samplingValue);
                        break;
                    case "mem_util":
                        vmData.setMemoryUsed(samplingValue);
                        break;
                    case "disk_util":
                        vmData.setDiskUsed(samplingValue);
                        updateDiskUsage(vmData, samplingValue);
                        break;
                    case "net_in_bytes_rate":
                        vmData.setNetworkInPackets(BigDecimal.ZERO);
                        BigDecimal multiply = samplingValue.multiply(new BigDecimal(1024 * 8));
                        vmData.setNetworkInBytes(multiply);
                        break;
                    case "net_out_bytes_rate":
                        vmData.setNetworkOutPackets(BigDecimal.ZERO);
                        BigDecimal outMultiply = samplingValue.multiply(new BigDecimal(1024 * 8));
                        vmData.setNetworkOutBytes(outMultiply);
                        break;
                    default:
                        break; // 其他项不处理
                }
            } catch (Exception e) {
                log.error("Error processing metric item: {}", e.getMessage());
            }
        });
    }


    private BigDecimal getSamplingValue(JsonObject jsonItem) {
        try {
            JsonArray asJsonArray = jsonItem.get("values").getAsJsonArray();
            if (asJsonArray == null || asJsonArray.isEmpty()) {
                return BigDecimal.ZERO;
            }
            JsonElement firstValue = asJsonArray.get(0);
            if (firstValue == null) {
                return BigDecimal.ZERO;
            }


            JsonObject valueObj = firstValue.getAsJsonObject();
            if (valueObj == null || !valueObj.has("sampling_value")) {
                return BigDecimal.ZERO;
            }
            String samplingValue = valueObj.get("sampling_value").getAsString();
            ;
            return StrUtil.isEmpty(samplingValue) ? BigDecimal.ZERO : new BigDecimal(samplingValue);
        } catch (Exception e) {
            log.warn("Error getting sampling value: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    private void updateDiskUsage(VmData vmData, BigDecimal usedPercentage) {
        BigDecimal totalDiskCapacity = vmData.getTotalDiskCapacity();
        BigDecimal usedBytes = totalDiskCapacity.multiply(usedPercentage.divide(new BigDecimal(100)));
        vmData.setDiskUsedBytes(usedBytes);
        vmData.setDiskFreeBytes(totalDiskCapacity.subtract(usedBytes));
    }

    private String stateConvert(String state) {
        String target = "";
        switch (state) {
            case "ACTIVE":
                target = "Running";
                break;
            case "Starting":
                target = "Starting";
                break;
            case "Stopping":
                target = "Stopping";
                break;
            case "SHUTOFF":
                target = "Stopped";
                break;
            case "resetting":
                target = "Rebooting";
                break;
            case "deleting":
                target = "Destroying";
                break;
            case "suspend":
                target = "Stopped";
                break;
            case "suspending":
                target = "Stopping";
                break;
            default:
                target = "Unknown";
        }
        return target;
    }

    public static Date convertStringToDate(String dateString) {
        try {
            // 解析原始日期字符串
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(dateString);
            // 转换为Date对象
            Date date = Date.from(offsetDateTime.toInstant());
            // 格式化为标准格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedDate = sdf.format(date);
            // 返回格式化后的日期
            return sdf.parse(formattedDate);
        } catch (Exception e) {
            log.error("日期转换失败: {}", dateString, e);
            // 如果转换失败，返回当前时间
            return new Date();
        }
    }

    public static String powerStateConvert(String state) {
        return switch (state) {
            case "Created" -> "on";
            case "Starting" -> "on";
            case "Running" -> "on";
            case "Stopping" -> "off";
            case "Stopped" -> "off";
            case "Unknown" -> "unknown";
            case "Rebooting" -> "on";
            case "Destroyed" -> "off";
            case "Destroying" -> "off";
            case "Migrating" -> "on";
            case "Expunging" -> "off";
            case "Paunging" -> "off";
            case "Paused" -> "off";
            case "Resuming" -> "on";
            case "VolumeMigrating" -> "on";
            default -> "unknown";
        };
    }

    public static String getCurrentDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.now().format(formatter);
    }

    private BigDecimal calculateTotalDataDiskSize(JsonArray diskList) {
        try {
            return StreamSupport.stream(diskList.spliterator(), false)
                    .map(JsonElement::getAsJsonObject)
                    .filter(disk -> {
                        JsonElement typeElement = disk.get("type");
                        return typeElement != null && DATA_DISK_TYPE.equals(typeElement.getAsString());
                    })
                    .map(disk -> {
                        try {
                            JsonElement sizeElement = disk.get("size");
                            return (sizeElement != null && !sizeElement.isJsonNull())
                                    ? sizeElement.getAsBigDecimal()
                                    : BigDecimal.ZERO;
                        } catch (Exception e) {
                            log.warn("获取磁盘大小失败: {}", e.getMessage());
                            return BigDecimal.ZERO;
                        }
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.warn("计算数据盘总大小失败: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }


    private String getVmDiskUtil(JsonObject vmuse) {
        try {
            return Optional.ofNullable(vmuse)
                    .map(vm -> vm.getAsJsonObject("pageResult"))
                    .map(page -> page.getAsJsonArray("rows"))
                    .filter(rows -> !rows.isEmpty())
                    .map(rows -> {
                        JsonObject firstRow = rows.get(0).getAsJsonObject();
                        JsonElement volumeUtil = firstRow.get("vm_volume_util");
                        return volumeUtil != null && !volumeUtil.isJsonNull()
                                ? volumeUtil.getAsString()
                                : "0";
                    })
                    .orElse("0");
        } catch (Exception e) {
            log.warn("获取虚拟机磁盘使用率失败: {}", e.getMessage());
            return "0";
        }
    }


    private void setDiskSizes(VmData vmData, BigDecimal cloudSize) {
        BigDecimal cloudSizeBytes = cloudSize.multiply(BYTES_TO_GB);

        if (vmData.getDiskUsed() != null) {
            try {
                BigDecimal utilization = vmData.getDiskUsed().divide(new BigDecimal(100));
                BigDecimal actualSize = cloudSize
                        .multiply(utilization)
                        .divide(HUNDRED, 2, RoundingMode.HALF_UP)
                        .multiply(BYTES_TO_GB);
                vmData.setActualSize(actualSize);
            } catch (NumberFormatException e) {
                log.warn("Invalid vmDiskUtil value: {}, using cloudSizeBytes");
                vmData.setActualSize(cloudSizeBytes);
            }
        } else {
            vmData.setActualSize(cloudSizeBytes);
        }

        vmData.setCloudSize(cloudSizeBytes);
    }


    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_VM.code();
    }
}
