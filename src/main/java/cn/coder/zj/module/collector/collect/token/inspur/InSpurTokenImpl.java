package cn.coder.zj.module.collector.collect.token.inspur;

import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.inspur.InSpurPlatform;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

import java.io.IOException;
import java.util.Date;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.PlatformType.IN_SPUR;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class InSpurTokenImpl extends AbstractToken {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void token(Platform platform) {
        Map<String, String> headers = Map.of(
                "version", "5.8",
                "Content-Type", "application/json"
        );

        Map<String, String> params = Map.of(
                "username", platform.getUsername(),
                "password", platform.getPassword(),
                "locale", "cn",
                "domain", "internal"
        );

        try {
            OkHttpService.postJsonHeader(platform.getPlatformUrl() + InSpurApiConstant.LOGIN, params, headers, new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    log.error("登录请求失败: {}", e.getMessage());
                }

                @Override
                public void onResponse(Call call, Response response) {
                    try (response) {
                        if (!response.isSuccessful()) {
                            updatePlatformStatus(platform, false);
                            return;
                        }

                        JsonObject jsonObject = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
                        if (jsonObject.isEmpty() || !jsonObject.has("sessonId")) {
                            log.error("登录响应数据格式错误");
                            return;
                        }

                        platform.setInSpurPlatform(InSpurPlatform.builder()
                                .token(jsonObject.get("sessonId").getAsString())
                                .build());

                        CacheService.put(IN_SPUR.code(),
                                Map.of(platform.getPlatformId().toString(), platform));

                        log.info("浪潮云登录成功，token已缓存");
                        updatePlatformStatus(platform, true);
                    } catch (Exception e) {
                        log.error("处理登录响应数据失败: {}", e.getMessage());
                        updatePlatformStatus(platform, false);
                    }
                }
            });
        } catch (Exception e) {
            log.info("获取授权异常：{}", e.getMessage());
            updatePlatformStatus(platform, false);
        }
    }

    /**
     * 发送平台异常信息
     */
    private void updatePlatformStatus(Platform platform, boolean isOnline) {
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        if (isOnline) {
            platform.setState(0L);
        } else {
            platform.setState(1L);
        }
        platform.setDateTime(new Date());
        sendMessageService.sendMessage(CacheService.getCtx("ctx"), ClusterMsg.Message.newBuilder().setData(GsonUtil.GSON.toJson(platform)).setType(ClusterMsg.MessageType.DETECT).setTime(System.currentTimeMillis()).build());
    }

    @Override
    public String supportProtocol() {
        return IN_SPUR.code();
    }
}
