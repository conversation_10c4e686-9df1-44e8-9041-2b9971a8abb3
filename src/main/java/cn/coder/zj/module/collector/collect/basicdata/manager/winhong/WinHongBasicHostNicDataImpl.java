package cn.coder.zj.module.collector.collect.basicdata.manager.winhong;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.log4j.Log4j2;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.util.*;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_WIN_HONG_HOST_VIC;

@Log4j2
public class WinHongBasicHostNicDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<HostNicData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_HOST_VIC.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }

    }

    private List<HostNicData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);
        Map<String, String> param = new HashMap<>();
        param.put("needMonitorInfo", "true");
        param.put("domainFlag", "4");

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HARDWARE_LIST, null, headers).getAsJsonObject().get("data").getAsJsonArray();
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<HostNicData> dataList = new ArrayList<>();
        List<Map> hostList = GSON.fromJson(hostArray, new TypeToken<List<Map>>(){}.getType());
        if (CollUtil.isNotEmpty(hostList)) {
            for (Map hostMap : hostList) {
                try {
                    String uuid = Convert.toStr(hostMap.get("id"));
                    //网络
                    JsonArray groups = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_PORT_GROUPS_VSWITCHS.replace("{hostId}", uuid), new HashMap<>(), headers).getAsJsonArray();
                    for (Object group : groups) {
                        //二级
                        JsonObject groupObj = (JsonObject) group;
                        String portGroupsId = groupObj.get("id").getAsString();
                        String ip = groupObj.get("ip").getAsString();
                        //三级
                        JsonArray portGroups = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_PORT_GROUPS.replace( "{portGroupsId}", portGroupsId), new HashMap<>(), headers).getAsJsonObject().get("data").getAsJsonArray();
                        if (CollectionUtil.isNotEmpty(portGroups)) {
                            for (Object item : portGroups) {
                                JsonObject obj = (JsonObject) item;
                                HostNicData nicRespDTO = new HostNicData();
                                nicRespDTO.setUuid(obj.get("id").getAsString() + "_3");
                                nicRespDTO.setHardwareUuid(uuid);
                                nicRespDTO.setPlatformName(platform.getPlatformName());
                                nicRespDTO.setPlatformId(platform.getPlatformId());
                                nicRespDTO.setL2NetworkUuid(obj.get("id").getAsString());
                                nicRespDTO.setL2NetworkName(obj.get("name").getAsString());
                                nicRespDTO.setState(true);
                                dataList.add(nicRespDTO);
                            }
                        }

                        /*if (StringUtil.isNotEmpty(ip)) {
                            HostNicData nicRespDTO = new HostNicData();
                            nicRespDTO.setUuid(groupObj.get("id").getAsString() + "_3");
                            nicRespDTO.setNetworkType("管理口");
                            nicRespDTO.setHardwareUuid(uuid);
                            nicRespDTO.setNetworkType("-");
                            nicRespDTO.setIpAddresses(groupObj.get("ip").getAsString());
                            nicRespDTO.setIpSubnet(groupObj.get("netmask").getAsString());
                            nicRespDTO.setL2NetworkUuid(groupObj.get("id").getAsString());
                            nicRespDTO.setL2NetworkName(groupObj.get("name").getAsString());
                            nicRespDTO.setState(groupObj.get("status").getAsInt() == 1);
                            nicRespDTO.setPlatformName(platform.getPlatformName());
                            nicRespDTO.setPlatformId(platform.getPlatformId());
                            dataList.add(nicRespDTO);
                        }*/
                    }
                } catch (Exception e) {
                    log.error("处理主机数据异常, hostMap: {}, error: {}", hostMap, e.getMessage());
                }
            }
        }
        return dataList;
    }

    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            return GSON.fromJson(response.body().string(), JsonElement.class);
        } catch (IOException e) {
            log.error("error collecting basic data: {}", e.getMessage());
            return new JsonObject();
        }
    }



    @Override
    public String supportProtocol() {
        return BASIC_WIN_HONG_HOST_VIC.code();
    }
}
