package cn.coder.zj.module.collector.collect.basicdata;

import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.framework.common.message.ClusterMsg;

public abstract class AbstractBasicData {


    public abstract void preCheck(Platform platform);


    public abstract void collectBasicData(ClusterMsg.Message.Builder message);

    /**
     * the protocol this collect instance support
     *
     * @return protocol str
     */
    public abstract String supportProtocol();
}
