package cn.coder.zj.module.collector.collect.basicdata.manager.sangfor;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;
import java.util.*;
import static cn.coder.zj.module.collector.util.ApiUtil.*;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_SXF_HOST_VIC;
@Slf4j
public class SangForBasicHostNicDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<HostNicData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_HOST_VIC.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<HostNicData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie,"CSRFPreventionToken",token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_HARDWARE, null, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<HostNicData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    List<HostNicData> vmData = collectHostNicData(platform, jsonElement,headers);
                    if (vmData != null) {
                        dataList.addAll(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理深信服物理网络数据异常, hostMap: {}, error: {}", jsonElement.toString(), e.getMessage());
                }
            }
        }
        return dataList;
    }

    private List<HostNicData> collectHostNicData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        List<HostNicData> list = new ArrayList<>();
        JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String hostId = getStringFromJson(jsonObject, "id", "");
        String platformUrl = platform.getPlatformUrl();

        // 获取网络信息
        Map<String, JsonArray> networkInfoArray = getNetworksInfo(
                platformUrl + SangForApiConstant.GET_V_NETWORK_LIST,
                platformUrl + SangForApiConstant.GET_NETWORK_INFO,
                null, headers);

        // 获取物理网卡信息
        JsonArray nicArray = Optional.ofNullable(getJsonArrayFromApi(platformUrl + SangForApiConstant.GET_IFACES, null, headers))
                .map(nicInfoArray -> nicInfoArray.asList().stream()
                        .map(JsonElement::getAsJsonObject)
                        .filter(nicInfo -> hostId.equals(getStringFromJson(nicInfo, "node_id", "")))
                        .findFirst()
                        .map(nicInfo -> nicInfo.getAsJsonArray("data"))
                        .orElse(new JsonArray()))
                .orElse(new JsonArray());

        // 处理物理网卡数据
        nicArray.forEach(element -> {
            JsonObject nic = element.getAsJsonObject();
            String ip = getStringFromJson(nic, "ip", "");
            if (StrUtil.isNotEmpty(ip)) {
                String id = getStringFromJson(nic, "id", "");
                String mac = getStringFromJson(nic, "mac", "");

                HostNicData hostNicData = new HostNicData();
                hostNicData.setUuid(id + ip + mac);
                hostNicData.setHardwareUuid(hostId);
                hostNicData.setMac(mac);
                hostNicData.setNetworkType("-");
                hostNicData.setIpAddresses(ip);
                hostNicData.setIpSubnet(getStringFromJson(nic, "netMask", ""));
                hostNicData.setL2NetworkUuid(id);
                hostNicData.setL2NetworkName(getStringFromJson(nic, "name", ""));
                hostNicData.setState(getIntFromJson(nic, "status") == 1);
                hostNicData.setPlatformId(platform.getPlatformId());
                hostNicData.setPlatformName(platform.getPlatformName());

                list.add(hostNicData);
            }
        });

        // 处理VLAN网卡数据
        Optional.ofNullable(networkInfoArray)
                .map(info -> info.get("vlanGroup"))
                .ifPresent(vlanArray -> vlanArray.forEach(element -> {
                    String id = getStringFromJson(element.getAsJsonObject(), "id", "");

                    HostNicData hostNicData = new HostNicData();
                    hostNicData.setHardwareUuid(hostId);
                    hostNicData.setUuid(id + "_3");
                    hostNicData.setNetworkType("-");
                    hostNicData.setL2NetworkUuid(id);
                    hostNicData.setL2NetworkName(id);
                    hostNicData.setState(true);
                    hostNicData.setPlatformId(platform.getPlatformId());
                    hostNicData.setPlatformName(platform.getPlatformName());

                    list.add(hostNicData);
                }));

        return list;
    }

    public Map<String, JsonArray> getNetworksInfo(String url, String urlInfo, Map<String, String> params, Map<String, String> headers) {
        JsonArray bridgeList = new JsonArray();
        JsonArray vlanGroup = new JsonArray();

        try (Response response = OkHttpService.getSync(url, params, headers)) {
            JsonObject root = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
            Optional.ofNullable(root.getAsJsonArray("data"))
                    .ifPresent(dataArray -> dataArray.forEach(datum ->
                            Optional.ofNullable(datum.getAsJsonObject().getAsJsonArray("data"))
                                    .ifPresent(netArray -> netArray.forEach(net -> {
                                        String networkId = getStringFromJson(net.getAsJsonObject(), "id", "");
                                        JsonObject detailRes = getNologJsonObjectFromApi(urlInfo + networkId, null, headers);

                                        if (detailRes.size() > 0) {
                                            Optional.ofNullable(detailRes.getAsJsonArray("bridgeList"))
                                                    .ifPresent(bridgeList::addAll);
                                            Optional.ofNullable(detailRes.getAsJsonArray("vlanGroup"))
                                                    .ifPresent(vlanGroup::addAll);
                                        }
                                    }))
                    ));
        } catch (Exception e) {
            log.error("获取网络信息失败, URL: {}, 错误: {}", url, e.getMessage());
        }

        return Map.of("bridgeList", bridgeList, "vlanGroup", vlanGroup);
    }

    @Override
    public String supportProtocol() {
        return BASIC_SXF_HOST_VIC.code();
    }
}
