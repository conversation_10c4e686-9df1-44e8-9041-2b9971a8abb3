package cn.coder.zj.module.collector.service.autoDiscovery.impl;

import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.autoDiscovery.AutoDiscoveryService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import cn.iocoder.zj.framework.common.enums.OtherEnum;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import static cn.coder.zj.module.collector.collect.scanprot.ping.ScanPingData.checkNetworkPing;
import static cn.coder.zj.module.collector.collect.scanprot.snmp.ScanSnmpData.checkSnmpEndpoints;
import static cn.coder.zj.module.collector.collect.scanprot.tcp.ScanTcpData.checkTcpPorts;


@Service
@Slf4j
public class AutoDiscoveryServiceImpl implements AutoDiscoveryService {

    // 自动发现相关
    @Override
    public void autoDiscovery(String jsonStr,String metrics) {
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        Type listType = new TypeToken<List<ScanIPData>>() {}.getType();
        List<ScanIPData> list = new Gson().fromJson(jsonStr, listType);

        List<ScanIPData> data;
        String otherCode;

        switch (metrics) {
            case "ping":
                long start = System.currentTimeMillis();
                data = checkNetworkPing(list);
                otherCode = OtherEnum.PING.code();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - start) / 1000.0);
                log.info("ping 扫描时间 {} 秒", endTimeFormatted);
                break;
            case "snmp":
                long start1 = System.currentTimeMillis();
                data = checkSnmpEndpoints(list);
                otherCode = OtherEnum.SNMP.code();
                String endTimeFormatted1 = String.format("%.2f", (System.currentTimeMillis() - start1) / 1000.0);
                log.info("snmp 扫描时间 {} 秒", endTimeFormatted1);
                break;
            case "tcp":
                long start2 = System.currentTimeMillis();
                data = checkTcpPorts(list);
                otherCode = OtherEnum.TCP.code();
                String endTimeFormatted2 = String.format("%.2f", (System.currentTimeMillis() - start2) / 1000.0);
                log.info("tcp 扫描时间 {} 秒", endTimeFormatted2);
                break;
            default:
                return;
        }

        ClusterMsg.Message message = ClusterMsg.Message.newBuilder()
                .setType(ClusterMsg.MessageType.OTHER)
                .setData(GsonUtil.GSON.toJson(data))
                .setMetrics(otherCode)
                .setTime(System.currentTimeMillis())
                .build();

        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message);
    }
}
