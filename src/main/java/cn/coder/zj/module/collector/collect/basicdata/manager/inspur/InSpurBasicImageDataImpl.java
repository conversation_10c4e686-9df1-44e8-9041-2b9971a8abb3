package cn.coder.zj.module.collector.collect.basicdata.manager.inspur;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_IMAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IN_SPUR_IMAGE;

@Slf4j
public class InSpurBasicImageDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<ImageData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_IMAGE.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮云镜像采集 {} s", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<ImageData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + InSpurApiConstant.GET_IMAGE_LIST;
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization", token
        );
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("items");

        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<ImageData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    ImageData vmData = collectImageData(platform, jsonElement,header);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理浪潮云镜像数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private ImageData collectImageData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        // 基础信息获取
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String guestosLabel = getStringFromJson(cloud, "guestosLabel").toLowerCase();

        ImageData imageInfo = new ImageData();

        // 设置基本信息
        imageInfo.setUuid(getStringFromJson(cloud,"uuid"));
        imageInfo.setName(getStringFromJson(cloud,"name"));
        imageInfo.setStatus("Enabled");
        imageInfo.setCpuArch(getStringFromJson(cloud, "cpuArchType").toLowerCase());
        imageInfo.setSharingScope("不共享");
        imageInfo.setOsType(guestosLabel);
        imageInfo.setApplicationPlatform(guestosLabel.contains("linux") || guestosLabel.contains("centos") ? "Linux" :
                guestosLabel.contains("windows") ? "Windows" : "");

        JsonArray disks = cloud.getAsJsonArray("disks");
        String stringFromJson = getStringFromJson(disks.get(0).getAsJsonObject().getAsJsonObject("volume"), "format", "RAW");
        imageInfo.setFormat(stringFromJson.toLowerCase());
        imageInfo.setImageType("RootVolumeTemplate");
        imageInfo.setOsLanguage("");

        // 设置资源信息
        long memoryMB = getLongFromJsonDouble(cloud,"memoryInByte");
        long diskGB = getLongFromJsonDouble(disks.get(0).getAsJsonObject().getAsJsonObject("volume"),"sizeInByte");
        imageInfo.setMinMemory(BigDecimal.valueOf(memoryMB));
        imageInfo.setMinDisk(BigDecimal.valueOf(diskGB));

        // 设置其他属性
        imageInfo.setDiskDriver("");
        imageInfo.setNetworkDriver("");
        imageInfo.setBootMode("");
        imageInfo.setRemoteProtocol("");

        // 设置平台信息
        imageInfo.setPlatformId(platform.getPlatformId());
        imageInfo.setPlatformName(platform.getPlatformName());

        // 设置创建时间
        String createTime = getStringFromJson(cloud, "createTime");
        try {
            imageInfo.setVCreateDate(StrUtil.isNumeric(createTime) ?
                    DateUtil.date(Long.parseLong(createTime) * 1000L) :
                    DateUtil.parseDateTime(createTime));
        } catch (Exception e) {
            imageInfo.setVCreateDate(DateUtil.date());
        }

        return imageInfo;
    }


    @Override
    public String supportProtocol() {
        return BASIC_IN_SPUR_IMAGE.code();
    }
}
