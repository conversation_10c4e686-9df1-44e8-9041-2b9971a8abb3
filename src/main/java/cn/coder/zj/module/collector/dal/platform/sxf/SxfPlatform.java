package cn.coder.zj.module.collector.dal.platform.sxf;

import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SxfPlatform {

    String loginAuthCookie;

    String token;

    /**
     * 虚拟机uuid
     */
    private List<MonitorInfo> vmUuids;

    /**
     * 主机uuid
     */
    private List<MonitorInfo> hostUuids;
}
