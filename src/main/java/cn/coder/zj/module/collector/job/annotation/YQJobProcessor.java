package cn.coder.zj.module.collector.job.annotation;

import cn.coder.zj.module.collector.job.cache.TaskCacheModel;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class YQJobProcessor implements BeanPostProcessor {

    private final ApplicationContext applicationContext;

    public YQJobProcessor(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public static final ConcurrentMap<String, TaskCacheModel> TASK_CACHE = new ConcurrentHashMap<>();

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Method[] methods = bean.getClass().getDeclaredMethods();
        for (Method method : methods) {
            if (method.isAnnotationPresent(YQJob.class)) {
                YQJob yqJob = method.getAnnotation(YQJob.class);
                String jobName = yqJob.value();
                log.info("task discovery: {}", jobName);
                TASK_CACHE.put(jobName, new TaskCacheModel(jobName,bean, method, 0,() -> {
                    try {
                        ClusterMsg.Message.Builder message = ClusterMsg.Message.newBuilder();
                        method.invoke(applicationContext.getBean(bean.getClass()),message);
                    } catch (Exception e){
                        log.error("An error occurred while executing the task {} {}", jobName, ExceptionUtils.getStackTrace(e));
                    }
                },yqJob.cronExpression()));
            }
        }
        return bean;
    }

}
