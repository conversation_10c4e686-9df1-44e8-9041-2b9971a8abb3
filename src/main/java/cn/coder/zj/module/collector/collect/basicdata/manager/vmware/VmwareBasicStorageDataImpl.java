package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.StorageData;
import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_STORAGE;
@Slf4j
public class VmwareBasicStorageDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                try {
                    List<StorageData> storageDataList = handleStorageData(platform);
                    if (!CollUtil.isEmpty(storageDataList)) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(storageDataList)
                                .metricsName(BASIC_STORAGE.code())
                                .build();

                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
                } catch (Exception e) {
                    log.error("平台 [{}] 宿主机网络数据收集失败", platform.getPlatformName(), e);
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private List<StorageData> handleStorageData(Platform platform) throws RemoteException {
        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();
        List<StorageData> storageDataList = new ArrayList<>();
        List<Datastore> dataStores = new ArrayList<>();
        Datastore datastore;

        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("Datastore");
        if (managedEntities != null) {
            for (ManagedEntity managedEntity : managedEntities) {
                datastore = (Datastore) managedEntity;
                dataStores.add(datastore);
            }
        }
        if (CollUtil.isNotEmpty(dataStores)) {
            for (Datastore dataStore : dataStores) {
                // 获取存储关联的主机挂载信息
                DatastoreHostMount[] hostMounts = dataStore.getHost();
                List<StorageHostRelationData> storageHostRelationDataList = new ArrayList<>();
                if (hostMounts != null) {
                    for (DatastoreHostMount hostMount : hostMounts) {
                        // 获取主机引用
                        ManagedObjectReference hostMor = hostMount.getKey();
                        // 转换为HostSystem对象
                        HostSystem hostSystem = new HostSystem(
                                dataStore.getServerConnection(),
                                hostMor
                        );
                        // 获取主机所属集群/数据中心ID
                        String parentId = hostSystem.getParent().getMOR().getVal();
                        // 获取主机UUID
                        String hostUuid = hostSystem.getSummary().getHardware().getUuid();
                        // 获取主机MOR值
                        String hostId = parentId + hostUuid+hostSystem.getMOR().getVal();

                        String name = dataStore.getName();
                        String uuid = dataStore.getSummary().getUrl();
                        String clusterId = dataStore.getParent().getMOR().getVal();
                        // 生成存储UUID:
                        // 1. 将存储名称(name)、存储URL(uuid)和集群ID(clusterId)拼接在一起
                        // 2. 使用正则表达式移除所有特殊字符(空格、反斜杠、冒号、星号、问号、引号、尖括号和竖线)
                        String storageUuid = (name + uuid + clusterId).replaceAll("[\\s\\\\/:*?\"<>|]", "");
                        StorageHostRelationData storageHostRelationData = new StorageHostRelationData();
                        storageHostRelationData.setHardwareUuid(hostId);
                        storageHostRelationData.setStorageUuid(storageUuid);
                        storageHostRelationData.setPlatformId(platform.getPlatformId());
                        storageHostRelationData.setPlatformName(platform.getPlatformName());
                        storageHostRelationDataList.add(storageHostRelationData);
                    }
                }
                StorageData storageData = collectStorageInfo(platform, dataStore, serviceInstance);
                storageData.setStorageHostRelationDataList(storageHostRelationDataList);
                storageDataList.add(storageData);
            }
        }else {
            storageDataList = null;
        }
        return storageDataList;
    }

    private StorageData collectStorageInfo(Platform platform, Datastore datastore, ServiceInstance serviceInstance) {
        DatastoreSummary datastoreSummary = datastore.getSummary();
        String clusterId = datastore.getParent().getMOR().getVal();
        String clusterName = datastore.getParent().getName();
        String uuid = datastoreSummary.getUrl();
        String state = mapState(datastore.getOverallStatus().toString());
        String status = mapStatus(datastore.getOverallStatus().toString());
        String type = datastoreSummary.getType();
        Long capacity = datastoreSummary.getCapacity();
        Long freeSpace = datastoreSummary.getFreeSpace();
        BigDecimal usedCapacity ;
        Long uncommitted = datastoreSummary.getUncommitted();
        if (datastoreSummary.getUncommitted() != null) {
            usedCapacity = NumberUtil.add(NumberUtil.sub(capacity, freeSpace), uncommitted);
        } else {
            usedCapacity = NumberUtil.sub(capacity, freeSpace);
        }
        String name = datastore.getName();
        BigDecimal total = NumberUtil.sub(capacity, freeSpace);
        BigDecimal capacityUtilization = NumberUtil.div(total, capacity);

        Date createDate;
        try {
            createDate = getDatastoreCreationTime(serviceInstance, datastore);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if (createDate == null) {
            createDate = new Date();
        }

        // 计算虚拟容量
        BigDecimal availableDecimal = Convert.toBigDecimal(freeSpace).compareTo(new BigDecimal(0)) > 0 ? Convert.toBigDecimal(freeSpace) : new BigDecimal(0);
        BigDecimal allocation = Convert.toBigDecimal(capacity).subtract(availableDecimal);
        BigDecimal commitRate = allocation.divide(availableDecimal, 2, RoundingMode.HALF_UP);
        BigDecimal virtualCapacity = Convert.toBigDecimal(capacity);
        String storageUuid = (name + uuid + clusterId).replaceAll("[\\s\\\\/:*?\"<>|]", "");
        return new StorageData()
                .setUuid(storageUuid)
                .setName(name)
                .setUrl(uuid)
                .setState(state)
                .setType(type)
                .setStatus(status)
                .setCapacityUtilization(NumberUtil.mul(capacityUtilization, 100))
                .setUsedCapacity(Convert.toLong(usedCapacity))
                .setTotalCapacity(capacity)
                .setAvailablePhysicalCapacity(Convert.toBigDecimal(freeSpace))
                .setTotalPhysicalCapacity(Convert.toBigDecimal(capacity))
                .setAvailableCapacity(Convert.toBigDecimal(freeSpace))
                .setClusterUuid(clusterId)
                .setClusterName(clusterName)
                .setTypeName("vmware")
                .setPlatformId(platform.getPlatformId())
                .setPlatformName(platform.getPlatformName())
                .setDeleted(0)
                .setCreateTime(createDate)
                .setSCreateTime(createDate)
                .setRegionId(platform.getRegionId())
                .setMediaType("机械盘")
                .setManager(platform.getPlatformName())
                .setAvailableManager(datastore.getParent().getParent().getName())
                .setAllocation(allocation)
                .setVirtualCapacity(virtualCapacity)
                .setCommitRate(commitRate)
                .setStoragePercent(new BigDecimal(1));
    }

    public Date getDatastoreCreationTime(ServiceInstance si, Datastore datastore) throws Exception {
        PerformanceManager perfManager = si.getPerformanceManager();

        // 获取可用的采样间隔
        PerfInterval[] intervals = perfManager.getHistoricalInterval();
        if (intervals.length == 0) {
            System.out.println("No historical intervals available.");
            return null;
        }

        // 创建性能查询规范
        PerfQuerySpec qSpec = new PerfQuerySpec();
        qSpec.setEntity(datastore.getMOR());
        qSpec.setMaxSample(1);

        PerfMetricId perfMetricId = new PerfMetricId();
        perfMetricId.setCounterId(267);
        perfMetricId.setInstance("");
        qSpec.setMetricId(new PerfMetricId[]{perfMetricId});


        qSpec.setStartTime(DateUtil.calendar(DateUtil.parse("1990-01-01 00:00:00")));
        qSpec.setEndTime(DateUtil.calendar(new Date()));

        // 执行查询
        PerfEntityMetricBase[] metrics = perfManager.queryPerf(new PerfQuerySpec[]{qSpec});
        if (metrics != null && metrics.length > 0) {
            PerfEntityMetric metric = (PerfEntityMetric) metrics[0];
            PerfSampleInfo[] sampleInfos = metric.getSampleInfo();
            if (sampleInfos != null && sampleInfos.length > 0) {
                // 返回最早的样本时间作为创建时间的估计
                return sampleInfos[0].getTimestamp().getTime();
            }
        }

        System.out.println("No performance data found for the datastore.");
        return null;
    }

    private String mapState(String state) {
        return switch (state) {
            case "green" -> "Enabled";
            case "red" -> "Enabled";
            case "yellow" -> "Enabled";
            case "gray" -> "Gray";
            default -> "Unknown";
        };
    }

    private String mapStatus(String state) {
        return switch (state) {
            case "green" -> "Connected";
            case "red" -> "Connected";
            case "yellow" -> "Connected";
            default -> "Unknown";
        };
    }

    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_STORAGE.code();
    }
}
