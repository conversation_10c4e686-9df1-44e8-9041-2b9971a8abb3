package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.framework.common.message.ClusterMsg;

import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_STORAGE_POOL;

// isTack 暂时没有存储池
public class IsTackBasicStoragePoolDataImpl extends AbstractBasicData {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {

    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_STORAGE_POOL.code();
    }
}
