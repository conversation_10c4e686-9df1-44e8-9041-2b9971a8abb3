package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.framework.common.message.ClusterMsg;

import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_VPC;

public class FsOneBasicVpcDataImpl extends AbstractBasicData {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {

    }

    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_VPC.code();
    }
}
