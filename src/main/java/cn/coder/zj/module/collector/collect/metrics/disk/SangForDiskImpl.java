package cn.coder.zj.module.collector.collect.metrics.disk;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.ApiCacheService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.DiskType.PROTOCOL_SXF_DISK;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getLongFromJsonDouble;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class SangForDiskImpl extends AbstractMetrics {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();
                Platform platform = (Platform) o;
                String token = platform.getSxfPlatform().getToken();
                if (token == null) {
                    log.error("平台 {} token为空", platform.getPlatformName());
                    return;
                }

                String cookie = platform.getSxfPlatform().getLoginAuthCookie();
                Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie, "CSRFPreventionToken", token);

                // 获取所有VM的UUID并组合成标签
                List<MonitorInfo> vmUuids = platform.getSxfPlatform().getVmUuids();
                List<MonitorInfo> hostUuids = platform.getSxfPlatform().getHostUuids();
                if (vmUuids == null || vmUuids.isEmpty()) {
                    log.error("平台 {} VM UUID为空", platform.getPlatformName());
                    return;
                }

                diskVmData(vmUuids, platform, headers, metricDataList);
                diskHostData(hostUuids, platform, headers, metricDataList);
                diskUsage(platform, headers, metricDataList);

                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect collectData data end, cost {} seconds", endTimeFormatted);
                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.DISK_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<MetricData> diskUsage(Platform platform, Map<String, String> headers, List<MetricData> metricDataList) {
        JsonArray storageArray = getLocalJsonArrayFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_STORAGE_LIST, null, headers);
        if (CollUtil.isNotEmpty(storageArray)) {
            // 处理普通存储
            for (JsonElement jsonElement : storageArray) {
                JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
                String uuid = jsonObject.get("id").getAsString();
                String name = jsonObject.get("name").getAsString();
                String value = jsonObject.get("used_ratio").getAsString();
                MetricData metricData = new MetricData();
                metricData.setPlatformId(platform.getPlatformId());
                metricData.setMetricName(DISK_USED_TASK.code());
                metricData.setResourceId(uuid);
                metricData.setResourceName(name);
                metricData.setValues(List.of(Double.parseDouble(value)));
                metricData.setTimestamps(List.of(System.currentTimeMillis()));
                metricDataList.add(metricData);
            }
        }
        return metricDataList;
    }

    private List<MetricData> diskVmData(List<MonitorInfo> vmUuids, Platform platform, Map<String, String> headers, List<MetricData> metricDataList) {
        String platformUrl = platform.getPlatformUrl();
        Long currentTime = System.currentTimeMillis();

        vmUuids.forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(DISK_READ_TASK.code());
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("vm");

            String url = platformUrl + SangForApiConstant.GET_VM_DETAIL.replace("{vmid}", info.getUuid());
            JsonObject obj = ApiCacheService.getJsonObject(url, null, headers);

            JsonArray diskStatus = obj.getAsJsonArray("disk_status");
            if (diskStatus != null && diskStatus.isJsonArray()) {
                JsonArray diskArray = diskStatus.getAsJsonArray();
                if (diskArray.size() > 0) {
                    JsonObject diskInfo = diskArray.get(0).getAsJsonObject();
                    double ratio = getLongFromJsonDouble(diskInfo, "ratio");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_USED_TASK.code());
                    data.setValues(Arrays.asList(ratio));
                    metricDataList.add(data);
                }
            }

            if (obj.size() <= 0 || !obj.has("disk_sheet")) {
                return;
            }

            JsonObject diskSheet = obj.getAsJsonObject("disk_sheet");
            boolean hasTotal = diskSheet.has("total");

            // 处理速度数据
            JsonArray speedData = hasTotal ?
                    diskSheet.getAsJsonObject("total").getAsJsonObject("speed").getAsJsonArray("hour") :
                    diskSheet.getAsJsonObject("speed").getAsJsonArray("hour");

            Double speedRead = dealInfo(speedData, "IO读速率");
            Double speedWrite = dealInfo(speedData, "IO写速率");

            if (speedRead != null) {
                metricData.setValues(Arrays.asList(speedRead));
                metricData.setTimestamps(Arrays.asList(currentTime));
                metricDataList.add(metricData);
            }

            if (speedWrite != null) {
                MetricData writeData = BeanUtil.copyProperties(metricData, MetricData.class);
                writeData.setMetricName(DISK_WRITE_TASK.code());
                writeData.setValues(Arrays.asList(speedWrite));
                writeData.setTimestamps(Arrays.asList(currentTime));
                metricDataList.add(writeData);
            }

            // 处理IO数据
            JsonArray ioData = hasTotal ?
                    diskSheet.getAsJsonObject("total").getAsJsonObject("oper").getAsJsonArray("hour") :
                    diskSheet.getAsJsonObject("oper").getAsJsonArray("hour");

            Double ioRead = dealInfo(ioData, "IO读次数");
            Double ioWrite = dealInfo(ioData, "IO写次数");

            if (ioRead != null) {
                MetricData readMetricData = BeanUtil.copyProperties(metricData, MetricData.class);
                readMetricData.setMetricName(DISK_READ_OPS.code());
                readMetricData.setValues(Arrays.asList(ioRead));
                readMetricData.setTimestamps(Arrays.asList(currentTime));
                metricDataList.add(readMetricData);
            }

            if (ioWrite != null) {
                MetricData writeMetricData = BeanUtil.copyProperties(metricData, MetricData.class);
                writeMetricData.setMetricName(DISK_WRITE_OPS.code());
                writeMetricData.setValues(Arrays.asList(ioWrite));
                writeMetricData.setTimestamps(Arrays.asList(currentTime));
                metricDataList.add(writeMetricData);
            }
        });

        return metricDataList;
    }

    private List<MetricData> diskHostData(List<MonitorInfo> hostUuids, Platform platform, Map<String, String> headers, List<MetricData> metricDataList) {
        String platformUrl = platform.getPlatformUrl();
        Long currentTime = System.currentTimeMillis();

        hostUuids.forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(DISK_READ_TASK.code());
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("host");

            String baseUrl = platformUrl + SangForApiConstant.GET_HOST_DETAIL.replace("{nodeId}", info.getUuid());

            // 处理速度数据
            JsonArray speedData = getJsonArrayFromApi(baseUrl + "io_speed", null, headers);
            if (!CollUtil.isEmpty(speedData)) {
                Double speedRead = dealInfo(speedData, "IO读速率");
                Double speedWrite = dealInfo(speedData, "IO写速率");

                if (speedRead != null) {
                    metricData.setValues(Arrays.asList(speedRead));
                    metricData.setTimestamps(Arrays.asList(currentTime));
                    metricDataList.add(metricData);
                }

                if (speedWrite != null) {
                    MetricData writeData = BeanUtil.copyProperties(metricData, MetricData.class);
                    writeData.setMetricName(DISK_WRITE_TASK.code());
                    writeData.setValues(Arrays.asList(speedWrite));
                    writeData.setTimestamps(Arrays.asList(currentTime));
                    metricDataList.add(writeData);
                }

                // 处理IO数据
                JsonArray ioData = getJsonArrayFromApi(baseUrl + "io_oper", null, headers);
                if (!CollUtil.isEmpty(ioData)) {
                    Double ioRead = dealInfo(ioData, "IO读次数");
                    Double ioWrite = dealInfo(ioData, "IO写次数");

                    if (ioRead != null) {
                        MetricData readMetricData = BeanUtil.copyProperties(metricData, MetricData.class);
                        readMetricData.setMetricName(DISK_READ_OPS.code());
                        readMetricData.setValues(Arrays.asList(ioRead));
                        readMetricData.setTimestamps(Arrays.asList(currentTime));
                        metricDataList.add(readMetricData);
                    }

                    if (ioWrite != null) {
                        MetricData writeMetricData = BeanUtil.copyProperties(metricData, MetricData.class);
                        writeMetricData.setMetricName(DISK_WRITE_OPS.code());
                        writeMetricData.setValues(Arrays.asList(ioWrite));
                        writeMetricData.setTimestamps(Arrays.asList(currentTime));
                        metricDataList.add(writeMetricData);
                    }
                }
            }
        });

        return metricDataList;
    }


    private Double dealInfo(JsonArray data, String label) {
        if (data == null || data.isEmpty()) {
            return null;
        }

        // 处理VM的数据格式
        if (data.get(0).getAsJsonObject().has("name")) {
            JsonArray valArr = new JsonArray();

            for (JsonElement element : data) {
                JsonObject item = element.getAsJsonObject();
                String name = item.get("name").getAsString();

                if (name.contains(label)) {
                    valArr = item.get("data").getAsJsonArray();
                    break;
                }
            }

            if (!valArr.isEmpty()) {
                double value = valArr.get(valArr.size() - 1).getAsDouble();
                return Double.parseDouble(String.format("%.2f", value));
            }
        }
        // 处理Host的数据格式
        else {
            for (JsonElement element : data) {
                JsonObject item = element.getAsJsonObject();
                if (item.get("name").getAsString().contains(label)) {
                    JsonArray cpuData = item.get("data").getAsJsonArray();
                    if (cpuData != null && !cpuData.isEmpty()) {
                        double value = cpuData.get(cpuData.size() - 1).getAsDouble();
                        return Double.parseDouble(String.format("%.2f", value));
                    }
                    break;
                }
            }
        }

        return null;
    }


    public static JsonArray getLocalJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            if (element != null && element.isJsonObject()) {
                JsonArray asJsonArray = element.getAsJsonObject().getAsJsonObject("data").getAsJsonArray("local");
                if (asJsonArray != null && asJsonArray.isJsonArray()) {
                    return asJsonArray;
                }
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_SXF_DISK.code();
    }
}
