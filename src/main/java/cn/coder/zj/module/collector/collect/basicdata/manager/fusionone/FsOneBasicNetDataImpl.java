package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL2Data;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL3Data;
import cn.iocoder.zj.framework.common.enums.MetricsType;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.*;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromFsApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getIntFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_NET;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_NET;
@Slf4j
public class FsOneBasicNetDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                List<NetWorkL2Data> netWorkL2List = new ArrayList<>();
                List<NetWorkL3Data> netWorkL3List = new ArrayList<>();
                collectData(platform, netWorkL2List, netWorkL3List);
                if (!netWorkL2List.isEmpty()) {
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GSON.toJson(BasicCollectData.builder()
                            .basicDataMap(netWorkL2List)
                            .metricsName(BASIC_NET.code())
                            .metricsType(MetricsType.BASIC_NET_L2.code())
                            .build()));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                }
                if (!netWorkL3List.isEmpty()) {
                    ChannelHandlerContext ctx = CacheService.getCtx("ctx");
                    ClusterMsg.Message.Builder messageL3 = ClusterMsg.Message.newBuilder()
                            .setType(ClusterMsg.MessageType.BASIC)
                            .setData(GSON.toJson(BasicCollectData.builder()
                                    .basicDataMap(netWorkL3List)
                                    .metricsName(BASIC_NET.code())
                                    .metricsType(MetricsType.BASIC_NET_L3.code())
                                    .build()));
                    messageL3.setTime(System.currentTimeMillis());
                    ctx.writeAndFlush(messageL3);
                }
                double costTime = (System.currentTimeMillis() - startTime) / 1000.0;
                log.info("平台 {} 网络数据收集完成，耗时：{} 秒", platform.getPlatformName(), String.format("%.2f", costTime));
            });
        }
    }

    private void collectData(Platform platform, List<NetWorkL2Data> netWorkL2List, List<NetWorkL3Data> netWorkL3List) {
        //获取网络
        String token = platform.getFsOnePlatform().getToken();
        String siteId = platform.getFsOnePlatform().getSiteId();
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject netWorkObj = getJsonObjectFromFsApi(
                platform.getPlatformUrl() + FusionOneApiConstant.NETWORK_LIST.replace("{siteId}", siteId),
                null,
                header);
        JsonArray netWork = netWorkObj.getAsJsonArray("portGroups");
        if (ObjectUtil.isNotNull(netWorkObj)){
            netWorkL2List.addAll(processNetWorkJsonArray(netWork, platform));
        }

        JsonObject DvSwitchSObj = getJsonObjectFromFsApi(
                platform.getPlatformUrl() + FusionOneApiConstant.GET_DVSWITCHS.replace("{siteId}", siteId),
                null,
                header);
        JsonArray dvSwitchS = DvSwitchSObj.getAsJsonArray("dvSwitchs");
        if (ObjectUtil.isNotNull(dvSwitchS)){
            netWorkL2List.addAll(processDvSwitchS(dvSwitchS,platform,header));
        }
        //获取宿主机列表
        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_HOST_LIST.replace("{siteId}", siteId);
        JsonObject hosts = getJsonObjectFromFsApi(url, null, header);
        JsonArray hostArray = hosts.getAsJsonArray("hosts");
        if (ObjectUtil.isNotNull(hostArray)){
            processHostsNetworkData(hostArray, platform, header, netWorkL2List, netWorkL3List);
        }
    }

    /**
     * 处理宿主机网络数据，同时处理L2和L3网络数据
     */
    private void processHostsNetworkData(JsonArray hostArray, Platform platform, Map<String, String> header, 
                                        List<NetWorkL2Data> netWorkL2List, List<NetWorkL3Data> netWorkL3List) {
        String siteId = platform.getFsOnePlatform().getSiteId();
        Date date = DateUtil.date(new Date());
        
        for (JsonElement jsonElement : hostArray) {
            JsonObject hostJsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
            String hostName = getStringFromJson(hostJsonObject, "name");
            String hostNetAddr = getStringFromJson(hostJsonObject, "netAddr");
            
            // 获取原始URN
            String originalUrn = getStringFromJson(hostJsonObject, "urn");
            // 处理L2网络需要的URN格式
            String l2Urn = originalUrn.contains("hosts:") ? originalUrn.split("hosts:")[1] : originalUrn;
            
            // 获取物理网卡信息
            JsonObject systemIntFsObj = getJsonObjectFromFsApi(
                    platform.getPlatformUrl() + FusionOneApiConstant.GET_HOST_SYSTEMINTFS.replace("{siteId}", siteId).replace("{urn}", l2Urn),
                    null,
                    header);
                    
            if (ObjectUtil.isNull(systemIntFsObj)) {
                log.warn("获取宿主机 {} 的网卡信息失败", hostName);
                continue;
            }
            
            JsonArray systemIntFs = systemIntFsObj.getAsJsonArray("systemIntfs");
            if (systemIntFs == null || systemIntFs.isEmpty()) {
                log.warn("宿主机 {} 没有网卡信息", hostName);
                continue;
            }
            
            // 处理每个网卡信息
            for (Object systemIntF : systemIntFs) {
                JsonObject protObject = (JsonObject) systemIntF;
                String portName = getStringFromJson(protObject, "portName");
                String portUrn = getStringFromJson(protObject, "portUrn");
                String urn = getStringFromJson(protObject, "urn");
                
                // 添加L2网络数据
                NetWorkL2Data netWorkL2Data = createL2NetworkData(portName, portUrn, platform, date);
                netWorkL2List.add(netWorkL2Data);
                
                // 添加L3网络数据
                NetWorkL3Data netWorkL3Data = createL3NetworkData(urn, hostName, portUrn, portName, hostNetAddr, platform, date);
                netWorkL3List.add(netWorkL3Data);
            }
        }
    }
    
    /**
     * 创建L2网络数据对象
     */
    private NetWorkL2Data createL2NetworkData(String portName, String portUrn, Platform platform, Date date) {
        NetWorkL2Data netWorkL2Data = new NetWorkL2Data();
        netWorkL2Data.setName(portName);
        netWorkL2Data.setUuid(portUrn);
        netWorkL2Data.setPhysicalInterface(portName);
        netWorkL2Data.setType("VLAN");
        netWorkL2Data.setVlan("-");
        netWorkL2Data.setPlatformId(platform.getPlatformId());
        netWorkL2Data.setPlatformName(platform.getPlatformName());
        netWorkL2Data.setRegionId(platform.getRegionId());
        netWorkL2Data.setTypeName("FusionOne");
        netWorkL2Data.setCreateTime(date);
        return netWorkL2Data;
    }
    
    /**
     * 创建L3网络数据对象
     */
    private NetWorkL3Data createL3NetworkData(String urn, String hostName, String portUrn, String portName, 
                                             String netAddr, Platform platform, Date date) {
        NetWorkL3Data netWorkL3Data = new NetWorkL3Data();
        netWorkL3Data.setUuid(urn);
        netWorkL3Data.setName(hostName);
        netWorkL3Data.setL2NetworkUuid(portUrn);
        netWorkL3Data.setL2NetworkName(portName);
        netWorkL3Data.setType("L3BasicNetwork");
        netWorkL3Data.setNetworkServices("-");
        netWorkL3Data.setNextHopIp(netAddr);
        netWorkL3Data.setCreateTime(date);
        netWorkL3Data.setPlatformId(platform.getPlatformId());
        netWorkL3Data.setPlatformName(platform.getPlatformName());
        netWorkL3Data.setTypeName("FusionOne");
        return netWorkL3Data;
    }

    private List<NetWorkL2Data> processNetWorkJsonArray(JsonArray netWork, Platform platform) {
        List<NetWorkL2Data> NetWorkL2DataS = new ArrayList<>();
        Date date = DateUtil.date(new Date());

        for (int i = 0; i < netWork.size(); i++) {
            JsonObject JsonObject = netWork.get(i).getAsJsonObject();
            NetWorkL2Data NetWorkL2Data = new NetWorkL2Data();

            NetWorkL2Data.setName(getStringFromJson(JsonObject,"name"));
            NetWorkL2Data.setUuid(getStringFromJson(JsonObject,"urn"));
            NetWorkL2Data.setPhysicalInterface(getStringFromJson(JsonObject,"physicalInterface"));
            NetWorkL2Data.setType(getIntFromJson(JsonObject,"portType") == 0 ? "VLAN" : "FLAT");
            NetWorkL2Data.setVlan(getStringFromJson(JsonObject,"vlanId"));
            NetWorkL2Data.setVirtualNetworkId(0);
            NetWorkL2Data.setPlatformId(platform.getPlatformId());
            NetWorkL2Data.setPlatformName(platform.getPlatformName());
            NetWorkL2Data.setRegionId(platform.getRegionId());
            NetWorkL2Data.setTypeName("FusionOne");
            NetWorkL2Data.setCreateTime(date);
            NetWorkL2Data.setPhysicalInterface(getStringFromJson(JsonObject,"name"));

            NetWorkL2DataS.add(NetWorkL2Data);
        }
        return NetWorkL2DataS;
    }

    private List<NetWorkL2Data> processDvSwitchS(JsonArray dvSwitchS,Platform platform,Map<String, String> header) {
        List<NetWorkL2Data> NetWorkL2DataList = new ArrayList<>();
        String siteId = platform.getFsOnePlatform().getSiteId();
        //获取上行链路详情
        Date date = DateUtil.date(new Date());
        for (Object dvSwitch : dvSwitchS) {
            JsonObject JsonObject = (JsonObject) dvSwitch;
            String urn = getStringFromJson(JsonObject,"urn").split("dvswitchs:")[1];
            JsonObject hostPortSetObj = getJsonObjectFromFsApi(
                    platform.getPlatformUrl() + FusionOneApiConstant.GET_DVSWITCH_INFO.replace("{siteId}", siteId).replace("{urn}",urn),
                    null,
                    header);
            JsonArray hostPortSet = hostPortSetObj.getAsJsonArray("hostPortSet");
            if (ObjectUtil.isNotNull(hostPortSet)){
                for (Object protocol : hostPortSet) {
                    JsonObject protObject = (JsonObject) protocol;
                    NetWorkL2Data NetWorkL2Data = new NetWorkL2Data();
                    NetWorkL2Data.setName(getStringFromJson(protObject,"portName"));
                    NetWorkL2Data.setUuid(getStringFromJson(protObject,"portUrn"));
                    NetWorkL2Data.setPhysicalInterface(getStringFromJson(protObject,"portName"));
                    NetWorkL2Data.setType("VLAN");
                    NetWorkL2Data.setVlan("-");
                    NetWorkL2Data.setPlatformId(platform.getPlatformId());
                    NetWorkL2Data.setPlatformName(platform.getPlatformName());
                    NetWorkL2Data.setRegionId(platform.getRegionId());
                    NetWorkL2Data.setTypeName("FusionOne");
                    NetWorkL2Data.setCreateTime(date);
                    NetWorkL2DataList.add(NetWorkL2Data);

                }
            }
        }

        return NetWorkL2DataList;
    }

    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_NET.code();
    }
}
