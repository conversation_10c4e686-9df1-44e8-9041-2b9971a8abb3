package cn.coder.zj.module.collector.enums;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
public enum MessageType {

    /**
     * 认证
     */
    AUTH(0, "认证"),
    /**
     * 心跳
     */
    HEART(1, "心跳"),
    /**
     * 更改配置
     */
    UPDATE(2, "更改"),

    /**
     * 初始化配置
     */
    INFO(3, "初始化配置token"),

    /**
     * 基础数据详情
     */
    BASIC(4, "基础数据详情"),
    /**
     * CPU业务数据
     */
    CPU_TASK(5, "CPU业务数据"),
    // DISK指令
    DISK_TASK(6,"磁盘性能数据"),
    // MEM指令
    MEM_TASK(7,"内存性能数据"),
    // NET指令
    NET_TASK(8,"网络性能数据");





    private final Integer code;

    private final String desc;


    public static MessageType fromCode(Integer code) {
        for (MessageType typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }


    public Integer code() {
        return this.code;
    }


}

