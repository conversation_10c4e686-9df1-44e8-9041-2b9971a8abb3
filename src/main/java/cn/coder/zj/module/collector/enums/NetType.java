package cn.coder.zj.module.collector.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public enum NetType {


    PROTOCOL_ZS_TACK_NET("zstackNet", "zstack网络"),
    PROTOCOL_FUSION_COMPUTE_NET("fusionComputeNet", "fusion_compute网络"),
    PROTOCOL_IS_TACK_NET("istackNet", "istack网络"),
    PROTOCOL_VMWARE_NET("vmwareNet", "vmware网络"),
    PROTOCOL_SXF_NET("sxfNet", "深信服网络"),
    PROTOCOL_WIN_HONG_NET("winHongNet", "winHong网络"),
    PROTOCOL_IN_SPUR_NET("InSpurNet", "浪潮网络");


    private final String code;

    private final String desc;


    public static NetType fromCode(String code) {
        for (NetType typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String code() {
        return this.code;
    }

    public static List<String> getAllNeTypeCodes() {
        List<String> codes = new ArrayList<>();
        for (NetType netType : NetType.values()) {
            codes.add(netType.code());
        }
        return codes;
    }

}
