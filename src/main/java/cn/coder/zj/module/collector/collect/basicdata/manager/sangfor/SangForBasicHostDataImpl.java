package cn.coder.zj.module.collector.collect.basicdata.manager.sangfor;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cn.coder.zj.module.collector.util.ApiUtil.*;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_SXF_HOST;

@Slf4j
public class SangForBasicHostDataImpl extends AbstractBasicData {


    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<HostData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_HOST.code())
                        .build();
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
    }

    private List<HostData> collectData(Object platformObj) {
        List<HostData> dataList = new ArrayList<>();
        Platform platform = (Platform) platformObj;
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return dataList;
        }

        Map<String, String> headers = Map.of(
                "Cookie", "LoginAuthCookie=" + platform.getSxfPlatform().getLoginAuthCookie(),
                "CSRFPreventionToken", token);
        String platformUrl = platform.getPlatformUrl();

        // 获取主机列表
        JsonArray host = getJsonArrayFromApi(platformUrl + SangForApiConstant.GET_HARDWARE, null, headers);
        if (host == null || host.isEmpty()) return dataList;

        // 获取CPU超售配置
        JsonArray cpuOveruseConfig = Optional.ofNullable(getNologJsonObjectFromApi(platformUrl + SangForApiConstant.GET_CPU_OVERUSE_CONFIG, null, headers))
                .filter(config -> config.size() > 0 && config.has("nodes"))
                .map(config -> config.get("nodes").getAsJsonArray())
                .orElse(new JsonArray());

        // 获取内存超售配置
        JsonArray memoryOveruseConfig = Optional.ofNullable(getJsonObjectFromApi(platformUrl + SangForApiConstant.GET_MEMORY_OVERUSE_CONFIG, null, headers))
                .filter(config -> config.size() > 0 && config.has("nodes"))
                .map(config -> config.get("nodes").getAsJsonArray())
                .orElse(new JsonArray());

        // 获取网卡信息
        JsonArray nicInfoArray = getJsonArrayFromApi(platformUrl + SangForApiConstant.GET_IFACES, null, headers);

        // 获取虚拟网络列表
        JsonArray networkArr = new JsonArray();
        JsonArray respArrays = getJsonArrayFromApi(platformUrl + SangForApiConstant.GET_V_NETWORK_LIST, null, headers);
        if (CollUtil.isNotEmpty(respArrays)) {
            for (JsonElement jsonElement : respArrays) {
                if (jsonElement.isJsonObject()) {
                    JsonObject obj = jsonElement.getAsJsonObject();
                    if (obj.has("data") && obj.get("data").isJsonArray()) {
                        JsonArray res = obj.get("data").getAsJsonArray();
                        for (JsonElement resElement : res) {
                            networkArr.add(GSON.fromJson(resElement.toString(), JsonElement.class).getAsJsonObject());
                        }
                    }
                }
            }
        }

        // 获取网络详细信息
        JsonArray vlanGroup = new JsonArray();
        JsonArray target = new JsonArray();
        for (JsonElement jsonElement : networkArr) {
            JsonObject net = jsonElement.getAsJsonObject();
            if (!net.has("id")) continue;

            JsonObject netInfo = getNologJsonObjectFromApi(platformUrl + SangForApiConstant.GET_NETWORK_INFO + net.get("id").getAsString(), null, headers);
            if (netInfo != null && netInfo.size() > 0) {
                if (netInfo.has("bridgeList") && netInfo.get("bridgeList").isJsonArray()) {
                    target.addAll(netInfo.get("bridgeList").getAsJsonArray());
                }
                if (netInfo.has("vlanGroup") && netInfo.get("vlanGroup").isJsonArray()) {
                    vlanGroup.addAll(netInfo.get("vlanGroup").getAsJsonArray());
                }
            }
        }

        // 处理每个主机
        for (JsonElement item : host) {
            JsonObject jsonObject = item.getAsJsonObject();
            if (!jsonObject.has("id")) continue;

            String hostId = jsonObject.get("id").getAsString();
            HostData hostData = new HostData();
            BigDecimal bigDecimal = getBigFromJson(jsonObject, "mem_ratio");

            // 查找CPU超售配置
            BigDecimal cpuOverPercent = BigDecimal.ONE;
            for (JsonElement jsonElement : cpuOveruseConfig) {
                JsonObject cpuOveruse = jsonElement.getAsJsonObject();
                if (cpuOveruse.has("node_id") && cpuOveruse.get("node_id").getAsString().equals(hostId)) {
                    cpuOverPercent = getBigFromJson(cpuOveruse, "cpu_over_percent").divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    break;
                }
            }

            // 查找内存超售配置
            BigDecimal memoryOverPercent = BigDecimal.ONE;
            for (JsonElement jsonElement : memoryOveruseConfig) {
                JsonObject memoryOveruse = jsonElement.getAsJsonObject();
                if (memoryOveruse.has("node_id") && memoryOveruse.get("node_id").getAsString().equals(hostId)) {
                    memoryOverPercent = getBigFromJson(memoryOveruse, "mem_over_percent").divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    break;
                }
            }

            // 查找物理网络信息
            JsonArray nicArray = new JsonArray();
            if (nicInfoArray != null) {
                for (JsonElement jsonElement : nicInfoArray) {
                    JsonObject nicInfo = jsonElement.getAsJsonObject();
                    if (nicInfo.has("node_id") && nicInfo.get("node_id").getAsString().equals(hostId) &&
                            nicInfo.has("data") && nicInfo.get("data").isJsonArray()) {
                        nicArray = nicInfo.get("data").getAsJsonArray();
                        break;
                    }
                }
            }

            // 初始化变量
            long disk_used_bytes = 0L, disk_free_bytes = 0L, total_disk_capacity = 0L;
            long cpu_sockets = 0L, total_memory_capacity = 0L, available_memory_capacity = 0L;
            String architecture = "";
            BigDecimal disk_used = BigDecimal.ZERO;
            BigDecimal reservedMemory = BigDecimal.ZERO, memCommitRate = BigDecimal.ZERO;

            // 获取主机详细信息
            JsonObject hostDetail = getJsonObjectFromApi(platformUrl + SangForApiConstant.GET_HARDWARE_INFO.replace("{id}", hostId), null, headers);
            if (hostDetail != null && !hostDetail.isJsonNull()) {
                // 处理存储状态
                if (hostDetail.has("storage_status") && hostDetail.get("storage_status").isJsonArray()) {
                    JsonArray storages = hostDetail.get("storage_status").getAsJsonArray();
                    for (JsonElement storage : storages) {
                        JsonObject st = storage.getAsJsonObject();
                        total_disk_capacity += getLongFromJson(st, "total");
                        disk_free_bytes += getLongFromJson(st, "free");
                        disk_used_bytes += getLongFromJson(st, "used");
                    }
                    if (total_disk_capacity > 0) {
                        disk_used = new BigDecimal((double) disk_used_bytes / total_disk_capacity).setScale(2, RoundingMode.HALF_UP);
                    }
                }

                // 处理CPU状态
                if (hostDetail.has("cpu_status") && hostDetail.get("cpu_status").isJsonObject()) {
                    JsonObject cpuStatus = hostDetail.get("cpu_status").getAsJsonObject();
                    cpu_sockets = getLongFromJson(cpuStatus, "sockets");
                    architecture = getStringFromJson(cpuStatus, "type", "");
                }

                // 处理内存状态
                if (hostDetail.has("mem_status") && hostDetail.get("mem_status").isJsonObject()) {
                    JsonObject memStatus = hostDetail.get("mem_status").getAsJsonObject();
                    total_memory_capacity = getLongFromJson(memStatus, "total");
                    available_memory_capacity = getLongFromJson(memStatus, "free");
                }

                // 处理内存布局
                if (hostDetail.has("mem_layout") && hostDetail.get("mem_layout").isJsonObject() &&
                        hostDetail.get("mem_layout").getAsJsonObject().has("system")) {
                    JsonObject systemMem = hostDetail.get("mem_layout").getAsJsonObject().getAsJsonObject("system");
                    reservedMemory = getBigFromJson(systemMem, "reserved_total_byte");
                }

                // 处理内存配置状态
                if (hostDetail.has("conf_mem_status") && hostDetail.get("conf_mem_status").isJsonObject()) {
                    JsonObject memConfStatus = hostDetail.get("conf_mem_status").getAsJsonObject();
                    memCommitRate = getBigFromJson(memConfStatus, "conf_used_rate");
                }
            }

            // 处理网络信息
            int inBps = 0, outBps = 0, inPps = 0;
            if (target != null && !target.isEmpty()) {
                if (target.size() > 0) {
                    for (JsonElement targetElement : target) {
                        JsonObject netInfo = GSON.fromJson(targetElement, JsonElement.class).getAsJsonObject();
                        if (netInfo.has("hostID") && netInfo.get("hostID").getAsString().equals(hostId)) {
                            inBps += netInfo.has("inBps") ? netInfo.get("inBps").getAsInt() : 0;
                            outBps += netInfo.has("outBps") ? netInfo.get("outBps").getAsInt() : 0;
                            inPps += netInfo.has("inPps") ? netInfo.get("inPps").getAsInt() : 0;
                        }
                    }
                    hostData.setBandwidthDownstream(new BigDecimal(inBps));
                    hostData.setBandwidthUpstream(new BigDecimal(outBps));
                    hostData.setPacketRate(new BigDecimal(inPps));
                } else {
                    JsonArray net = getJsonArrayFromApi(platformUrl + SangForApiConstant.GET_NODE_SHEET + "?node=" + hostId + "&time_frame=hour&data_type=net", null, headers);
                    if (net != null && !net.isEmpty()) {
                        JsonObject js = dealInfo(net, "接收").get(0).getAsJsonObject();
                        JsonObject fs = dealInfo(net, "发送").get(0).getAsJsonObject();
                        hostData.setBandwidthDownstream(Convert.toBigDecimal(Convert.toInt(js.get("val"))));
                        hostData.setBandwidthUpstream(Convert.toBigDecimal(Convert.toInt(fs.get("val"))));
                        hostData.setPacketRate(Convert.toBigDecimal(Convert.toInt(js.get("val"))));
                    }
                }
            }

            // 设置主机基本信息
            hostData.setName(getStringFromJson(jsonObject, "name", ""));
            hostData.setUuid(hostId);
            hostData.setIp(getStringFromJson(jsonObject, "ip", ""));
            hostData.setStatus(jsonObject.has("status") ? (jsonObject.get("status").getAsInt() == 1 ? "Connected" : "Disconnected") : "");
            hostData.setState("Enabled");
            hostData.setClusterName("default cluster");
            hostData.setClusterUuid("default_cluster");

            // 处理CPU信息
            BigDecimal cpuCommitRate = BigDecimal.ZERO;
            if (jsonObject.has("conf_cpu") && jsonObject.get("conf_cpu").isJsonObject() &&
                    jsonObject.get("conf_cpu").getAsJsonObject().size() > 0) {
                JsonObject cpuInfo = jsonObject.get("conf_cpu").getAsJsonObject();
                if (cpuInfo.has("total_cpu_vcore") && cpuInfo.has("cpu_over_percent") &&
                        cpuInfo.has("conf_used_vcore")) {
                    Integer cpuNum = cpuInfo.get("total_cpu_vcore").getAsInt();
                    hostData.setCpuNum(cpuNum);
                    long total = cpuNum * cpuOverPercent.longValue();
                    hostData.setTotalCpuCapacity(total);
                    long availableCpuCapacity = cpuInfo.get("conf_total_vcore").getAsLong() -
                            cpuInfo.get("conf_used_vcore").getAsLong();
                    hostData.setAvailableCpuCapacity(availableCpuCapacity);
                    if (cpuNum > 0) {
                        cpuCommitRate = getBigFromJson(cpuInfo,"conf_used_vcore");
                    }
                }
            } else {
                JsonObject wareInfo = getJsonObjectFromApi(platformUrl + SangForApiConstant.GET_HARDWARE_CLOUD_INFO.replace("{id}", hostId), null, headers);
                if (wareInfo != null && wareInfo.size() > 0 && wareInfo.has("cpu_status") &&
                        wareInfo.get("cpu_status").isJsonObject() && jsonObject.has("cpu_ratio")) {
                    JsonObject cpuStatusObj = wareInfo.get("cpu_status").getAsJsonObject();
                    if (cpuStatusObj.has("cores")) {
                        long cores = cpuStatusObj.get("cores").getAsLong();
                        BigDecimal cpuRatio = jsonObject.get("cpu_ratio").getAsBigDecimal();
                        Long totalCpuCapacity = cores * cpu_sockets;
                        Long useCpuCapacity = Convert.toLong(NumberUtil.mul(totalCpuCapacity, cpuRatio));
                        Long availableCpuCapacity = Convert.toLong(NumberUtil.sub(totalCpuCapacity, useCpuCapacity));
                        hostData.setTotalCpuCapacity(totalCpuCapacity);
                        hostData.setAvailableCpuCapacity(availableCpuCapacity);
                        hostData.setCpuNum(Convert.toInt(useCpuCapacity));
                        if (useCpuCapacity > 0) {
                            cpuCommitRate = new BigDecimal(totalCpuCapacity - availableCpuCapacity)
                                    .divide(new BigDecimal(useCpuCapacity), 3, RoundingMode.HALF_UP);
                        }
                    }
                }
            }

            // 设置CPU和内存信息
            hostData.setCpuSockets(Convert.toInt(cpu_sockets));
            if (jsonObject.has("cpu_ratio")) {
                hostData.setCpuUsed(jsonObject.get("cpu_ratio").getAsBigDecimal().multiply(new BigDecimal(100)));
            }
            hostData.setArchitecture(architecture);

            // 设置磁盘信息
            hostData.setDiskUsedBytes(Convert.toBigDecimal(disk_used_bytes));
            hostData.setDiskFreeBytes(Convert.toBigDecimal(disk_free_bytes));
            hostData.setDiskUsed(Convert.toBigDecimal(disk_used).multiply(new BigDecimal(100)));
            hostData.setTotalDiskCapacity(Convert.toBigDecimal(total_disk_capacity));

            // 设置内存信息
            hostData.setTotalMemoryCapacity(total_memory_capacity);
            hostData.setAvailableMemoryCapacity(available_memory_capacity);
            if (jsonObject.has("mem_ratio")) {
                hostData.setMemoryUsed(jsonObject.get("mem_ratio").getAsBigDecimal().multiply(new BigDecimal(100)));
            }

            if (bigDecimal != null && bigDecimal.signum() < 0) {
                hostData.setMemoryUsed(bigDecimal.abs().multiply(new BigDecimal(100)));
                hostData.setStatus("Connected");
            }

            // 设置平台信息
            hostData.setPlatformId(platform.getPlatformId());
            hostData.setPlatformName(platform.getPlatformName());
            hostData.setTypeName("sangFor");
            hostData.setDeleted(0);
            hostData.setCpuOverPercent(cpuOverPercent);
            hostData.setMemoryOverPercent(memoryOverPercent);
            hostData.setCpuCommitRate(cpuCommitRate);
            hostData.setMemoryCommitRate(memCommitRate);
            hostData.setReservedMemory(reservedMemory);
            BigDecimal multiply = new BigDecimal(total_memory_capacity).multiply(memoryOverPercent);
            hostData.setTotalVirtualMemory(multiply);
            hostData.setManufacturer("-");
            hostData.setVms("");
            hostData.setManager(platform.getPlatformName());
            hostData.setAvailableManager("");
            hostData.setCpuType("-");
            hostData.setSerialNumber("-");
            hostData.setModel("-");
            hostData.setBrandName("sangFor");
            hostData.setIsMaintain(0);

            dataList.add(hostData);
        }

        return dataList;
    }

    private JsonArray dealInfo(JsonArray sheet, String labe) {
        JsonArray valArr = new JsonArray();
        Long time = 0L;
        Long interval = 0L;
        for (int i = 0; i < sheet.size(); i++) {
            JsonObject item = GSON.fromJson(sheet.get(i).toString(), JsonElement.class).getAsJsonObject();
            if (item.get("name").getAsString().contains(labe)) {
                valArr = item.get("data").getAsJsonArray();
            }
            if (item.get("name").getAsString().contains("time")) {
                time = item.get("data").getAsJsonObject().get("start").getAsLong();
                interval = item.get("data").getAsJsonObject().get("interval").getAsLong();
            }
        }

        JsonArray valOfTime = new JsonArray();
        for (int i = 0; i < valArr.size(); i++) {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("val", valArr.get(i).getAsLong());
            jsonObject.addProperty("time", time);
            valOfTime.add(jsonObject);
            time += interval;
        }
        return valOfTime;
    }

    @Override
    public String supportProtocol() {
        return BASIC_SXF_HOST.code();
    }
}
