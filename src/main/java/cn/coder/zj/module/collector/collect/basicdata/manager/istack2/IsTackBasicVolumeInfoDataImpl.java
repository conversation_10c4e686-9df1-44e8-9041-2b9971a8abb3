package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VOLUME_INFO;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_VOLUME_INFO;

@Slf4j
public class IsTackBasicVolumeInfoDataImpl extends AbstractBasicData {


    protected long startTime;


    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK2.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(TimeUtils.withExecutionTime(
                    String.format("プラットフォーム[%s]のデータ処理", platform.getPlatformName()), IsTackBasicVolumeInfoDataImpl.class.getSimpleName(), startTime,() -> {
                List<VolumeInfoData> list = new ArrayList<>();
                managerData(platform, list);

                if (!list.isEmpty()) {
                    BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                            .metricsType("")
                            .metricsName(BASIC_VOLUME_INFO.code())
                            .build();
                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(build));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                }
            }));
        }
    }

    private void managerData(Platform platform, List<VolumeInfoData> list) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());

        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VOLUMES_DETAIL, param, null)) {
            String responseBody = response.body().string();
            JsonObject jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();
            JsonArray results = jsonObject.get("results").getAsJsonArray();
            results.forEach(info -> {
                VolumeInfoData volumeInfoData = new VolumeInfoData();
                JsonObject resultObj = info.getAsJsonObject();

                // 处理 size 字段
                long total = 0L;
                if (resultObj.has("size") && !resultObj.get("size").isJsonNull()) {
                    try {
                        total = resultObj.get("size").getAsLong() * 1024L * 1024L * 1024L;
                    } catch (Exception e) {
                        log.warn("云盘大小解析失败: {}", resultObj.get("name").getAsString());
                    }
                }

                String type = ""; // 设置默认值
                if (resultObj.has("type") && !resultObj.get("type").isJsonNull()) {
                    try {
                        type = resultObj.get("type").getAsString();
                    } catch (Exception e) {
                        log.warn("云盘类型解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                // 处理 instance_uuid 字段
                String instanceUuid = "";
                if (resultObj.has("instance_uuid") && !resultObj.get("instance_uuid").isJsonNull()) {
                    try {
                        instanceUuid = resultObj.get("instance_uuid").getAsString();
                    } catch (Exception e) {
                        log.warn("实例UUID解析失败: {}", resultObj.get("name").getAsString());
                    }
                } else {
                    log.warn("实例UUID为空: {}", resultObj.get("name").getAsString());
                }
                JsonObject jsonObject1 = vmUse(platform, instanceUuid);
                if (!jsonObject1.isEmpty()){
                    JsonArray rows = jsonObject1.get("results").getAsJsonObject().get("pageResult").getAsJsonObject().get("rows").getAsJsonArray();
                    if (!rows.isEmpty()) {
                        JsonObject asJsonObject = jsonObject1.get("results").getAsJsonObject().get("pageResult").getAsJsonObject().get("rows").getAsJsonArray().get(0).getAsJsonObject();
                        if (type.equals("ROOT")) {
                            // 处理 vm_volume_util 字段
                            String vmVolumeUtil = "0";
                            if (asJsonObject.has("vm_volume_util") && !asJsonObject.get("vm_volume_util").isJsonNull()) {
                                try {
                                    vmVolumeUtil = asJsonObject.get("vm_volume_util").getAsString();
                                } catch (Exception e) {
                                    log.warn("云盘使用率解析失败: {}", resultObj.get("name").getAsString());
                                }
                            }
                            if (StrUtil.isEmpty(vmVolumeUtil)) {
                                vmVolumeUtil = "0";
                            }
                            volumeInfoData.setActualRatio(vmVolumeUtil);
                            volumeInfoData.setType("Root");
                            BigDecimal diskUtilRatio = new BigDecimal(vmVolumeUtil).divide(BigDecimal.valueOf(100));
                            long diskTotal = diskUtilRatio.multiply(BigDecimal.valueOf(total)).setScale(0, RoundingMode.HALF_UP).longValue();
                            volumeInfoData.setActualUse(diskTotal);
                            volumeInfoData.setActualFree(total - diskTotal);
                        } else {
                            String vmDiskUtil = getStringFromJson(asJsonObject, "vm_disk_util_avg", "0");
                            volumeInfoData.setType("Data");
                            volumeInfoData.setActualRatio(vmDiskUtil);
                            BigDecimal voUtilRatio = new BigDecimal(vmDiskUtil).divide(BigDecimal.valueOf(100));
                            long diskTotal = voUtilRatio.multiply(BigDecimal.valueOf(total)).setScale(0, RoundingMode.HALF_UP).longValue();
                            volumeInfoData.setActualUse(diskTotal);
                            volumeInfoData.setActualFree(total - diskTotal);
                        }
                    } else {
                        if (type.equals("ROOT")) {
                            volumeInfoData.setType("Root");
                        } else {
                            volumeInfoData.setType("Data");
                        }
                        volumeInfoData.setActualUse(0L);
                        volumeInfoData.setActualFree(total);
                        volumeInfoData.setActualRatio("0");
                    }
                    String timeStr = resultObj.get("create_time").getAsString() != null ? resultObj.get("create_time").getAsString() : null;
                    Instant instant = null;
                    if (timeStr != null) {
                        instant = Instant.parse(timeStr);
                    }
                    if (instant != null) {
                        Date date1 = Date.from(instant);
                        volumeInfoData.setVCreateDate(date1);
                        volumeInfoData.setVUpdateDate(date1);
                    }
                }
                volumeInfoData.setDescription("IStack云盘描述");
                volumeInfoData.setName(resultObj.get("name").getAsString());
                // 处理 volume_type 字段
                String volumeType = "";
                if (resultObj.has("volume_type") && !resultObj.get("volume_type").isJsonNull()) {
                    try {
                        volumeType = resultObj.get("volume_type").getAsString();
                    } catch (Exception e) {
                        log.warn("云盘格式解析失败: {}", resultObj.get("name").getAsString());
                    }
                }

                volumeInfoData.setFormat(volumeType);
                volumeInfoData.setSize(total);
                volumeInfoData.setActualSize(total);

                volumeInfoData.setState("Enabled");
                // 处理 id 字段
                String uuid = "";
                if (resultObj.has("id") && !resultObj.get("id").isJsonNull()) {
                    try {
                        uuid = resultObj.get("id").getAsString();
                    } catch (Exception e) {
                        log.warn("云盘UUID解析失败: {}", resultObj.get("name").getAsString());
                    }
                }

                volumeInfoData.setUuid(uuid);
                volumeInfoData.setStatus("Ready");
                volumeInfoData.setPlatformId(Convert.toStr(platform.getPlatformId()));
                volumeInfoData.setPlatformName(platform.getPlatformName());
                // 处理 instance_uuid
                String vmInstanceUuid = "";
                if (resultObj.has("instance_uuid") && !resultObj.get("instance_uuid").isJsonNull()) {
                    try {
                        vmInstanceUuid = resultObj.get("instance_uuid").getAsString();
                    } catch (Exception e) {
                        log.warn("实例UUID解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                volumeInfoData.setVmInstanceUuid(vmInstanceUuid);

                // 处理 instance_name
                String vmInstanceName = "";
                if (resultObj.has("instance_name") && !resultObj.get("instance_name").isJsonNull()) {
                    try {
                        vmInstanceName = resultObj.get("instance_name").getAsString();
                    } catch (Exception e) {
                        log.warn("实例名称解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                volumeInfoData.setVmInstanceName(vmInstanceName);


                // 处理 storageid
                String storageUuid = "";
                if (resultObj.has("storageid") && !resultObj.get("storageid").isJsonNull()) {
                    try {
                        storageUuid = resultObj.get("storageid").getAsString();
                    } catch (Exception e) {
                        log.warn("存储UUID解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                volumeInfoData.setPrimaryStorageUuid(storageUuid);

                // 处理 storage
                String storageName = "";
                if (resultObj.has("storage") && !resultObj.get("storage").isJsonNull()) {
                    try {
                        storageName = resultObj.get("storage").getAsString();
                    } catch (Exception e) {
                        log.warn("存储名称解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                volumeInfoData.setPrimaryStorageName(storageName);

                volumeInfoData.setDeleted(0);
                // 处理 harddisk_status
                String hardDiskStatus = "";
                if (resultObj.has("harddisk_status") && !resultObj.get("harddisk_status").isJsonNull()) {
                    try {
                        hardDiskStatus = resultObj.get("harddisk_status").getAsString();
                    } catch (Exception e) {
                        log.warn("硬盘状态解析失败: {}", resultObj.get("name").getAsString());
                    }
                }

                if (hardDiskStatus != null) {
                    volumeInfoData.setIsMount(hardDiskStatus.equals("ATTACHED"));
                } else {
                    volumeInfoData.setIsMount(null);
                }
                volumeInfoData.setMediaType("ssd");
                volumeInfoData.setCreateTime(new Date());
                list.add(volumeInfoData);
            });
        } catch (IOException e) {
            log.error("IsTack平台 云盘 {} 数据采集异常: {}", platform.getPlatformName(), e.getMessage());
        }
    }

    private JsonObject vmUse(Platform platform, String instanceUuid) {
        if (instanceUuid.isEmpty()){
            return new JsonObject();
        }
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        param.put("pageNum", "1");
        param.put("pageSize", "10");
        param.put("poolid", "1");
        param.put("instanceuuid", instanceUuid);
        param.put("downExcelParameters", "vm_disk_util,vm_volume_util");
        param.put("monitorStartTimeStamp", getCurrentDate());
        param.put("monitorEndTimeStamp", getCurrentDate());
        JsonObject jsonObject = new JsonObject();
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_USE, param, null)) {
            String responseBody = response.body().string();
            jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();
        } catch (IOException e) {
            log.error("IsTack平台 {} 数据采集异常: {}", platform.getPlatformName(), e.getMessage());
        }
        return jsonObject;
    }


    public static String getCurrentDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.now().format(formatter);
    }


    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_VOLUME_INFO.code();
    }
}
