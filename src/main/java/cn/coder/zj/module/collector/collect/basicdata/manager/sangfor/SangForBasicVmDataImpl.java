package cn.coder.zj.module.collector.collect.basicdata.manager.sangfor;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.ApiCacheService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.CommonUtil;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StateUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_SXF_VM;

@Slf4j
public class SangForBasicVmDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

         for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VmData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VM.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VmData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie,"CSRFPreventionToken",token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_CLOUDS, null, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<VmData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {

                    VmData vmData = collectVmInfo(platform, jsonElement,headers);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }

            }
        }
        return dataList;
    }


    private JsonObject getJsonNetInfoFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        JsonObject result = new JsonObject();
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);

            if (element != null && element.isJsonObject()) {
                JsonObject dataObj = element.getAsJsonObject().get("data").getAsJsonObject();
                JsonObject supInfo = dataObj.get("ctrl").getAsJsonObject().get("hotplug_sup_info").getAsJsonObject();
                String osName;

                if(supInfo.size() > 0) {
                    osName = supInfo.get("osname").getAsString() + "(" + supInfo.get("sysbit").getAsString() + "-bit)";
                    int netCount = CommonUtil.getIntFromJson(supInfo, "net");

                    if(netCount > 0) {
                        StringBuilder ipBuilder = new StringBuilder();
                        StringBuilder macBuilder = new StringBuilder();

                        for (int i = 0; i < netCount; i++) {
                            String[] netInfos = dataObj.get("net" + i).getAsString().split(",");
                            for (String net : netInfos) {
                                if (net.contains("virtio=")) macBuilder.append(net.replace("virtio=", "")).append(",");
                                if (net.contains("ip=")) ipBuilder.append(net.replace("ip=", "")).append(",");
                            }
                        }

                        String ip = ipBuilder.toString();
                        String mac = macBuilder.toString();

                        if (StringUtils.isNotEmpty(ip)) result.addProperty("ip", ip.substring(0, ip.length() - 1));
                        else result.addProperty("ip", "");

                        if (StringUtils.isNotEmpty(mac)) result.addProperty("mac", mac.substring(0, mac.length() - 1));
                        else result.addProperty("mac", "");
                    }
                } else {
                    osName = dataObj.get("osname").getAsString();
                    String[] netInfos = dataObj.get("net0").getAsString().split(",");
                    result.addProperty("ip", dataObj.get("location").getAsJsonObject().get("ip").getAsString());
                    result.addProperty("mac", netInfos[0].split("=")[1]);
                }

                result.addProperty("osName", osName);
                return result;
            }

            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonObject();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonObject();
        }
    }

    private VmData collectVmInfo(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String platformUrl = platform.getPlatformUrl();
        VmData vmData = new VmData();
        String vmid = getStringFromJson(jsonObject, "vmid", "");

        // 设置MAC地址和操作系统信息
        if (StrUtil.isEmpty(vmid)) {
            JsonObject netInfo = getJsonNetInfoFromApi(platformUrl + SangForApiConstant.GET_VM_CONFIG.replace("{vmid}", vmid), null, headers);
            if (netInfo.size() > 0) {
                vmData.setGuestOsType(getStringFromJson(netInfo, "osName", ""));
                vmData.setIp(getStringFromJson(netInfo, "osName", ""));
                String mac = getStringFromJson(netInfo, "mac", "");
                vmData.setMac(mac == null || mac.equals("") ? "-" : (mac.contains(",") ? mac.split(",")[0].trim() : mac));
            }
        } else {
            JsonObject netCloudInfo = ApiCacheService.getJsonObject(platformUrl + SangForApiConstant.GET_VM_CLOUD_INFO.replace("{vmid}", vmid), null, headers);
            if (netCloudInfo.size() > 0) {
                vmData.setGuestOsType(getStringFromJson(netCloudInfo,"","osname"));
                vmData.setIp(getStringFromJson(netCloudInfo, "ip", ""));
                String asString = null;
                try {
                    JsonElement hardwareStatus = netCloudInfo.get("hardware_status");
                    if (hardwareStatus != null && hardwareStatus.isJsonObject()) {
                        JsonElement net0 = hardwareStatus.getAsJsonObject().get("net0");
                        if (net0 != null && !net0.isJsonNull()) {
                            asString = net0.getAsString();
                        }
                    }
                } catch (Exception e) {
                    log.error("Error parsing hardware_status.net0 from netCloudInfo", e);
                }

                if (asString != null && !asString.isEmpty()) {
                    vmData.setMac(StrUtil.isNotEmpty(asString) ? asString.split(",")[0].split("=")[1] : "-");
                } else {
                    log.warn("Unable to extract net0 value from hardware_status");
                }
            }
        }

        // 设置基本信息
        vmData.setDeleted(0);
        vmData.setUuid(StrUtil.isNotEmpty(getStringFromJson(jsonObject,"uuid","")) ?
                getStringFromJson(jsonObject,"uuid","") : getStringFromJson(jsonObject,"vmid",""));
        vmData.setVms(getStringFromJson(jsonObject,"vmid",""));
        vmData.setClusterName("default cluster");
        vmData.setClusterUuid("default_cluster");
        vmData.setName(getStringFromJson(jsonObject, "name", "-"));

        String state = getStringFromJson(jsonObject, "status", "-");
        vmData.setState(StateUtil.stateConvert(state));
        vmData.setPowerState(StateUtil.powerStateConvert(state));
        vmData.setIp(getStringFromJson(jsonObject, "ip", ""));
        String host = getStringFromJson(jsonObject, "host", "");
        vmData.setHardwareUuid(host);
        vmData.setHardwareName(getStringFromJson(jsonObject, "hostname", ""));
        vmData.setGuestOsType(getStringFromJson(jsonObject, "osVersion", ""));
        vmData.setToolsInstalled(getIntFromJson(jsonObject, "vtool_installed")== 1 ? "true":"false");
        vmData.setToolsRunStatus(getIntFromJson(jsonObject, "vtool_installed")== 1 ? "run" : "stop");

        JsonObject hardware = getJsonObjectFromApi(platformUrl + SangForApiConstant.GET_HARDWARE_INFO.replace("{id}", host), null, headers);
        String cpuType = "";
        if (hardware != null && hardware.has("cpu_status") && !hardware.get("cpu_status").isJsonNull()) {
            JsonObject cpuStatus = hardware.get("cpu_status").getAsJsonObject();
            if (cpuStatus.has("type") && !cpuStatus.get("type").isJsonNull()) {
                cpuType = cpuStatus.get("type").getAsString().toLowerCase();
            }
        }

        vmData.setArchitecture(StrUtil.isNotEmpty(cpuType) &&
                (cpuType.contains("intel") || cpuType.contains("amd") || cpuType.contains("core2duo"))
                ? "x86_64" : "-");
        // 设置HA信息
        Integer ha = getIntFromJson(jsonObject, "ha");
        vmData.setAutoInitType(ha == 0 ? "None" : ha == 1 ? "NeverStop" : "");

        // 设置BIOS信息
        JsonObject bios = getJsonObjectFromApi(platformUrl + SangForApiConstant.GET_VM_CONFIG.replace("{vmid}", vmid), null, headers);
        String uefiBios = getStringFromJson(bios, "uefi_bios", "");
        if(StrUtil.isNotEmpty(uefiBios)) {
            vmData.setGuideMode(uefiBios.equals("0") ? "Legacy" : uefiBios.equals("1") ? "UEFI" : "Other");
        } else {
            vmData.setGuideMode("Other");
        }

        // 设置CPU和内存信息
        vmData.setCpuNum(getIntFromJson(jsonObject, "cpus"));
        vmData.setMemorySize(getLongFromJson(jsonObject, "mem_total"));
        vmData.setCpuUsed(getBigFromJson(jsonObject, "cpu_ratio").multiply(new BigDecimal(100)));
        vmData.setMemoryUsed(getBigFromJson(jsonObject, "mem_ratio").multiply(new BigDecimal(100)));
        vmData.setDiskUsed(getBigFromJson(jsonObject, "disk_used").divide(getBigFromJson(jsonObject, "disk_total"),2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));

        // 设置镜像信息
        String vmType = getStringFromJson(jsonObject,"vmtype","");
        if(StrUtil.isNotEmpty(vmType) && vmType.equals("tpl")){
            vmData.setImageUuid(StrUtil.isNotEmpty(getStringFromJson(jsonObject, "uuid", "")) ?
                    getStringFromJson(jsonObject, "uuid", "") : getStringFromJson(jsonObject, "vmid", ""));
            vmData.setImageName(getStringFromJson(jsonObject, "name", ""));
            vmData.setIso(getStringFromJson(jsonObject, "name", ""));
        }

        // 设置创建日期
        Long createDateStr = getLongFromJson(jsonObject, "create_time") * 1000;
        vmData.setVCreateDate(DateUtil.date(createDateStr != 0L ? DateUtil.parse(StrUtil.toString(createDateStr)) : DateUtil.date()));

        // 设置平台信息
        vmData.setRegionId(platform.getRegionId());
        vmData.setPlatformId(platform.getPlatformId());
        vmData.setPlatformName(platform.getPlatformName());
        vmData.setType("UserVm");
        vmData.setTypeName(platform.getTypeCode());
        // 设置磁盘和网络信息
        BigDecimal diskTotal = getBigFromJson(jsonObject, "disk_total");
        BigDecimal diskUsed = getBigFromJson(jsonObject, "disk_used");
        vmData.setTotalDiskCapacity(diskTotal);
        vmData.setDiskFreeBytes(diskTotal.subtract(diskUsed));
        vmData.setDiskUsedBytes(diskUsed);
        vmData.setNetworkInBytes(getBigFromJson(jsonObject, "netin"));
        vmData.setNetworkOutBytes(getBigFromJson(jsonObject, "netout"));
        vmData.setNetworkInPackets(getBigFromJson(jsonObject, "netin_packet"));
        vmData.setNetworkOutPackets(getBigFromJson(jsonObject, "netout_packet"));

        return vmData;
    }

    @Override
    public String supportProtocol() {
        return BASIC_SXF_VM.code();
    }
}
