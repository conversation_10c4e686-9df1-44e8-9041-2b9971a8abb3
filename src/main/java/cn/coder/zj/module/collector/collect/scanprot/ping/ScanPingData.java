package cn.coder.zj.module.collector.collect.scanprot.ping;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.coder.zj.module.collector.util.NetworkUtil.*;

@Slf4j
public class ScanPingData  {
    public static List<ScanIPData> checkNetworkPing(List<ScanIPData> ipAddresses) {
        if (ipAddresses == null || ipAddresses.isEmpty()) {
            return Collections.emptyList();
        }

        List<List<ScanIPData>> batches = splitIntoBatches(ipAddresses, 20);
        int totalSize = ipAddresses.size();
        Long ipRangeId = ipAddresses.get(0).getIpRangeId();

        AtomicInteger processedCount = new AtomicInteger(0);
        for (List<ScanIPData> batch : batches) {
            // 对当前批次使用并行流进行第一轮扫描
            batch.parallelStream().forEach(ip -> {
                ip.setPingStatus(isPingable(ip.getIpAddress()) ? 1 : 2);
            });

            // 对当前批次中失败的IP进行重试
            batch.parallelStream()
                    .filter(ip -> ip.getPingStatus() == 2)
                    .forEach(ip -> {
                        ip.setPingStatus(isPingable(ip.getIpAddress()) ? 1 : 2);
                    });

            // 更新进度
            processedCount.addAndGet(batch.size());

            sendMessage(ipRangeId, totalSize, processedCount.get(),"ping");
        }
        return ipAddresses;
    }
}