package cn.coder.zj.module.collector.dal.platform;

import cn.coder.zj.module.collector.dal.ClientInfo;
import cn.coder.zj.module.collector.dal.platform.fusionone.FsOnePlatform;
import cn.coder.zj.module.collector.dal.platform.inspur.InSpurPlatform;
import cn.coder.zj.module.collector.dal.platform.istack.IsTackPlatform;
import cn.coder.zj.module.collector.dal.platform.sxf.SxfPlatform;
import cn.coder.zj.module.collector.dal.platform.vmware.VmwarePlatform;
import cn.coder.zj.module.collector.dal.platform.winhong.WinHongPlatform;
import cn.coder.zj.module.collector.dal.platform.zstack.ZsTackPlatform;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;


/**
 * 平台账号实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class Platform extends ClientInfo implements Serializable {

    /**
     * 主键ID
     */
    private Long platformId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 接口URL
     */
    private String platformUrl;

    private Long akType;


    /**
     * 类型编码
     */
    private String typeCode;

    /**
     * 地区id
     */
    private Long regionId;

    private Long state;

    private Date dateTime;

    /**
     * zstack 平台实体类
     */
    private ZsTackPlatform zsTackPlatform;

    /**
     * sxf 平台token
     */
    private SxfPlatform sxfPlatform;

    /**
     * 云宏平台token
     */
    private WinHongPlatform winHongPlatform;

    /**
     *
     */
    private IsTackPlatform isTackPlatform;

    private VmwarePlatform vmWarePlatform;

    private FsOnePlatform fsOnePlatform;

    private InSpurPlatform inSpurPlatform;

}