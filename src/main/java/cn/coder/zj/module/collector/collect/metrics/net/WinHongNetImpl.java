package cn.coder.zj.module.collector.collect.metrics.net;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.util.*;

import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_IN_TASK;
import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_OUT_TASK;
import static cn.coder.zj.module.collector.enums.NetType.PROTOCOL_WIN_HONG_NET;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class WinHongNetImpl extends AbstractMetrics {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();
                Platform platform = (Platform) o;
                String token = platform.getWinHongPlatform().getToken();
                if (token == null) {
                    log.error("平台 {} token为空", platform.getPlatformName());
                    return;
                }
                metricDataList.addAll(vmdata(platform));
                metricDataList.addAll(hostData(platform));

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.NET_TASK );
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                log.info("collectVmData name: {}", Thread.currentThread().getName());
            });
        }
    }
    public List<MetricData> vmdata(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_CLOUDS, null, headers).getAsJsonObject().get("data").getAsJsonArray();

        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();

        //
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);
        List<MetricData> metricDataList = new ArrayList<>();
        for (JsonElement host1 : hostArray) {
            JsonObject host = host1.getAsJsonObject();
            if (!Objects.equals(host.getAsJsonObject().get("status").getAsString(), "1")) {
                continue;
            }
            // 磁盘io
            Map<String, String> param = new HashMap<>();
            param.put("domainIds", host.get("uuid").getAsString());
            param.put("startTime", startTime);
            param.put("endTime", endTime);
            JsonArray networkDList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_NETWORK_FLOW, param, headers).getAsJsonArray();
            if (!networkDList.isEmpty()) {

                int diskI = 0;
                for (JsonElement rate : networkDList) {
                    JsonObject usageItem = rate.getAsJsonObject();
                    JsonArray rdataList = usageItem.get("networkFlowRspList").getAsJsonArray();

                    if(!rdataList.isEmpty()) {
                        for (JsonElement ritem : rdataList) {
                            JsonObject rdata = ritem.getAsJsonObject();

                            JsonArray readBytes = rdata.get("input").getAsJsonObject().get("data").getAsJsonArray();;
                            JsonArray writeBytes = rdata.get("output").getAsJsonObject().get("data").getAsJsonArray();;
                            if (!readBytes.isEmpty()) {
                                for (JsonElement item : readBytes) {
                                    JsonObject cpu = item.getAsJsonObject();
                                    MetricData metricData = new MetricData();
                                    metricData.setPlatformId(platform.getPlatformId());
                                    metricData.setResourceId(usageItem.get("domainId").getAsString());
                                    metricData.setResourceName(usageItem.get("domainName").getAsString());
                                    metricData.setType("vm");
                                    metricData.setMetricName(NETWORK_IN_TASK.code());
                                    metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                    metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));
                                    metricDataList.add(metricData);

                                    break;
                                }
                            }
                            if (!writeBytes.isEmpty()) {
                                for (JsonElement item : writeBytes) {
                                    JsonObject cpu = item.getAsJsonObject();
                                    MetricData metricData = new MetricData();
                                    metricData.setPlatformId(platform.getPlatformId());
                                    metricData.setResourceId(usageItem.get("domainId").getAsString());
                                    metricData.setResourceName(usageItem.get("domainName").getAsString());
                                    metricData.setType("vm");
                                    metricData.setMetricName(NETWORK_OUT_TASK.code());
                                    metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                    metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));
                                    metricDataList.add(metricData);
                                    break;
                                }
                            }
                        }

                    }

                }

            }
        }

        return metricDataList;
    }

    public List<MetricData> hostData(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HARDWARE_LIST, null, headers).getAsJsonObject().get("data").getAsJsonArray();;
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();

        List<MetricData> metricDataList = new ArrayList<>();
        for (JsonElement jsonElement : hostArray) {
            JsonObject host = jsonElement.getAsJsonObject();
            String uuid = host.get("id").getAsString();
            String endTime = Convert.toStr(DateUtil.currentSeconds());
            String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);

            Map<String, String> param = new HashMap<>();
            param.put("hostIds", uuid);
            param.put("endTime", endTime);
            param.put("startTime", startTime);
            JsonArray netList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_FLOW_INFO, param, headers).getAsJsonArray();;
            if (!netList.isEmpty()) {

                int diskI = 0;
                for (JsonElement rate : netList) {
                    JsonObject usageItem = rate.getAsJsonObject();
                    JsonArray userItemData = usageItem.get("networkFlowRspList").getAsJsonArray();
                    for (JsonElement item2 : userItemData) {
                        JsonObject itemJson = item2.getAsJsonObject();
//                        metricData.setResourceName(itemJson.get("devName").getAsString());

                        JsonArray readBytes = itemJson.get("input").getAsJsonObject().get("data").getAsJsonArray();
                        JsonArray writeBytes = itemJson.get("output").getAsJsonObject().get("data").getAsJsonArray();
                        if (!readBytes.isEmpty()) {
                            for (JsonElement item : readBytes) {
                                JsonObject cpu = item.getAsJsonObject();
                                MetricData metricData = new MetricData();
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setResourceId(itemJson.get("hostId").getAsString());
                                metricData.setResourceName(itemJson.get("hostName").getAsString());
                                metricData.setType("host");
                                metricData.setMetricName(NETWORK_IN_TASK.code());
                                metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));

                                metricDataList.add(metricData);
                                break;
                            }
                        }
                        if (!writeBytes.isEmpty()) {
                            for (JsonElement item : writeBytes) {
                                JsonObject cpu = item.getAsJsonObject();
                                MetricData metricData = new MetricData();
                                metricData.setPlatformId(platform.getPlatformId());
                                metricData.setResourceId(itemJson.get("hostId").getAsString());
                                metricData.setResourceName(itemJson.get("hostName").getAsString());
                                metricData.setType("host");
                                metricData.setMetricName(NETWORK_OUT_TASK.code());
                                metricData.setTimestamps(Collections.singletonList(DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000));
                                metricData.setValues(Collections.singletonList(cpu.get("value").getAsDouble()));
                                metricDataList.add(metricData);
                                break;
                            }
                        }
                    }

                }

            }
        }
        return metricDataList;
    }
    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            return GSON.fromJson(responseBody, JsonElement.class);
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }



    @Override
    public String supportProtocol() {
        return PROTOCOL_WIN_HONG_NET.code();
    }
}
