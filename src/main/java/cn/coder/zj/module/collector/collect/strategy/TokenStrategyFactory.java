package cn.coder.zj.module.collector.collect.strategy;


import cn.coder.zj.module.collector.collect.token.AbstractToken;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.util.ServiceLoader;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
@Order(value = Ordered.HIGHEST_PRECEDENCE)
public class TokenStrategyFactory implements CommandLineRunner {


    /**
     * strategy container
     */
    private static final ConcurrentHashMap<String, AbstractToken> COLLECT_STRATEGY = new ConcurrentHashMap<>();

    /**
     * get instance of this protocol collection
     *
     * @param protocol collect protocol
     * @return implement of Metrics Collection
     */
    public static AbstractToken invoke(String protocol) {
        return COLLECT_STRATEGY.get(protocol);
    }

    @Override
    public void run(String... args) throws Exception {
        // spi load and registry protocol and collect instance
        ServiceLoader<AbstractToken> loader = ServiceLoader.load(AbstractToken.class, AbstractToken.class.getClassLoader());
        for (AbstractToken collect : loader) {
            COLLECT_STRATEGY.put(collect.supportProtocol(), collect);
        }
    }

}
