package cn.coder.zj.module.collector.collect.token.winhong;

import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.winhong.WinHongPlatform;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.PlatformType.WIN_HONG;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class WinHongTokenImpl extends AbstractToken {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void token(Platform platform) {
        Map<String,String> map = new HashMap<>();
        map.put("user", platform.getUsername());
        map.put("pwd", platform.getPassword());
        try {
            OkHttpService.postJson(platform.getPlatformUrl() + WinHongApiConstant.LOGIN, map, new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    log.error("登录请求失败: {}", e.getMessage());
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        if (!response.isSuccessful()) {
                            updatePlatformStatus(platform, false);
                            return;
                        }

                        String responseBody = response.body().string();
                        JsonObject jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();
                        if (jsonObject.isEmpty() && jsonObject.has(jsonObject.get("sessionId").getAsString())) {
                            log.error("登录响应数据格式错误: {}", responseBody);
                            return;
                        }
                        platform.setWinHongPlatform(WinHongPlatform.builder().token(jsonObject.get("sessionId").getAsString()).build());
                        Map<String, Object> tokenMap = new HashMap<>();
                        tokenMap.put(platform.getPlatformId().toString(), platform);
                        CacheService.put(WIN_HONG.code(), tokenMap);
                        log.info("云宏登录成功，token已缓存");
                        updatePlatformStatus(platform, true);
                    } catch (Exception e) {
                        log.error("处理登录响应数据失败: {}", e.getMessage());
                        updatePlatformStatus(platform, false);
                    }
                }
            });

        } catch (Exception e) {
            log.info("获取授权异常：" + e.getMessage());
            updatePlatformStatus(platform, false);
        }
    }



    /**
     * 发送平台异常信息
     */
    private void updatePlatformStatus(Platform platform, boolean isOnline) {
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        if (isOnline) {
            platform.setState(0L);
        } else {
            platform.setState(1L);
        }
        platform.setDateTime(new Date());
        sendMessageService.sendMessage(CacheService.getCtx("ctx"), ClusterMsg.Message.newBuilder().setData(GsonUtil.GSON.toJson(platform)).setType(ClusterMsg.MessageType.DETECT).setTime(System.currentTimeMillis()).build());
    }


    @Override
    public String supportProtocol() {
        return WIN_HONG.code();
    }
}
