package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.metrics.cpu.FsOneCpuImpl;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.StorageData;
import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_STORAGE;

@Slf4j
public class FsOneBasicStorageDataImpl extends AbstractBasicData {


    private long startTime;


    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformInfo : platformList) {
            Platform platform = (Platform) platformInfo;
            taskExecutor.execute(TimeUtils.withExecutionTime(
                    String.format("基础数据处理[%s]", platform.getPlatformName()), FsOneCpuImpl.class.getSimpleName(), startTime, () -> {
                        List<StorageData> dataList = collectData(platformInfo);
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(dataList)
                                .metricsName(BASIC_STORAGE.code())
                                .build();
                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                    })
            );
        }

    }

    private List<StorageData> collectData(Object platformInfo) {
        List<StorageData> storageList = Lists.newArrayList();
        List<StorageHostRelationData> relationDataList = Lists.newArrayList();
        Platform platform = (Platform) platformInfo;
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String url = platform.getPlatformUrl().concat(FusionOneApiConstant.DATASTORES_LIST.replace("{siteId}",
                platform.getFsOnePlatform().getSiteId()));
        Map<String, String> header = new HashMap<>(3);
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject jsonObject = FsApiCacheService.getJsonObject(url, null, header);
        assert jsonObject != null;
        JsonArray stores = jsonObject.getAsJsonArray("datastores");
        //主机信息
        String hostUrl = platform.getPlatformUrl().concat(FusionOneApiConstant.GET_HOST_LIST.replace("{siteId}",
                platform.getFsOnePlatform().getSiteId()));
        JsonObject hostJson = FsApiCacheService.getJsonObject(hostUrl, null, header);
        assert hostJson != null;
        JsonArray hosts = hostJson.getAsJsonArray("hosts");
        Map<String, JsonObject> hostMap = IntStream.range(0, hosts.size())
                .mapToObj(i -> hosts.get(i).getAsJsonObject())
                .collect(Collectors.toMap(host -> host.get("urn").getAsString(), host -> host));
        String siteName = platform.getFsOnePlatform().getSiteName();
        for (JsonElement store : stores){
            JsonObject object = store.getAsJsonObject();
            StorageData build = StorageData.builder()
                    .name(object.get("name").getAsString())
                    .uuid(object.get("urn").getAsString())
                    .url("not used")
                    .state("NORMAL".equals(object.get("status").getAsString()) ? "Enabled" : "Disabled")
                    .status("NORMAL".equals(object.get("status").getAsString()) ? "Connected" : "Disconnected")
                    .totalCapacity(object.get("actualCapacityGB").getAsLong() * 1024 * 1024 * 1024)
                    .usedCapacity(object.get("usedSizeGB").getAsLong() * 1024 * 1024 * 1024)
                    .availableCapacity(object.get("actualFreeSizeGB").getAsBigDecimal()
                            .multiply(BigDecimal.valueOf(1024))
                            .multiply(BigDecimal.valueOf(1024))
                            .multiply(BigDecimal.valueOf(1024)))
                    .capacityUtilization(calculateUsageRate(object))
                    .totalPhysicalCapacity(object.get("actualCapacityGB").getAsBigDecimal()
                            .multiply(BigDecimal.valueOf(1024))
                            .multiply(BigDecimal.valueOf(1024))
                            .multiply(BigDecimal.valueOf(1024)))
                    .availablePhysicalCapacity(object.get("actualFreeSizeGB").getAsBigDecimal()
                            .multiply(BigDecimal.valueOf(1024))
                            .multiply(BigDecimal.valueOf(1024))
                            .multiply(BigDecimal.valueOf(1024)))
                    .type("其他存储")
                    .platformId(platform.getPlatformId())
                    .platformName(platform.getPlatformName())
                    .regionId(platform.getRegionId())
                    .deleted(0)
                    .typeName("FusionOne")
                    .createTime(new Date())
                    .sCreateTime(new Date())
                    .vUpdateTime(new Date())
                    .mediaType("机械盘")
                    .manager(platform.getPlatformName())
                    .availableManager(siteName)
                    .storagePercent(new BigDecimal(1))
                    .remark(Optional.ofNullable(object.get("description"))
                            .map(JsonElement::getAsString)
                            .orElse(""))
                    .virtualCapacity(calculateVirtualCapacity(object))
                    .allocation(calculateAllocation(object))
                    .commitRate(commitRate(object))
                    .build();
            if (!hosts.isEmpty()) {
                String hardwareUuid = null;
                if (BeanUtil.isNotEmpty(hostMap.get(hosts.get(0).getAsJsonObject().get("urn").getAsString()))) {
                    JsonObject urn = hostMap.get(hosts.get(0).getAsJsonObject().get("urn").getAsString());
                    hardwareUuid = urn.get("uuid").getAsString();
                }
                StorageHostRelationData relationData =
                         StorageHostRelationData.builder()
                        .hardwareUuid(hardwareUuid)
                        .storageUuid(object.get("urn").getAsString())
                        .platformId(platform.getPlatformId())
                        .platformName(platform.getPlatformName())
                        .updateTime(new Date())
                        .build();
                relationDataList.add(relationData);
                build.setStorageHostRelationDataList(relationDataList);
            }
            storageList.add(build);
        }
        return storageList;
    }

    /**
     * 计算分配容量
     */
    private static BigDecimal commitRate(JsonObject object) {
        BigDecimal availableDecimal = object.get("actualFreeSizeGB").getAsBigDecimal()
                .multiply(BigDecimal.valueOf(1024))
                .multiply(BigDecimal.valueOf(1024))
                .multiply(BigDecimal.valueOf(1024));
        return calculateAllocation(object)
                .divide(availableDecimal, 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算区域
     */
    private static BigDecimal calculateAllocation(JsonObject object) {
        BigDecimal totalPhysicalCapacity = object.get("actualCapacityGB").getAsBigDecimal()
                .multiply(BigDecimal.valueOf(1024))
                .multiply(BigDecimal.valueOf(1024))
                .multiply(BigDecimal.valueOf(1024));
        BigDecimal availableDecimal = object.get("actualFreeSizeGB").getAsBigDecimal()
                .multiply(BigDecimal.valueOf(1024))
                .multiply(BigDecimal.valueOf(1024))
                .multiply(BigDecimal.valueOf(1024));
        return totalPhysicalCapacity.subtract(availableDecimal);
    }

    /**
     * 计算虚拟容量
     */
    private static BigDecimal calculateVirtualCapacity(JsonObject jsonObject) {
        BigDecimal totalPhysicalCapacity = jsonObject.get("actualCapacityGB").getAsBigDecimal()
                .multiply(BigDecimal.valueOf(1024))
                .multiply(BigDecimal.valueOf(1024))
                .multiply(BigDecimal.valueOf(1024));
        return totalPhysicalCapacity.multiply(new BigDecimal(1));
    }

    /**
     * 计算使用率
     * @param jsonObject
     * @return
     */
    public static BigDecimal calculateUsageRate(JsonObject jsonObject) {
        BigDecimal totalCapacity = jsonObject.get("actualCapacityGB").getAsBigDecimal();
        long usedCapacity = jsonObject.get("usedSizeGB").getAsLong();
        return (totalCapacity != null && totalCapacity.compareTo(BigDecimal.ZERO) > 0)
                ? BigDecimal.valueOf(usedCapacity).divide(totalCapacity, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
                : BigDecimal.ZERO;
    }


    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_STORAGE.code();
    }
}
