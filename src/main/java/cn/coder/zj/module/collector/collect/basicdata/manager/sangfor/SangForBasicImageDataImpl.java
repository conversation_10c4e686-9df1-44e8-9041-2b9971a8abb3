package cn.coder.zj.module.collector.collect.basicdata.manager.sangfor;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.OsType;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_IMAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_SXF_IMAGE;

@Slf4j
public class SangForBasicImageDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<ImageData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_IMAGE.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<ImageData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie,"CSRFPreventionToken",token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_CLOUDS, null, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<ImageData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
                    String vmtype = getStringFromJson(jsonObject, "vmtype", "");
                    if(StrUtil.isNotEmpty(vmtype)){
                        if (!vmtype.equals("tpl")){
                            continue;
                        }
                    }else{
                        continue;
                    }

                    ImageData imageData = collectImageData(platform, jsonObject);
                    if (imageData != null) {
                        dataList.add(imageData);
                    }
                } catch (Exception e) {
                    log.error("处理深信服镜像数据异常, hostMap: {}, error: {}", jsonElement.toString(), e.getMessage());
                }
            }
        }
        return dataList;
    }

    private ImageData collectImageData(Platform platform, JsonObject jsonObject) {
        ImageData imageInfo = new ImageData();
        // 设置基本信息
        imageInfo.setUuid(StrUtil.isNotEmpty(getStringFromJson(jsonObject,"uuid","")) ?
                getStringFromJson(jsonObject,"uuid","") : getStringFromJson(jsonObject,"vmid",""));
        imageInfo.setName(getStringFromJson(jsonObject,"name",""));
        imageInfo.setStatus("Enabled");
        imageInfo.setFormat("qcow2");
        imageInfo.setCpuArch("x86_64");
        String osname = getStringFromJson(jsonObject, "osname", "");
        imageInfo.setOsType(osname);
        if (StrUtil.isNotEmpty(osname)) {
            imageInfo.setApplicationPlatform(OsType.getPlatform(osname));
        }

        // 设置大小和类型信息
        imageInfo.setSize(getLongFromJson(jsonObject,"disk_total"));
        imageInfo.setImageType("RootVolumeTemplate");

        //共享范围
        String share = getStringFromJson(jsonObject,"cfgstorageshared","");
        imageInfo.setSharingScope("1".equals(share) ? "不共享" : "2".equals(share) ? "共享" : "");

        // 设置其他属性
        imageInfo.setOsLanguage("");
        imageInfo.setMinMemory(getBigFromJson(jsonObject, "mem_total"));
        imageInfo.setMinDisk(getBigFromJson(jsonObject, "disk_total"));

        imageInfo.setDiskDriver("");
        imageInfo.setNetworkDriver("");
        imageInfo.setBootMode("");
        imageInfo.setRemoteProtocol(getStringFromJson(jsonObject,"graphic_type",""));

        // 设置平台信息
        imageInfo.setPlatformId(platform.getPlatformId());
        imageInfo.setPlatformName(platform.getPlatformName());

        Long timestamp = getLongFromJson(jsonObject, "create_time") * 1000;
        imageInfo.setVCreateDate(DateUtil.date(timestamp != 0L ? DateUtil.parse(StrUtil.toString(timestamp)) : DateUtil.date()));
        imageInfo.setVUpdateDate(DateUtil.date(timestamp != 0L ? DateUtil.parse(StrUtil.toString(timestamp)) : DateUtil.date()));

        return imageInfo
                ;
    }

    @Override
    public String supportProtocol() {
        return BASIC_SXF_IMAGE.code();
    }
}
