package cn.coder.zj.module.collector.collect.metrics.mem;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper.*;
import static cn.coder.zj.module.collector.enums.MemType.PROTOCOL_IN_SPUR_MEM;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.ApiUtil.getInSpurJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getBigFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class InSpurMemImpl extends AbstractMetrics implements MetricsCollectHelper.MetricsHandler {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) platformObj;
                List<MetricData> metricDataList = collectData(platform);
                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.MEM_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮-MEM性能采集 {} 秒,{} 平台", endTimeFormatted,platform.getPlatformName());
            });
        }
    }

    private List<MetricData> collectData(Platform platformObj) {
        List<MetricData> list = new ArrayList<>();
        list.addAll(collectInSpurVmData(platformObj, "MEM",this));
        list.addAll(collectInSpurHostData(platformObj, "MEM",this));
        return list;
    }


    @Override
    public String supportProtocol() {
        return PROTOCOL_IN_SPUR_MEM.code();
    }

    @Override
    public List<MetricData> handleVmMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String id = getStringFromJson(cloud, "id");
        BigDecimal memoryInByte = getBigFromJson(cloud, "memoryInByte");
        Map<String, String> params = Map.of(
                "targetType", "VM",
                "type", "0",
                "itemIns", "mem",
                "objuuid", id,
                "section","10"
        );

        JsonArray valueArray = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_REALTIME_DATA, params, headers);
        MetricData metricData = createData(platform, cloud, MEM_USAGE_TASK.code(),"vm");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonObject asJsonObject = element.getAsJsonObject();
            String key = getStringFromJson(asJsonObject, "key");
            JsonArray perfPoints = asJsonObject.getAsJsonArray("perfPoints");
            if ("mem".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal memoryUsed = getBigFromJson(lastPoint, "data");
                    BigDecimal useNum = memoryInByte.multiply(memoryUsed).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal freeNum = memoryInByte.subtract(useNum).setScale(2, RoundingMode.HALF_UP);

                    MetricData used = BeanUtil.copyProperties(metricData, MetricData.class);
                    used.setValues(Arrays.asList(memoryUsed.doubleValue()));
                    metricDataList.add(used);

                    MetricData free = BeanUtil.copyProperties(metricData, MetricData.class);
                    free.setMetricName(MEM_FREE_TASK.code());
                    free.setValues(Arrays.asList(freeNum.doubleValue()));
                    metricDataList.add(free);

                    MetricData use = BeanUtil.copyProperties(metricData, MetricData.class);
                    use.setMetricName(MEM_USED_TASK.code());
                    use.setValues(Arrays.asList(useNum.doubleValue()));
                    metricDataList.add(use);
                }
            }
        }
        return metricDataList;
    }

    @Override
    public List<MetricData> handleHostMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject host = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String id = getStringFromJson(host, "id");
        BigDecimal memoryInByte = getBigFromJson(host, "totalMemInByte");
        Map<String, String> params = Map.of(
                "targetType", "HOST",
                "type", "0",
                "itemIns", "mem",
                "objuuid", id,
                "section","10"
        );

        JsonArray valueArray = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_REALTIME_DATA, params, headers);
        MetricData metricData = createData(platform, host, MEM_USAGE_TASK.code(),"host");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonObject asJsonObject = element.getAsJsonObject();
            String key = getStringFromJson(asJsonObject, "key");
            JsonArray perfPoints = asJsonObject.getAsJsonArray("perfPoints");
            if ("mem".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal memoryUsed = getBigFromJson(lastPoint, "data");
                    BigDecimal useNum = memoryInByte.multiply(memoryUsed).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal freeNum = memoryInByte.subtract(useNum).setScale(2, RoundingMode.HALF_UP);

                    MetricData used = BeanUtil.copyProperties(metricData, MetricData.class);
                    used.setValues(Arrays.asList(memoryUsed.doubleValue()));
                    metricDataList.add(used);

                    MetricData free = BeanUtil.copyProperties(metricData, MetricData.class);
                    free.setMetricName(MEM_FREE_TASK.code());
                    free.setValues(Arrays.asList(freeNum.doubleValue()));
                    metricDataList.add(free);

                    MetricData use = BeanUtil.copyProperties(metricData, MetricData.class);
                    use.setMetricName(MEM_USED_TASK.code());
                    use.setValues(Arrays.asList(useNum.doubleValue()));
                    metricDataList.add(use);
                }
            }
        }
        return metricDataList;
    }
}
