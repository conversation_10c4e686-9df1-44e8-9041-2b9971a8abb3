package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.StorageData;
import cn.iocoder.zj.framework.common.dal.manager.StorageHostRelationData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_STORAGE;

@Slf4j
public class IsTackBasicStorageDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK2.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            Platform platform = (Platform) o;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(TimeUtils.withExecutionTime(
                    String.format("プラットフォーム[%s]のデータ処理", platform.getPlatformName()), IsTackBasicStorageDataImpl.class.getSimpleName(), startTime,() -> {
                List<StorageData> dataList = new ArrayList<>();


                Map<String, String> hard = new HashMap<>();
                hard.put("os_id", platform.getPassword());
                hard.put("ct_user_id", platform.getUsername());

                try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_STORAGE_LIST, hard, null)) {
                    JsonObject jsonObject = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
                    if (!jsonObject.has("results") || jsonObject.get("results").isJsonNull()) {
                        return;
                    }
                    JsonArray results = jsonObject.get("results").getAsJsonArray();
                    if (results.isEmpty()) {
                        log.warn("平台 {} 返回空数据集", platform.getPlatformName());
                        return;
                    }

                    results.forEach(jsonElement -> {
                        JsonObject storage = jsonElement.getAsJsonObject();
                        ZonedDateTime zonedDateTime = ZonedDateTime.parse(storage.get("create_time").getAsString(), DateTimeFormatter.ISO_DATE_TIME);
                        Date date = Date.from(zonedDateTime.toInstant());

                        StorageData storageData = buildStorageData(storage, platform, date);
                        StorageHostRelation(platform, storageData);
                        dataList.add(storageData);
                    });


                    if (!dataList.isEmpty()) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(dataList)
                                .metricsName(BASIC_STORAGE.code())
                                .build();
                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                    }
                } catch (IOException e) {
                    log.error("error collecting host basic data: {}", e.getMessage());
                }

            }));
        }

    }


    private StorageData buildStorageData(JsonObject storage,Platform platform, Date date) {
        BigDecimal availableCapacity = storage.get("capacity_size").getAsBigDecimal().subtract(storage.get("capacity_allocate_used").getAsBigDecimal());
        BigDecimal totalPhysicalCapacity = storage.get("capacity_size").getAsBigDecimal();
        BigDecimal storagePercent = new BigDecimal(1);
        BigDecimal virtualCapacity = totalPhysicalCapacity.multiply(storagePercent);
        BigDecimal allocation = totalPhysicalCapacity.subtract(availableCapacity.compareTo(BigDecimal.ZERO) > 0 ? availableCapacity : BigDecimal.ZERO);
        BigDecimal commitRate = allocation.compareTo(BigDecimal.ZERO) > 0
                ? allocation.divide(availableCapacity.compareTo(BigDecimal.ZERO) > 0 ? availableCapacity : BigDecimal.ONE, 2, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;

        return StorageData.builder()
                .name(storage.get("name").getAsString())
                .uuid(storage.get("uuid").getAsString())
                .url("not used")
                .state("Enabled")
                .status("Connected")
                .totalCapacity(storage.get("capacity_size").getAsLong())
                .usedCapacity(storage.get("capacity_allocate_used").getAsLong())
                .availableCapacity(availableCapacity)
                .capacityUtilization(storage.get("capacity_allocate_used_rate").getAsBigDecimal().multiply(new BigDecimal(100)))
                .totalPhysicalCapacity(totalPhysicalCapacity)
                .availablePhysicalCapacity(totalPhysicalCapacity.subtract(storage.get("capacity_used").getAsBigDecimal()))
                .type("其他存储")
                .platformId(platform.getPlatformId())
                .platformName(platform.getPlatformName())
                .regionId(platform.getRegionId())
                .deleted(0)
                .typeName("istack")
                .createTime(date)
                .sCreateTime(date)
                .vUpdateTime(date)
                .mediaType("机械盘")
                .manager(platform.getPlatformName())
                .availableManager(storage.get("os_id").getAsString())
                .storagePercent(storagePercent)
                .remark(storage.get("remark").getAsString())
                .virtualCapacity(virtualCapacity)
                .allocation(allocation)
                .commitRate(commitRate)
                .build();
    }


    private void StorageHostRelation(Platform platform, StorageData storageData) {
        List<StorageHostRelationData> storageHostRelationDataList = new ArrayList<>();
        platform.getIsTackPlatform().getHostUuids().forEach(info -> {
            Map<String, String> hard = new HashMap<>();
            hard.put("os_id", platform.getPassword());
            hard.put("ct_user_id", platform.getUsername());
            hard.put("uuid", info.getUuid());
            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_HARDWARE, hard, null)) {
                JsonObject jsonObject = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
                if (!jsonObject.has("results") || jsonObject.get("results").isJsonNull()) {
                    log.warn("平台 {} 主机 {} 没有数据返回", platform.getPlatformName(), info.getUuid());
                    return;
                }
                JsonArray results = jsonObject.get("results").getAsJsonArray();
                if (results.isEmpty()) {
                    log.warn("平台 {} 主机 {} 返回空数据集", platform.getPlatformName(), info.getUuid());
                    return;
                }

                results.forEach(jsonElement -> {
                    JsonObject host = jsonElement.getAsJsonObject();
                    String hostUuid = host.get("uuid").getAsString();
                    String dcUuid = host.get("dc_uuid").getAsString();
                    if (storageData.getAvailableManager().equals(dcUuid)) {

                        storageHostRelationDataList.add(StorageHostRelationData.builder().hardwareUuid(hostUuid)
                                .storageUuid(storageData.getUuid())
                                .platformId(platform.getPlatformId())
                                .platformName(platform.getPlatformName())
                                .build());
                    }
                });

            } catch (IOException e) {
                log.error("error collecting host basic data: {}", e.getMessage());
            }
        });
        storageData.setStorageHostRelationDataList(storageHostRelationDataList);
    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_STORAGE.code();
    }
}
