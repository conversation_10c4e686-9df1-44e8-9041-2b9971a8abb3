package cn.coder.zj.module.collector.service.asyncDemo;

import cn.coder.zj.module.collector.util.GsonUtil;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

/**
 * <AUTHOR>
 */
@Slf4j
public class OkHttpService {

    private static OkHttpClient okHttpClient;

    static {
        try {
            // 创建信任所有证书的 SSLContext
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public X509Certificate[] getAcceptedIssuers() {
                            return new X509Certificate[0];
                        }
                    }
            };

            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            // 创建 SSLSocketFactory
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();

            okHttpClient = new OkHttpClient.Builder()
                    .sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0])
                    .hostnameVerifier((hostname, session) -> true)
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public OkHttpService() {
        okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }


    private static String buildUrl(String url, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return url;
        }
        StringBuilder sb = new StringBuilder(url).append("?");
        params.forEach((key, value)
                -> sb.append(URLEncoder.encode(key, StandardCharsets.UTF_8))
                .append("=")
                .append(URLEncoder.encode(value, StandardCharsets.UTF_8))
                .append("&"));
        return sb.deleteCharAt(sb.length() - 1).toString();
    }

    /**
     * GET请求
     *
     * @param url      请求地址
     * @param params   请求参数
     * @param callback 回调函数
     */
    public static void get(String url, Map<String, String> params, Map<String, String> headers, Callback callback) {
        if (url == null || callback == null) {
            throw new IllegalArgumentException("URL or Callback cannot be null");
        }
        Request.Builder requestBuilder = new Request.Builder()
                .get()
                .url(buildUrl(url, params));
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }
        Request request = requestBuilder.build();
        okHttpClient.newCall(request).enqueue(callback);
    }

    /**
     * POST请求
     *
     * @param url      请求地址
     * @param params   请求参数
     * @param callback 回调函数
     */
    public static void post(String url, Map<String, String> params, Callback callback) {
        if (url == null || callback == null) {
            throw new IllegalArgumentException("URL or Callback cannot be null");
        }
        FormBody.Builder formBuilder = new FormBody.Builder();
        if (params != null) {
            params.forEach(formBuilder::add);
        }
        Request request = new Request.Builder()
                .post(formBuilder.build())
                .url(url)
                .build();
        okHttpClient.newCall(request).enqueue(callback);
    }

    /**
     * 同步GET请求
     */
    public static Response getSync(String url, Map<String, String> params, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .get()
                .url(buildUrl(url, params));
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }
        Request request = requestBuilder.build();
        return okHttpClient.newCall(request).execute();
    }

    /**
     * 同步POST请求
     */
    public static Response postSync(String url, Map<String, String> params) throws IOException {
        FormBody.Builder formBuilder = new FormBody.Builder();
        if (params != null) {
            params.forEach(formBuilder::add);
        }
        Request request = new Request.Builder()
                .post(formBuilder.build())
                .url(url)
                .build();
        return okHttpClient.newCall(request).execute();
    }

    public static void postSyncHead(String url, Map<String, String> params, Map<String, String> headers, Callback callback) {
        FormBody.Builder formBuilder = new FormBody.Builder();
        if (params != null) {
            params.forEach(formBuilder::add);
        }

        Request.Builder requestBuilder = new Request.Builder()
                .post(formBuilder.build())
                .url(url);

        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();
        okHttpClient.newCall(request).enqueue(callback);
    }

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    /**
     * JSON格式的POST请求
     */
    public static void postJson(String url, Map<String, String> params, Callback callback) {
        if (url == null || callback == null) {
            throw new IllegalArgumentException("URL or Callback cannot be null");
        }
        
        // 将Map转换为JSON字符串
        String jsonBody = GsonUtil.GSON.toJson(params);
        
        RequestBody body = RequestBody.create(JSON, jsonBody);
        Request request = new Request.Builder()
                .post(body)
                .url(url)
                .addHeader("Content-Type", "application/json")
                .build();
                
        okHttpClient.newCall(request).enqueue(callback);
    }

    public static void postJsonHeader(String url, Map<String, String> params, Map<String, String> headers, Callback callback) {
        if (url == null || callback == null) {
            throw new IllegalArgumentException("URL or Callback cannot be null");
        }

        String jsonBody = GsonUtil.GSON.toJson(params);
        RequestBody body = RequestBody.create(JSON, jsonBody);

        Request.Builder requestBuilder = new Request.Builder()
                .post(body)
                .url(url);

        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = requestBuilder.build();
        okHttpClient.newCall(request).enqueue(callback);
    }

    /**
     * 同步JSON格式POST请求
     */
    public static Response postJsonSync(String url, Map<String, String> params) throws IOException {
        String jsonBody = GsonUtil.GSON.toJson(params);
        RequestBody body = RequestBody.create(JSON, jsonBody);
        
        Request request = new Request.Builder()
                .post(body)
                .url(url)
                .addHeader("Content-Type", "application/json")
                .build();
                
        return okHttpClient.newCall(request).execute();
    }

    public static Response postJsonFs(String url, Map<String, String> headers, JsonArray requestArray) throws IOException {
        RequestBody body = RequestBody.create(
                MediaType.parse("application/json; charset=utf-8"),
                requestArray.toString()
        );
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);
        headers.forEach(requestBuilder::addHeader);
        return okHttpClient.newCall(requestBuilder.build()).execute();
    }
}
