package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.time.Instant;
import java.util.*;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SNAPSHOT;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_SNAPSHOT;

@Slf4j
public class IsTackBasicSnapshotDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK2.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(TimeUtils.withExecutionTime(
                    String.format("プラットフォーム[%s]のデータ処理", platform.getPlatformName()), IsTackBasicSnapshotDataImpl.class.getSimpleName(), startTime,() -> {
                List<VolumeSnapshotData> list = new ArrayList<>();

                managerData(platform, list);
                if (!list.isEmpty()) {
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(list)
                            .metricsName(BASIC_SNAPSHOT.code())
                            .build()));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                }
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
            }));
        }
    }

    private void managerData(Platform platform, List<VolumeSnapshotData> list) {

        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VOLUME_SNAPSHOT_LIST, param, null)) {
            JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
            if (!asJsonArray.isEmpty()) {
                asJsonArray.forEach(jsonElement -> {
                    JsonObject resultObj = jsonElement.getAsJsonObject();
                    VolumeSnapshotData volumeSnapshotDTO = snapshotInfo(resultObj, platform, "云盘快照");
                    list.add(volumeSnapshotDTO);
                });
            }
        } catch (IOException e) {
            log.error("平台 {} 连接超时", platform.getPlatformName());
        }

        Map<String, String> Snapshot = new HashMap<>();
        Snapshot.put("os_id", platform.getPassword());
        Snapshot.put("ct_user_id", platform.getUsername());
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_INSTANCE_SNAPSHOT_LIST, Snapshot, null)) {
            JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
            if (!asJsonArray.isEmpty()) {
                asJsonArray.forEach(jsonElement -> {
                    JsonObject resultObj = jsonElement.getAsJsonObject();
                    VolumeSnapshotData volumeSnapshotDTO = snapshotInfo(resultObj, platform, "主机快照");
                    list.add(volumeSnapshotDTO);
                });
            }
        } catch (IOException e) {
            log.error("平台 {} 连接超时", platform.getPlatformName());
        }

    }

    private VolumeSnapshotData snapshotInfo(JsonObject jsonObject, Platform platform, String type) {
        // 基础字段处理
        String uuid = "";
        String name = "";
        String description = "";
        if (jsonObject.has("uuid") && !jsonObject.get("uuid").isJsonNull()) {
            uuid = jsonObject.get("uuid").getAsString();
        }
        if (jsonObject.has("name") && !jsonObject.get("name").isJsonNull()) {
            name = jsonObject.get("name").getAsString();
        }
        if (jsonObject.has("description") && !jsonObject.get("description").isJsonNull()) {
            description = jsonObject.get("description").getAsString();
        }

        // 实例相关字段处理
        String instanceUuid = "";
        String instanceName = "";
        if (jsonObject.has("instance_uuid") && !jsonObject.get("instance_uuid").isJsonNull()) {
            instanceUuid = jsonObject.get("instance_uuid").getAsString();
        }
        if (jsonObject.has("instance_name") && !jsonObject.get("instance_name").isJsonNull()) {
            instanceName = jsonObject.get("instance_name").getAsString();
        }

        // 卷相关字段处理
        String volumeUuid = "";
        String volumeName = "";
        String volumeType = "";
        if (jsonObject.has("source_disk_id") && !jsonObject.get("source_disk_id").isJsonNull()) {
            volumeUuid = jsonObject.get("source_disk_id").getAsString();
        }
        if (jsonObject.has("source_disk_name") && !jsonObject.get("source_disk_name").isJsonNull()) {
            volumeName = jsonObject.get("source_disk_name").getAsString();
        }
        if (jsonObject.has("source_disk_type") && !jsonObject.get("source_disk_type").isJsonNull()) {
            String diskType = jsonObject.get("source_disk_type").getAsString();
            volumeType = "ROOT".equals(diskType) ? "Root" : "Data";
        }

        // 处理 size 字段
        long size = 0L;
        if (jsonObject.has("size") && !jsonObject.get("size").isJsonNull()) {
            try {
                size = jsonObject.get("size").getAsLong() * 1024L * 1024L * 1024L;
            } catch (Exception e) {
                log.warn("快照大小解析失败: {}", name);
            }
        }

        // 状态处理
        String status = "";
        if (jsonObject.has("status") && !jsonObject.get("status").isJsonNull()) {
            status = jsonObject.get("status").getAsString();
            if (StrUtil.isNotEmpty(status)) {
                status = "available".equals(status) ? "Enabled" : "Disabled";
            }
        }

        // 时间处理
        Date vCreateDate = null;
        Date vUpdateDate = null;
        if (jsonObject.has("create_time") && !jsonObject.get("create_time").isJsonNull()) {
            try {
                vCreateDate = Date.from(Instant.parse(jsonObject.get("create_time").getAsString()));
            } catch (Exception e) {
                log.warn("创建时间解析失败: {}", name);
            }
        }
        if (jsonObject.has("update_time") && !jsonObject.get("update_time").isJsonNull()) {
            try {
                vUpdateDate = Date.from(Instant.parse(jsonObject.get("update_time").getAsString()));
            } catch (Exception e) {
                log.warn("更新时间解析失败: {}", name);
            }
        }

        return VolumeSnapshotData.builder()
                .createTime(new Date())
                .uuid(uuid)
                .name(name)
                .description(description)
                .hostUuid(instanceUuid)
                .hostName(instanceName)
                .volumeUuid(volumeUuid)
                .volumeName(volumeName)
                .type(type)
                .volumeType(volumeType)
                .latest("true")
                .platformName(platform.getPlatformName())
                .platformId(platform.getPlatformId())
                .typeName("istack")
                .size(size)
                .status(status)
                .vCreateDate(vCreateDate)
                .vUpdateDate(vUpdateDate)
                .format("qcow2")
                .isMemory(false)
                .build();


    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_SNAPSHOT.code();
    }
}
