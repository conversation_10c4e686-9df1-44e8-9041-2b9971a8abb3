package cn.coder.zj.module.collector.collect.basicdata.manager.winhong;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.util.*;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_WIN_HONG_VM_VIC;

@Slf4j
public class WinHongBasicVmNicDataImpl extends AbstractBasicData {

    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VmNicData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VM_VIC.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VmNicData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);
        Map<String, String> param = new HashMap<>();
        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_CLOUDS, param, headers).getAsJsonObject().get("data").getAsJsonArray();
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<VmNicData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
                    String uuid = getStringFromJson(jsonObject, "uuid", "");
                    JsonArray dombridgeInterfaces = getJsonArrayFromApi(
                            platform.getPlatformUrl() + "/api/compute/domains/"+uuid +"/domainInterfaceInfo", null, headers)
                            .getAsJsonObject().get("bridgeInterfaces").getAsJsonArray();

                    if (!dombridgeInterfaces.isEmpty()) {
                        for (int i = 0; i < dombridgeInterfaces.size(); i++) {
                            JsonObject dbi = dombridgeInterfaces.get(i).getAsJsonObject();
                            VmNicData hostNicCreateReqDto = new VmNicData();
                            hostNicCreateReqDto.setHostUuid(uuid);
                            hostNicCreateReqDto.setUuid(dbi.get("interfaceId").getAsString());
                            hostNicCreateReqDto.setIp(dbi.get("ip").isJsonNull() ? "" : dbi.get("ip").getAsString());
                            hostNicCreateReqDto.setIp6("");
                            hostNicCreateReqDto.setPlatformId(platform.getPlatformId());
                            hostNicCreateReqDto.setPlatformName(platform.getPlatformName());
                            hostNicCreateReqDto.setMac(dbi.get("mac").getAsString());
                            hostNicCreateReqDto.setDriver("virtio");
                            hostNicCreateReqDto.setInClassicNetwork((byte) 0);
                            if (!dbi.get("portGroupId").isJsonNull()) {
                                hostNicCreateReqDto.setNetworkUuid(dbi.get("portGroupId").getAsString());
                            }
                            dataList.add(hostNicCreateReqDto);
                        }
                    }
                } catch (Exception e) {
                    log.error("处理主机数据异常, hostMap: {}, error: {}", jsonElement, e.getStackTrace());
                }
            }
        }
        return dataList;
    }

    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);

            // 检查响应是否为有效的JSON对象
            if (element != null && element.isJsonObject()) {
                return element;
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_WIN_HONG_VM_VIC.code();
    }
}
