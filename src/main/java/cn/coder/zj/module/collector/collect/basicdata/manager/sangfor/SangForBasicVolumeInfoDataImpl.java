package cn.coder.zj.module.collector.collect.basicdata.manager.sangfor;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.UuidUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VOLUME_INFO;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_SXF_VOLUME_INFO;
import static cn.iocoder.zj.framework.common.enums.MetricsType.BASIC_TYPE_SXF;

@Slf4j
public class SangForBasicVolumeInfoDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VolumeInfoData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VOLUME_INFO.code())
                        .metricsType(BASIC_TYPE_SXF.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VolumeInfoData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie, "CSRFPreventionToken", token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_CLOUDS, null, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<VolumeInfoData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                List<VolumeInfoData> vmData = collectVmInfoData(platform, jsonElement, headers);
                dataList.addAll(vmData);
            }
        }
        return dataList;
    }

    private List<VolumeInfoData> collectVmInfoData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
        List<VolumeInfoData> list = new ArrayList<>();
        String vmid = getStringFromJson(jsonObject, "vmid", "");
        String platformUrl = platform.getPlatformUrl();

        JsonObject usageInfo = getUsageJsonFromApi(platformUrl + SangForApiConstant.GET_VM_DETAIL.replace("{vmid}", vmid), null, headers);
        JsonArray diskList = usageInfo.getAsJsonArray("diskStatus");
        if (diskList == null || diskList.isEmpty()) return list;

        JsonObject hardwareStatus = usageInfo.getAsJsonObject("hardwareStatus");
        String vmName = getStringFromJson(jsonObject, "name", "");
        String volumeId = getStringFromJson(jsonObject, "volume_id", "");
        String primaryStorageName = getStringFromJson(jsonObject, "storagename", "");
        Long timestamp = getLongFromJson(jsonObject, "create_time") * 1000;
        Date createDate = new Date(timestamp == 0L ? System.currentTimeMillis() : timestamp);

        for (int i = 0; i < diskList.size(); i++) {
            // 处理硬件状态信息
            String json = "";
            if (hardwareStatus != null && hardwareStatus.has("ide" + i)) {
                json = hardwareStatus.get("ide" + i).toString();
            }

            // 解析键值对
            String diskName = "";
            if (StrUtil.isNotEmpty(json)) {
                int firstCommaIndex = json.indexOf(",");
                if (firstCommaIndex > 0) {
                    diskName = json.substring(0, firstCommaIndex).split(":")[1];
                }
            }


            // 创建并设置卷信息
            VolumeInfoData volumeDTO = new VolumeInfoData();
            JsonObject diskStatus = diskList.get(i).getAsJsonObject();

            // 设置基本信息
            volumeDTO.setDescription("深信服云盘描述");
            volumeDTO.setName(vmName + "-" + diskName);
            volumeDTO.setFormat("vmtx");
            volumeDTO.setStatus("Ready");
            volumeDTO.setState("Enabled");
            volumeDTO.setType("Root");
            volumeDTO.setMediaType("rotate");
            volumeDTO.setIsMount(true);

            // 设置容量信息
            Long total = getLongFromJsonDouble(diskStatus, "total");
            Long free = getLongFromJsonDouble(diskStatus, "free");
            volumeDTO.setSize(total);
            volumeDTO.setActualFree(free);
            volumeDTO.setActualUse(free != null ? Convert.toLong(NumberUtil.sub(total, free)) : 0L);
            volumeDTO.setActualRatio(diskStatus.has("ratio") ? getStringFromJson(diskStatus, "ratio", "") : "0");
            volumeDTO.setActualSize(0L);

            // 设置UUID和平台信息
            volumeDTO.setUuid(UuidUtil.generateNameBasedUuid(volumeDTO.getName()));
            volumeDTO.setPlatformId(platform.getPlatformId().toString());
            volumeDTO.setPlatformName(platform.getPlatformName());
            volumeDTO.setVmInstanceName(vmName);

            // 设置存储信息
            if (volumeId != null) {
                if ("local".equals(volumeId)) {
                    String host = getStringFromJson(jsonObject, "host", "");
                    if (host != null) {
                        volumeDTO.setPrimaryStorageUuid(host + "_local");
                    }
                    volumeDTO.setPrimaryStorageType("local");
                } else {
                    volumeDTO.setPrimaryStorageUuid(volumeId);
                    JsonObject storageInfo = getJsonObjectFromApi(platformUrl + SangForApiConstant.GET_STORAGE_DETAIL.replace("{storageUuid}", volumeId), null, headers);
                    volumeDTO.setPrimaryStorageType(getStringFromJson(storageInfo, "lvtype", "").toLowerCase());
                }
            }

            JsonElement uuidElement = jsonObject.get("uuid");
            String uuid = (uuidElement != null && !uuidElement.isJsonNull()) ? uuidElement.getAsString()
                    : jsonObject.get("vmid").getAsString();
            // 设置时间和存储名称
            volumeDTO.setVCreateDate(createDate);
            volumeDTO.setVmInstanceUuid(uuid);
            volumeDTO.setVUpdateDate(createDate);
            volumeDTO.setPrimaryStorageName(primaryStorageName);
            volumeDTO.setDeleted(0);
            volumeDTO.setCreateTime(new Date());

            list.add(volumeDTO);
        }

        return list;
    }

    public static JsonObject getUsageJsonFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        JsonObject usageInfo = new JsonObject();
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);
            JsonObject data = element.getAsJsonObject().get("data").getAsJsonObject();
            if (data != null && data.isJsonObject()) {
                JsonArray diskStatus = data.get("disk_status").getAsJsonArray();
                JsonObject hardwareStatus = data.get("hardware_status").getAsJsonObject();
                usageInfo.add("diskStatus", diskStatus);
                usageInfo.add("hardwareStatus", hardwareStatus);
                return usageInfo;
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonObject();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonObject();
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_SXF_VOLUME_INFO.code();
    }
}
