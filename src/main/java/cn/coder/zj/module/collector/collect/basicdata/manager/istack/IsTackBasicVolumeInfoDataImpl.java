package cn.coder.zj.module.collector.collect.basicdata.manager.istack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromFsApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VOLUME_INFO;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_VOLUME_INFO;

@Slf4j
public class IsTackBasicVolumeInfoDataImpl extends AbstractBasicData {


    protected long startTime;


    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(() -> {
                List<VolumeInfoData> list = new ArrayList<>();
                managerData(platform, list);

                if (!list.isEmpty()) {
                    BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                            .metricsType("")
                            .metricsName(BASIC_VOLUME_INFO.code())
                            .build();

                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(build));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                }
            });
        }
    }

    private void managerData(Platform platform, List<VolumeInfoData> list) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());

        JsonObject hostInfo = getJsonObjectFromFsApi(platform.getPlatformUrl() + IStackApiConstant.GET_VM_DETAIL, param, null);
        JsonArray hostArray = hostInfo.getAsJsonArray("results");
        Map<String, JsonObject> hostMap = StreamSupport.stream(hostArray.spliterator(), false)
                .map(JsonElement::getAsJsonObject)
                .collect(Collectors.toMap(
                        host -> getStringFromJson(host, "id"),
                        host -> host,
                        (existing, replacement) -> existing
                ));

        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VOLUMES_DETAIL, param, null)) {
            String responseBody = response.body().string();
            JsonObject jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();
            JsonArray results = jsonObject.get("results").getAsJsonArray();
            results.forEach(info -> {
                VolumeInfoData volumeInfoData = new VolumeInfoData();
                JsonObject resultObj = info.getAsJsonObject();

                // 处理 size 字段
                long total = 0L;
                long actual = 0L;
                if (resultObj.has("size") && !resultObj.get("size").isJsonNull()) {
                    try {
                        total = resultObj.get("size").getAsLong() * 1024L * 1024L * 1024L;
                    } catch (Exception e) {
                        log.warn("云盘大小解析失败: {}", resultObj.get("name").getAsString());
                    }
                }

                String type = ""; // 设置默认值
                if (resultObj.has("type") && !resultObj.get("type").isJsonNull()) {
                    try {
                        type = resultObj.get("type").getAsString();
                    } catch (Exception e) {
                        log.warn("云盘类型解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                // 处理 instance_uuid 字段
                String instanceUuid = "";
                if (resultObj.has("instance_uuid") && !resultObj.get("instance_uuid").isJsonNull()) {
                    try {
                        instanceUuid = resultObj.get("instance_uuid").getAsString();
                    } catch (Exception e) {
                        log.warn("实例UUID解析失败: {}", resultObj.get("name").getAsString());
                    }
                } else {
                    log.warn("实例UUID为空: {}", resultObj.get("name").getAsString());
                }
                JsonObject jsonObject1 = vmUse(platform, instanceUuid,hostMap);
                if (!jsonObject1.isEmpty()){
                    JsonArray rows = jsonObject1.getAsJsonArray("results");
                    if (rows != null && rows.size() > 0) {
                        String samplingValue = rows.get(0).getAsJsonObject().get("sampling_value").getAsString();
                        if (type.equals("ROOT")) {
                            if (StrUtil.isEmpty(samplingValue)) {
                                samplingValue = "0";
                            }
                            String vmVolumeUtil = samplingValue;
                            volumeInfoData.setActualRatio(vmVolumeUtil);
                            volumeInfoData.setType("Root");
                            BigDecimal diskUtilRatio = new BigDecimal(vmVolumeUtil).divide(BigDecimal.valueOf(100));
                            long diskTotal = diskUtilRatio.multiply(BigDecimal.valueOf(total)).setScale(2, RoundingMode.HALF_UP).longValue();
                            actual = diskTotal;
                            volumeInfoData.setActualUse(diskTotal);
                            volumeInfoData.setActualFree(total - diskTotal);
                        } else {
                            volumeInfoData.setType("Data");
                            volumeInfoData.setActualRatio("0");
                            BigDecimal voUtilRatio = new BigDecimal(0).divide(BigDecimal.valueOf(100));
                            long diskTotal = voUtilRatio.multiply(BigDecimal.valueOf(total)).setScale(0, RoundingMode.HALF_UP).longValue();
                            actual = diskTotal;
                            volumeInfoData.setActualUse(diskTotal);
                            volumeInfoData.setActualFree(total - diskTotal);
                        }
                    }else {
                        if (type.equals("ROOT")) {
                            volumeInfoData.setType("Root");
                        } else {
                            volumeInfoData.setType("Data");
                        }
                        volumeInfoData.setActualUse(0L);
                        volumeInfoData.setActualFree(total);
                        volumeInfoData.setActualRatio("0");
                        actual = 0L;
                    }

                    String timeStr = resultObj.get("create_time").getAsString() != null ? resultObj.get("create_time").getAsString() : null;
                    Instant instant = null;
                    if (timeStr != null) {
                        instant = Instant.parse(timeStr);
                    }
                    if (instant != null) {
                        Date date1 = Date.from(instant);
                        volumeInfoData.setVCreateDate(date1);
                        volumeInfoData.setVUpdateDate(date1);
                    }
                }
                volumeInfoData.setDescription("IStack云盘描述");
                volumeInfoData.setName(resultObj.get("name").getAsString());
                // 处理 volume_type 字段
                String volumeType = "";
                if (resultObj.has("volume_type") && !resultObj.get("volume_type").isJsonNull()) {
                    try {
                        volumeType = resultObj.get("volume_type").getAsString();
                    } catch (Exception e) {
                        log.warn("云盘格式解析失败: {}", resultObj.get("name").getAsString());
                    }
                }

                volumeInfoData.setFormat("raw");
                volumeInfoData.setPrimaryStorageType(volumeType.toLowerCase());
                volumeInfoData.setSize(total);
                volumeInfoData.setActualSize(actual);

                volumeInfoData.setState("Enabled");
                // 处理 id 字段
                String uuid = "";
                if (resultObj.has("id") && !resultObj.get("id").isJsonNull()) {
                    try {
                        uuid = resultObj.get("id").getAsString();
                    } catch (Exception e) {
                        log.warn("云盘UUID解析失败: {}", resultObj.get("name").getAsString());
                    }
                }

                volumeInfoData.setUuid(uuid);
                volumeInfoData.setStatus("Ready");
                volumeInfoData.setPlatformId(Convert.toStr(platform.getPlatformId()));
                volumeInfoData.setPlatformName(platform.getPlatformName());
                // 处理 instance_uuid
                String vmInstanceUuid = "";
                if (resultObj.has("instance_uuid") && !resultObj.get("instance_uuid").isJsonNull()) {
                    try {
                        vmInstanceUuid = resultObj.get("instance_uuid").getAsString();
                    } catch (Exception e) {
                        log.warn("实例UUID解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                volumeInfoData.setVmInstanceUuid(vmInstanceUuid);

                // 处理 instance_name
                String vmInstanceName = "";
                if (resultObj.has("instance_name") && !resultObj.get("instance_name").isJsonNull()) {
                    try {
                        vmInstanceName = resultObj.get("instance_name").getAsString();
                    } catch (Exception e) {
                        log.warn("实例名称解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                volumeInfoData.setVmInstanceName(vmInstanceName);


                // 处理 storageid
                String storageUuid = "";
                if (resultObj.has("storageid") && !resultObj.get("storageid").isJsonNull()) {
                    try {
                        storageUuid = resultObj.get("storageid").getAsString();
                    } catch (Exception e) {
                        log.warn("存储UUID解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                volumeInfoData.setPrimaryStorageUuid(storageUuid);

                // 处理 storage
                String storageName = "";
                if (resultObj.has("storage") && !resultObj.get("storage").isJsonNull()) {
                    try {
                        storageName = resultObj.get("storage").getAsString();
                    } catch (Exception e) {
                        log.warn("存储名称解析失败: {}", resultObj.get("name").getAsString());
                    }
                }
                volumeInfoData.setPrimaryStorageName(storageName);

                volumeInfoData.setDeleted(0);
                // 处理 harddisk_status
                String hardDiskStatus = "";
                if (resultObj.has("harddisk_status") && !resultObj.get("harddisk_status").isJsonNull()) {
                    try {
                        hardDiskStatus = resultObj.get("harddisk_status").getAsString();
                    } catch (Exception e) {
                        log.warn("硬盘状态解析失败: {}", resultObj.get("name").getAsString());
                    }
                }

                if (hardDiskStatus != null) {
                    volumeInfoData.setIsMount(hardDiskStatus.equals("ATTACHED"));
                } else {
                    volumeInfoData.setIsMount(null);
                }
                volumeInfoData.setMediaType("ssd");
                volumeInfoData.setCreateTime(new Date());
                list.add(volumeInfoData);
            });
        } catch (IOException e) {
            log.error("IsTack平台 云盘 {} 数据采集异常: {}", platform.getPlatformName(), e.getMessage());
        }
    }

    private JsonObject vmUse(Platform platform, String instanceUuid,Map<String, JsonObject> hostMap) {
        JsonObject jsonObject = new JsonObject();

        if (instanceUuid == null || instanceUuid.isEmpty()) {
            return jsonObject;
        }

        // 默认挂载路径
        String index = "/";
        JsonObject hostInfo = hostMap.get(instanceUuid);
        if (hostInfo != null) {
            String osTypeName = getStringFromJson(hostInfo, "ostypename");
            String platformType = determineApplicationPlatform(osTypeName);
            if ("Windows".equalsIgnoreCase(platformType)) {
                index = "C:";
            }
        }

        long timestamp = DateUtil.currentSeconds() * 1000;
        Map<String, String> param = Map.of(
                "os_id", platform.getPassword(),
                "ct_user_id", platform.getUsername(),
                "uuid", instanceUuid,
                "item_name", "pused_disk_space_on",
                "fs_dir", index,
                "from", String.valueOf(timestamp),
                "to", String.valueOf(timestamp)
        );

        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_USAGE, param, null)) {
            String responseBody = response.body().string();
            jsonObject = GSON.fromJson(responseBody, JsonElement.class).getAsJsonObject();
        } catch (IOException e) {
            log.error("IsTack平台 {} 数据采集异常: {}", platform.getPlatformName(), e.getMessage());
        }
        return jsonObject;
    }

    private String determineApplicationPlatform(String osType) {
        if (osType.contains("linux") || osType.contains("centos")) {
            return "Linux";
        } else if (osType.contains("windows")) {
            return "Windows";
        }
        return "";
    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_VOLUME_INFO.code();
    }
}
