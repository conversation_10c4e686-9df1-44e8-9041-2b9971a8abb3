package cn.coder.zj.module.collector.collect.scanprot.snmp;
import cn.iocoder.zj.framework.common.dal.manager.ScanIPData;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.coder.zj.module.collector.util.NetworkUtil.*;

@Slf4j
public class ScanSnmpData{

    public static List<ScanIPData> checkSnmpEndpoints(List<ScanIPData> endpoints) {
        if (endpoints == null || endpoints.isEmpty()) {
            return Collections.emptyList();
        }

        List<List<ScanIPData>> batches = splitIntoBatches(endpoints, 20);
        int totalSize = endpoints.size();
        Long ipRangeId = endpoints.get(0).getIpRangeId();

        AtomicInteger processedCount = new AtomicInteger(0);
        for (List<ScanIPData> batch : batches) {
            batch.parallelStream().forEach(endpoint -> {
                endpoint.setSnmpStatus(checkEndpointSnmp(endpoint) ? 1 : 2);
            });
            // 更新进度
            processedCount.addAndGet(batch.size());

            sendMessage(ipRangeId, totalSize, processedCount.get(),"snmp");
        }

        return endpoints;
    }

    private static boolean checkEndpointSnmp(ScanIPData endpoint) {
        boolean isReachable = isSnmpReachable(endpoint.getIpAddress(),endpoint.getSnmpPort(),endpoint.getSnmpCommunity(),convertVersion(endpoint.getSnmpVersion()));
        endpoint.setSnmpStatus(isReachable ? 1 : 2);
        return isReachable;
    }
}