package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.*;
import cn.iocoder.zj.framework.common.dal.manager.aggregation.SecGroupAggData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SEC_GROUP;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_SEC_GROUP;
@Slf4j
public class FsOneBasicSecGroupDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<SecGroupAggData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_SEC_GROUP.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<SecGroupAggData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        List<SecGroupAggData> aggData = Lists.newArrayList();
        //组装对象
        SecGroupAggData groupInfos = new SecGroupAggData();
        List<SecGroupData> secGroups = Lists.newArrayList();
        List<SecGroupRuleData> secGroupRules = Lists.newArrayList();
        List<HostSecGroupData> hostSecGroups = Lists.newArrayList();

        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_GROUP.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        String hostUrl = platform.getPlatformUrl() + FusionOneApiConstant.GET_CLOUD_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonObject hostdata = FsApiCacheService.getJsonObject(hostUrl, null, header);
        JsonArray hostArray = hostdata.getAsJsonArray("vms");
        Map<String, String> urnUuidMap = StreamSupport.stream(hostArray.spliterator(), false)
                .map(JsonElement::getAsJsonObject)
                .collect(Collectors.toMap(
                        host -> getStringFromJson(host, "urn"),
                        host -> getStringFromJson(host, "uuid"),
                        (existing, replacement) -> existing
                ));

        JsonArray vmArray = vmdata.getAsJsonArray("securityGroups");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        for (JsonElement jsonElement : vmArray) {
            JsonObject group = jsonElement.getAsJsonObject();
            secGroups.add(SecGroupData.builder()
                    .platformId(platform.getPlatformId())
                    .platformName(platform.getPlatformName())
                    .name(getStringFromJson(group,"sgName"))
                    .uuid(getStringFromJson(group,"sgId"))
                    .status("Enabled")
                    .build());
            String roleUrl = url + "/" + getStringFromJson(group,"sgId") + "/action/rules";
            JsonObject roledata = FsApiCacheService.getJsonObject(roleUrl, null, header);
            JsonArray roleArray = roledata.getAsJsonArray("securityGroupRules");
            if(roleArray.size() > 0){
                for (JsonElement element : roleArray) {
                    JsonObject role = element.getAsJsonObject();
                    secGroupRules.add(SecGroupRuleData.builder()
                            .uuid(getStringFromJson(role, "rulesId"))
                            .secgroupUuid(getStringFromJson(group, "sgId"))
                            .action(getStringFromJson(role, "ipProtocol"))
                            .protocol(getStringFromJson(role, "ipProtocol"))
                            .direction(getStringFromJson(role, "direction"))
                            .platformId(platform.getPlatformId())
                            .platformName(platform.getPlatformName())
                            .ports(getStringFromJson(role, "toPort").equals("-1") ? "任意端口" : getStringFromJson(role, "toPort"))
                            .cidr(getStringFromJson(role, "ipRanges")).build());
                }
            }

            JsonArray vmList = group.getAsJsonArray("vmList");
            if(vmList.size() > 0){
                for (JsonElement element : vmList) {
                    JsonObject vm = element.getAsJsonObject();
                    String vmUrn = urnUuidMap.get(getStringFromJson(vm, "vmUrn"));
                    hostSecGroups.add(HostSecGroupData.builder().hostUuid(vmUrn)
                            .secgroupUuid(getStringFromJson(group,"sgId"))
                            .platformId(platform.getPlatformId())
                            .platformName(platform.getPlatformName()).build());
                }
            }

            groupInfos.setSecGroups(secGroups);
            groupInfos.setSecGroupRules(secGroupRules);
            groupInfos.setHostSecGroups(hostSecGroups);
            aggData.add(groupInfos);
        }
        return aggData;
    }

    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_SEC_GROUP.code();
    }
}
