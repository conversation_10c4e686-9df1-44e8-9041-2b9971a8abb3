/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.coder.zj.module.collector.remoting.netty.handler.processor;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import io.netty.channel.ChannelHandlerContext;

/**
 * 客户端消息处理器接口
 * 
 * <AUTHOR>
 */
public interface ClientMessageProcessor {

    /**
     * 处理消息
     * 
     * @param ctx 网络通道上下文
     * @param message 消息对象
     * @return 响应消息，如果不需要响应则返回null
     */
    ClusterMsg.Message process(ChannelHandlerContext ctx, ClusterMsg.Message message);

    /**
     * 获取处理器支持的消息类型
     * 
     * @return 消息类型
     */
    ClusterMsg.MessageType getMessageType();

} 