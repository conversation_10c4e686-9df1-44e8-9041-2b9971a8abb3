package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.InventoryNavigator;
import com.vmware.vim25.mo.ManagedEntity;
import com.vmware.vim25.mo.ServiceInstance;
import com.vmware.vim25.mo.VirtualMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.rmi.RemoteException;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_IMAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_IMAGE;
@Slf4j
public class VmwareBasicImageDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                try {
                    List<ImageData> imageDataList = handleImageData(platform);
                    if (!CollUtil.isEmpty(imageDataList)) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(imageDataList)
                                .metricsName(BASIC_IMAGE.code())
                                .build();

                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
                }catch (Exception e){
                    log.error("平台 [{}] 镜像数据收集失败", platform.getPlatformName(), e);
                }
            });
        }

    }

    private List<ImageData> handleImageData(Platform platform) throws RemoteException {
        List<ImageData> imageList = new ArrayList<>();
        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();
        ManagedEntity[] vms = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("VirtualMachine");
        if (vms == null) {
            return imageList;
        }
        for (ManagedEntity entity : vms) {
            VirtualMachine vm = (VirtualMachine) entity;
            try {
                if (!vm.getConfig().isTemplate()) {
                    continue;
                }
                imageList.add(buildImageInfo(platform,vm));
            } catch (Exception e) {
                log.error("平台 [{}] 处理虚拟机模板 {} 失败: {}",platform.getPlatformName(),vm.getName(), e.getMessage());
            }
        }
        return imageList;
    }

    private ImageData buildImageInfo(Platform platform,VirtualMachine vm) throws RemoteException {
        String vmUUid = vm.getMOR().getVal() + "-" + vm.getConfig().getUuid();
        String vmName = vm.getName();
        VirtualMachineConfigInfo config = vm.getConfig();
        GuestInfo guestInfo = vm.getGuest();

        Long size = 0L;
        VirtualMachineStorageInfo storageInfo = vm.getStorage();
        if (storageInfo != null && storageInfo.getPerDatastoreUsage() != null) {
            for (VirtualMachineUsageOnDatastore usage : storageInfo.getPerDatastoreUsage()) {
                size = usage.getCommitted();
            }
        }

        String cpuArch = "";
        if (guestInfo != null) {
            String guestId = guestInfo.getGuestId();
            cpuArch = determineCpuArch(guestId);
        }
        VirtualHardware hardware = config.getHardware();
        long minMemory = hardware.getMemoryMB() * 1024L;
        long totalSize = 0;
        for (VirtualDevice device : hardware.getDevice()) {
            if (device instanceof VirtualDisk disk) {
                totalSize += disk.getCapacityInKB();
            }
        }
        long minDisk = totalSize * 1024L;

        String changeVersion = config.getChangeVersion();
        Date createTime ;
        if (StrUtil.isNotEmpty(changeVersion)) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                createTime = sdf.parse(changeVersion.substring(0, 23) + "Z");
            } catch (Exception e) {
                createTime = new Date();
            }
        } else {
            createTime = new Date();
        }
        String applicationPlatform = Objects.toString(config.getGuestFullName(), "").toLowerCase().contains("windows") ? "Windows" : "Linux";
        return ImageData.builder()
                .name(vm.getName())
                .uuid(config.getUuid())
                .size(size)
                .status("Enabled")
                .format("vmdk")
                .osType(config.getGuestFullName())
                .applicationPlatform(applicationPlatform)
                .imageType("RootVolumeTemplate")
                .sharingScope("不共享")
                .osLanguage("")
                .diskDriver("")
                .networkDriver("")
                .bootMode("")
                .remoteProtocol("")
                .hostUuid(vmUUid)
                .hostName(vmName)
                .cpuArch(cpuArch)
                .minMemory(BigDecimal.valueOf(minMemory))
                .minDisk(BigDecimal.valueOf(minDisk))
                .platformId(platform.getPlatformId())
                .platformName(platform.getPlatformName())
                .vCreateDate(createTime)
                .vUpdateDate(createTime)
                .deleted(0)
                .build();
    }

    private static String determineCpuArch(String guestId) {
        if (guestId == null) {
            return "x86_64";
        }
        if (guestId.contains("64")) {
            return "x86_64";
        } else if (guestId.contains("arm") || guestId.contains("ARM")) {
            return "aarch64";
        }else if (guestId.contains("32")) {
            return "x86_32";
        }else if (guestId.contains("mips")) {
            return "mips64el";
        }
        return "x86_64";
    }

    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_IMAGE.code();
    }
}
