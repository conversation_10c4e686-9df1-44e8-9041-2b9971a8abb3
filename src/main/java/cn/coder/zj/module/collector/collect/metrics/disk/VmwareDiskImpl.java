package cn.coder.zj.module.collector.collect.metrics.disk;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.manager.PerMonitorDO;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.vmware.RealtimePerfMonitor;
import cn.coder.zj.module.collector.service.vmware.VmwareMetricsUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.vmware.vim25.DatastoreSummary;
import com.vmware.vim25.HostHardwareSummary;
import com.vmware.vim25.mo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static cn.coder.zj.module.collector.enums.DiskType.PROTOCOL_VMWARE_DISK;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.service.vmware.HostComputerResourceSummary.getHostList;
import static cn.coder.zj.module.collector.service.vmware.VmComputerResourceSummary.getVmList;

@Slf4j
public class VmwareDiskImpl extends AbstractMetrics {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {
        // 预检查逻辑，暂时不需要实现
    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                        List<MetricData> metricDataList = new ArrayList<>();

                        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();

                        if (serviceInstance == null) {
                            log.error("平台 {} serviceInstance为空", platform.getPlatformName());
                            return;
                        }

                        // 宿主机指标
                        List<MetricData> hostMetrics = hostDiskMetric(serviceInstance, platform);
                        if (hostMetrics != null && !hostMetrics.isEmpty()) {
                            metricDataList.addAll(hostMetrics);
                        }

                        // 虚拟机指标
                        List<MetricData> vmMetrics = vmDiskMetric(serviceInstance, platform);
                        if (vmMetrics != null && !vmMetrics.isEmpty()) {
                            metricDataList.addAll(vmMetrics);
                        }

                        // 存储的磁盘使用率
                        List<MetricData> diskUsageMetrics = diskUsageMetric(serviceInstance, platform);
                        if (diskUsageMetrics != null && !diskUsageMetrics.isEmpty()) {
                            metricDataList.addAll(diskUsageMetrics);
                        }

                        message.setData(new Gson().toJson(metricDataList));
                        message.setTime(System.currentTimeMillis());
                        message.setType(ClusterMsg.MessageType.DISK_TASK);
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
            );
        }
    }

    private List<MetricData> diskUsageMetric(ServiceInstance serviceInstance, Platform platform) {
        List<MetricData> metricDataList = new ArrayList<>();
        try {
            List<Datastore> dataStoreList = new ArrayList<>();
            Datastore datastore;

            ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                    .searchManagedEntities("Datastore");
            if (managedEntities != null) {
                for (ManagedEntity managedEntity : managedEntities) {
                    datastore = (Datastore) managedEntity;
                    dataStoreList.add(datastore);
                }
            }
            if (CollUtil.isNotEmpty(dataStoreList)) {
                for (Datastore datastore1 : dataStoreList) {
                    DatastoreSummary datastoreSummary = datastore1.getSummary();
                    String name = datastore1.getName();
                    String uuid = datastoreSummary.getUrl();
                    String clusterId = datastore1.getParent().getMOR().getVal();
                    String storageUUid = (name + uuid + clusterId).replaceAll("[\\s\\\\/:*?\"<>|]", "");
                    Long capacity = datastoreSummary.getCapacity();
                    Long freeSpace = datastoreSummary.getFreeSpace();
                    BigDecimal total = NumberUtil.sub(capacity, freeSpace);
                    BigDecimal capacityUtilization = NumberUtil.round(NumberUtil.mul(NumberUtil.div(total, capacity), 100, 2), 2);
                    // 收集磁盘使用率指标
                    MetricData diskUsedMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), storageUUid, name, DISK_USED_TASK.code());
                    diskUsedMetric.setValues(Collections.singletonList(capacityUtilization.doubleValue()));
                    diskUsedMetric.setTimestamps(Collections.singletonList(System.currentTimeMillis() / 1000));
                    metricDataList.add(diskUsedMetric);
                }
            }
            return metricDataList;
        } catch (Exception e) {
            log.error("平台 {} 收集磁盘使用率指标异常", platform.getPlatformName(), e);
        }
        return metricDataList;
    }

    private List<MetricData> hostDiskMetric(ServiceInstance serviceInstance, Platform platform) {
        List<MetricData> metricDataList = new ArrayList<>();
        try {
            // 宿主机列表
            List<HostSystem> hostSystems = getHostList(serviceInstance);

            if (hostSystems == null || hostSystems.isEmpty()) {
                log.warn("平台 {} 未找到宿主机系统", platform.getPlatformName());
                return metricDataList;
            }

            // 循环宿主机列表
            for (HostSystem hostSystem : hostSystems) {
                String clusterUuid = hostSystem.getParent().getMOR().getVal();
                HostHardwareSummary hostHardwareSummary = hostSystem.getSummary().getHardware();
                String vms = hostSystem.getMOR().getVal();
                String uuid = clusterUuid + hostHardwareSummary.getUuid() + vms;
                String hostName = hostSystem.getName();

                ManagedEntity managedEntity = new InventoryNavigator(serviceInstance.getRootFolder())
                        .searchManagedEntity("HostSystem", hostName);

                if (managedEntity == null) {
                    log.warn("找不到宿主机: {}", hostName);
                    continue;
                }

                // 收集磁盘写入指标
                MetricData writeMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, hostName, DISK_WRITE_TASK.code());
                VmwareMetricsUtil.collectMetric(
                        serviceInstance,
                        platform,
                        writeMetric,
                        managedEntity,
                        hostName,
                        "disk.write.average",
                        VmwareMetricsUtil::diskValueConverter,
                        "host"
                );
                if (!writeMetric.getValues().isEmpty()) {
                    metricDataList.add(writeMetric);
                }

                // 收集磁盘读取指标
                MetricData readMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, hostName, DISK_READ_TASK.code());
                VmwareMetricsUtil.collectMetric(
                        serviceInstance,
                        platform,
                        readMetric,
                        managedEntity,
                        hostName,
                        "disk.read.average",
                        VmwareMetricsUtil::diskValueConverter,
                        "host"
                );
                if (!readMetric.getValues().isEmpty()) {
                    metricDataList.add(readMetric);
                }

                // 收集磁盘写入IOPS指标
                MetricData writeIopsMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, hostName, DISK_WRITE_OPS.code());
                collectDiskIopsMetric(serviceInstance, platform, writeIopsMetric, managedEntity, hostName, "disk.numberWrite.summation");
                if (!writeIopsMetric.getValues().isEmpty()) {
                    metricDataList.add(writeIopsMetric);
                }

                // 收集磁盘读取IOPS指标
                MetricData readIopsMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), uuid, hostName, DISK_READ_OPS.code());
                collectDiskIopsMetric(serviceInstance, platform, readIopsMetric, managedEntity, hostName, "disk.numberRead.summation");
                if (!readIopsMetric.getValues().isEmpty()) {
                    metricDataList.add(readIopsMetric);
                }

            }

            return metricDataList;
        } catch (Exception e) {
            log.error("收集宿主机磁盘性能数据失败: {}", e.getMessage(), e);
        }

        return metricDataList;
    }

    // IOPS指标收集方法 - 由于处理逻辑特殊，暂不抽取到工具类
    private void collectDiskIopsMetric(ServiceInstance serviceInstance, Platform platform,
                                       MetricData metricData, ManagedEntity managedEntity,
                                       String entityName, String counterName) {
        try {
            List<PerMonitorDO> metricList = RealtimePerfMonitor.getPerEntityMetricBasesByName(
                    entityName, serviceInstance, managedEntity, counterName, platform);

            if (metricList.isEmpty()) {
                return;
            }

            //取metricList数据中最后一个数据的时间戳和value
            // metricList value 为负数去除
            metricList.removeIf(metric -> metric.getValue() < 0);
            PerMonitorDO lastMetric = metricList.get(metricList.size() - 1);
            if (lastMetric.getInstance().isEmpty()) {
                lastMetric.setInstance("0");
            }

            // 直接使用值作为IOPS
            double iops = lastMetric.getValue();
            Long timestamp = lastMetric.getDateTime().getTime() / 1000;
            metricData.getTimestamps().add(timestamp);
            metricData.getValues().add(iops);

        } catch (Exception e) {
            log.error("收集磁盘IOPS指标失败: {}", e.getMessage(), e);
        }
    }

    private List<MetricData> vmDiskMetric(ServiceInstance serviceInstance, Platform platform) {
        List<MetricData> metricDataList = new ArrayList<>();
        try {
            // 获取所有虚拟机
            List<VirtualMachine> virtualMachines = getVmList(serviceInstance);
            if (virtualMachines == null || virtualMachines.isEmpty()) {
                log.warn("平台 {} 未找到虚拟机", platform.getPlatformName());
                return metricDataList;
            }

            for (VirtualMachine vm : virtualMachines) {
                // 跳过模板和未开机的虚拟机
                if (vm.getConfig() != null && vm.getConfig().isTemplate()) {
                    continue;
                }

                if (vm.getRuntime().getPowerState() != com.vmware.vim25.VirtualMachinePowerState.poweredOn) {
                    continue;
                }

                String vmName = vm.getName();
                String vmId = vm.getMOR().getVal();
                String uuid = vm.getConfig().getUuid();
                String resourceId = vmId + "-" + uuid;

                ManagedEntity managedEntity = new InventoryNavigator(serviceInstance.getRootFolder())
                        .searchManagedEntity("VirtualMachine", vmName);

                if (managedEntity == null) {
                    log.warn("找不到虚拟机: {}", vmName);
                    continue;
                }

                // 收集虚拟机磁盘写入指标
                MetricData writeMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), resourceId, vmName, DISK_WRITE_TASK.code());
                VmwareMetricsUtil.collectMetric(
                        serviceInstance,
                        platform,
                        writeMetric,
                        managedEntity,
                        vmName,
                        "virtualDisk.write.average",
                        VmwareMetricsUtil::diskValueConverter,
                        "vm"
                );
                if (!writeMetric.getValues().isEmpty()) {
                    metricDataList.add(writeMetric);
                }

                // 收集虚拟机磁盘读取指标
                MetricData readMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), resourceId, vmName, DISK_READ_TASK.code());
                VmwareMetricsUtil.collectMetric(
                        serviceInstance,
                        platform,
                        readMetric,
                        managedEntity,
                        vmName,
                        "virtualDisk.read.average",
                        VmwareMetricsUtil::diskValueConverter,
                        "vm"
                );
                if (!readMetric.getValues().isEmpty()) {
                    metricDataList.add(readMetric);
                }

                // 收集虚拟机磁盘写入IOPS指标
                MetricData writeIopsMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), resourceId, vmName, DISK_WRITE_OPS.code());
                collectDiskIopsMetric(serviceInstance, platform, writeIopsMetric, managedEntity, vmName, "virtualDisk.numberWriteAveraged.average");
                if (!writeIopsMetric.getValues().isEmpty()) {
                    metricDataList.add(writeIopsMetric);
                }

                // 收集虚拟机磁盘读取IOPS指标
                MetricData readIopsMetric = VmwareMetricsUtil.createMetricData(platform.getPlatformId(), resourceId, vmName, DISK_READ_OPS.code());
                collectDiskIopsMetric(serviceInstance, platform, readIopsMetric, managedEntity, vmName, "virtualDisk.numberReadAveraged.average");
                if (!readIopsMetric.getValues().isEmpty()) {
                    metricDataList.add(readIopsMetric);
                }
            }
        } catch (Exception e) {
            log.error("收集虚拟机磁盘性能数据失败: {}", e.getMessage(), e);
        }

        return metricDataList;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_VMWARE_DISK.code();
    }
}
