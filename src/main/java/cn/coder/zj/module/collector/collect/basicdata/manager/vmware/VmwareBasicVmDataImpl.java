package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.manager.PerMonitorDO;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.vmware.RealtimePerfMonitor;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.coder.zj.module.collector.service.vmware.VmComputerResourceSummary.getVmDiskInfoWithActualSize;
import static cn.coder.zj.module.collector.util.CommonUtil.getIntFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_VM;

@Slf4j
public class VmwareBasicVmDataImpl extends AbstractBasicData {

    protected Long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute( () -> {
                try {
                    // 收集虚拟机数据
                    List<VmData> VmDataList = handleVirtualMachine(platform);
                    if (!CollUtil.isEmpty(VmDataList)) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(VmDataList)
                                .metricsName(BASIC_VM.code())
                                .build();

                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
                } catch (Exception e) {
                    log.error("平台 [{}] 虚拟机数据收集失败", platform.getPlatformName(), e);
                    throw new RuntimeException(e);
                }
            });
        }

    }

    private List<VmData> handleVirtualMachine (Platform platform) throws Exception {
        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();
        // 创建服务实例并获取主机列表
        List<VirtualMachine> virtualMachineList = new ArrayList<>();
        VirtualMachine virtualMachine;
        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("VirtualMachine");
        if (managedEntities != null) {
            for (ManagedEntity managedEntity : managedEntities) {
                virtualMachine = (VirtualMachine) managedEntity;
                virtualMachineList.add(virtualMachine);
            }
        }

        if (virtualMachineList.isEmpty()) {
            log.warn("平台 [{}] 未找到虚拟机信息", platform.getPlatformName());
            return null;
        }

        // 处理虚拟机数据
        return processVirtualMachineInfo(serviceInstance, virtualMachineList, platform);
    }

    private List<VmData> processVirtualMachineInfo(ServiceInstance serviceInstance, List<VirtualMachine> virtualMachineList, Platform platform) throws Exception {
        List<VmData> vmDataList = new ArrayList<>();
        VmData vmData;
        for (VirtualMachine vm : virtualMachineList) {
            vmData  = extractVmInfos(serviceInstance, vm, platform);
            vmDataList.add(vmData);
        }
        return vmDataList;
    }

    private VmData extractVmInfos(ServiceInstance serviceInstance, VirtualMachine virtualMachine, Platform platform) throws Exception {
        VirtualMachineSummary virtualMachineSummary = virtualMachine.getSummary();
        String uuid = virtualMachineSummary.getConfig().getUuid();
        String name = virtualMachine.getName();
        String state = getState(virtualMachineSummary.getRuntime().getPowerState().name());
        String ip = virtualMachine.getGuest().getIpAddress();
        String clusterId = virtualMachine.getParent().getMOR().getVal();
        String clusterName = virtualMachine.getParent().getName();
        String guestOsType = virtualMachineSummary.getConfig().getGuestFullName();
        BigDecimal memTotal = NumberUtil.mul(Convert.toBigDecimal(virtualMachineSummary.getConfig().getMemorySizeMB()), 1024, 1024);
        BigDecimal memUsage = NumberUtil.mul(Convert.toBigDecimal(virtualMachineSummary.getQuickStats().getGuestMemoryUsage()), 1024, 1024);
        // 判断menUsage 是否为0
        BigDecimal memoryUsed = BigDecimal.ZERO;
        if (memUsage.compareTo(BigDecimal.ZERO) > 0) {
            memoryUsed = NumberUtil.round(NumberUtil.mul(NumberUtil.div(memUsage, memTotal, 2), 100),2);
        }
        long storageUsage = virtualMachineSummary.getStorage().getCommitted();

        // 获取虚拟机创建时间
        Date createDate = getVmCreateTime(serviceInstance, virtualMachine);
        
        List<HostSystem> systems = getHostList(serviceInstance);
        String hardwareUuid = "";
        String hardwareName = "";
        for (HostSystem system : systems) {
            if (system.getMOR().getVal().equals(virtualMachine.getRuntime().getHost().getVal())) {
                hardwareUuid = system.getParent().getMOR().getVal() + system.getSummary().getHardware().getUuid() + system.getMOR().getVal();
                hardwareName = system.getName();
                break;
            }
        }

        ManagedEntity managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntity("VirtualMachine", virtualMachine.getName());
        List<PerMonitorDO> cpuMetrics = RealtimePerfMonitor.getPerEntityMetricBasesByName(name, serviceInstance, managedEntities, "cpu.usage.average", platform);

        double cpuUsage = NumberUtil.div(cpuMetrics.stream().mapToLong(PerMonitorDO::getValue).average().orElse(0),100,2);
        cpuUsage = cpuUsage < 0 ? 0.00 : cpuUsage;
        VirtualDevice[] devices = virtualMachine.getConfig().getHardware().getDevice();
        String firmware = virtualMachine.getConfig().getFirmware();

        long storageTotal = 0;
        long virtualTotal = 0;
        boolean firstDiskFound = false;
        List<VmDiskFileInfo> diskFileInfos = getVmDiskInfoWithActualSize(virtualMachine);

        if (devices != null) {
            for (VirtualDevice virtualDevice : devices) {
                if (virtualDevice instanceof VirtualDisk disk) {
                    if (!firstDiskFound) {
                        firstDiskFound = true; // 第一个 VirtualDisk 找到了，设置标志为 true
                        storageTotal = disk.getCapacityInBytes();
                        continue; // 跳过第一个 VirtualDisk
                    }
                    virtualTotal += disk.getCapacityInBytes();
                }
            }
        }

        BigDecimal diskFreeBytes = new BigDecimal(0);
        BigDecimal actualSize = new BigDecimal(0);

        if (!diskFileInfos.isEmpty()) {
            diskFreeBytes = Convert.toBigDecimal(diskFileInfos.get(0).getFileSize());
            for (int i = 1; i < diskFileInfos.size(); i++) { // 从索引 1 开始，跳过第一个元素
                VmDiskFileInfo diskFileInfo = diskFileInfos.get(i);
                // 如果
                if (firmware.equals("efi") && diskFileInfo.getFileSize() > 1048576) {
                    BigDecimal dsk = Convert.toBigDecimal(diskFileInfo.getFileSize());
                    actualSize = actualSize.add(dsk);
                } else if (!firmware.equals("efi")) {
                    BigDecimal dsk = Convert.toBigDecimal(diskFileInfo.getFileSize());
                    actualSize = actualSize.add(dsk);
                }
            }
        }

        if(name.equals("bglstc13")){
            System.out.println();
        }
        BigDecimal diskUsage = calculateDiskUsage(virtualMachine, devices);

        String mac = getMacAddresses(virtualMachine);
        if (mac == null || mac.isEmpty()) {
            mac = "-";
        } else {
            if (mac.contains(",")) {
                // 如果包含逗号，取第一个MAC地址
                mac = mac.split(",")[0].trim();
            }
        }
        String typeName = "vmware";
        String vms = virtualMachine.getMOR().getVal();

        GuestInfo guestInfo = virtualMachine.getGuest();
        String toolsStatus = guestInfo.getToolsStatus().toString();
        String tool = switch (toolsStatus) {
            case "toolsOld", "toolsOk" -> "true";
            default -> "false";
        };

        long networkRx = getNetworkUsage(serviceInstance, managedEntities, name, "net.bytesRx.average", platform);
        long networkTx = getNetworkUsage(serviceInstance, managedEntities, name, "net.bytesTx.average", platform);
        long netPacRx = getNetworkUsage(serviceInstance, managedEntities, name, "net.packetsRx.summation", platform);
        long netPacTx = getNetworkUsage(serviceInstance, managedEntities, name, "net.packetsTx.summation", platform);

        String status = powerStateConvert(state);
        String guideMode;
        String autoInitType = "";
        String imageUuid = "";
        String imageName = "";

        ManagedObjectReference hostMor = virtualMachine.getRuntime().getHost();
        HostSystem host = new HostSystem(serviceInstance.getServerConnection(), hostMor);
        ManagedEntity parent = host.getParent();
        if (parent instanceof ClusterComputeResource cluster) {
            ClusterConfigInfoEx configEx = (ClusterConfigInfoEx) cluster.getConfigurationEx();
            ClusterDasConfigInfo dasConfig = configEx.getDasConfig();
            if(dasConfig.getEnabled()){
                autoInitType = "None";
            }else{
                autoInitType = "NeverStop";
            }
        }

        if(StrUtil.isNotEmpty(firmware) &&  firmware.equals("bios")){
            guideMode = "Legacy";
        }else if(StrUtil.isNotEmpty(firmware) &&  firmware.equals("efi")){
            guideMode = "UEFI";
        }else {
            guideMode = "Other";
        }


        if (virtualMachine.getConfig().isTemplate()) {
            imageUuid = virtualMachine.getConfig().getUuid();
            imageName = virtualMachine.getName();
        }

        return new VmData()
                .setUuid(vms + "-" + uuid)
                .setName(name)
                .setState(state)
                .setIp(ip)
                .setVipIp("")
                .setToolsInstalled(tool)
                .setToolsRunStatus(tool.equals("true")?"run":"stop")
                .setClusterUuid(clusterId)
                .setClusterName(clusterName)
                .setHardwareUuid(hardwareUuid)
                .setHardwareName(hardwareName)
                .setArchitecture("x86_64")
                .setGuestOsType(guestOsType)
                .setType("UserVm")
                .setMemoryUsed(memoryUsed)
                .setMemorySize(Convert.toLong(memTotal))
                .setCpuUsed(Convert.toBigDecimal(cpuUsage))
                .setDiskUsed(diskUsage)
                .setCpuNum(virtualMachineSummary.getConfig().getNumCpu())
                .setMac(mac)
                .setDiskFreeBytes(diskFreeBytes)
                .setDiskUsedBytes(Convert.toBigDecimal(storageUsage))
                .setTotalDiskCapacity(Convert.toBigDecimal(storageTotal))
                .setCloudSize(Convert.toBigDecimal(virtualTotal))
                .setTypeName(typeName)
                .setVms(vms)
                .setPlatformId(platform.getPlatformId())
                .setPlatformName(platform.getPlatformName())
                .setRegionId(platform.getRegionId())
                .setNetworkInBytes(Convert.toBigDecimal(networkRx))
                .setNetworkOutBytes(Convert.toBigDecimal(networkTx))
                .setNetworkInPackets(Convert.toBigDecimal(netPacRx))
                .setNetworkOutPackets(Convert.toBigDecimal(netPacTx))
                .setVCreateDate(createDate)
                .setActualSize(actualSize)
                .setDeleted(0)
                .setPowerState(status)
                .setZoneName(host.getParent().getParent().getParent().getName())
                .setImageName(imageName)
                .setImageUuid(imageUuid)
                .setAutoInitType(autoInitType)
                .setGuideMode(guideMode);
    }

    private Map<String, Long> processDiskSizes(Map<String, Long> originalMap) {
        Map<String, Long> processedMap = new HashMap<>();

        originalMap.forEach((fileName, size) -> {
            String baseFileName = fileName;
            // 只匹配 -flat 和 -delta 后缀
            if (fileName.matches(".*(-flat|-delta|-digest|-sesparse)\\.vmdk$")) {
                baseFileName = fileName.replaceFirst("(-flat|-delta|-digest|-sesparse)\\.vmdk$", ".vmdk");
            }
            processedMap.merge(baseFileName, size, Long::sum);
        });

        return processedMap;
    }

    private BigDecimal calculateDiskUsage(VirtualMachine virtualMachine, VirtualDevice[] devices) {
        // 获取磁盘文件信息
        List<VmDiskFileInfo> diskFiles = getVmDiskInfoWithActualSizeVm(virtualMachine);

        // 获取虚拟磁盘信息
        List<Map<String, Object>> virtualDisks = getVirtualDisksInfo(devices);
        if (virtualDisks.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 构建文件大小映射
        Map<String, Long> fileSizeMap = buildFileSizeMap(diskFiles);
        Map<String, Long> processedSizeMap = processDiskSizes(fileSizeMap);

        // 只处理第一个磁盘（根磁盘）
        Map<String, Object> rootDisk = virtualDisks.get(0);
        String diskName = Convert.toStr(rootDisk.get("name"));
        Long actualSize = processedSizeMap.getOrDefault(diskName, 0L);
        Long capacityInKB = Convert.toLong(rootDisk.get("capacityInKB"))*1024;

        return NumberUtil.div(actualSize, capacityInKB, 2).multiply(new BigDecimal(100));
    }

    private List<Map<String, Object>> getVirtualDisksInfo(VirtualDevice[] devices) {
        return Optional.ofNullable(devices)
                .map(devs -> Arrays.stream(devs)
                        .filter(dev -> dev instanceof VirtualDisk)
                        .map(this::extractDiskInfo)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    private Map<String, Object> extractDiskInfo(VirtualDevice dev) {
        VirtualDisk disk = (VirtualDisk) dev;
        VirtualDeviceBackingInfo backing = disk.getBacking();

        if (!(backing instanceof VirtualDiskFlatVer2BackingInfo ||
                backing instanceof VirtualDiskSparseVer2BackingInfo)) {
            log.warn("未知的磁盘backing类型: {}", backing.getClass().getName());
            return null;
        }

        Map<String, Object> diskInfo = new HashMap<>(4);
        if (backing instanceof VirtualDiskFlatVer2BackingInfo) {
            VirtualDiskFlatVer2BackingInfo flatBacking = (VirtualDiskFlatVer2BackingInfo) backing;
            diskInfo.put("uuid", flatBacking.getUuid());
            diskInfo.put("name", flatBacking.getFileName());
            diskInfo.put("datastore", flatBacking.getDatastore().getVal());
        } else {
            VirtualDiskSparseVer2BackingInfo sparseBacking = (VirtualDiskSparseVer2BackingInfo) backing;
            diskInfo.put("uuid", sparseBacking.getUuid());
            diskInfo.put("name", sparseBacking.getFileName());
            diskInfo.put("datastore", sparseBacking.getDatastore().getVal());
        }
        diskInfo.put("capacityInKB", disk.getCapacityInKB());
        return diskInfo;
    }

    private Map<String, Long> buildFileSizeMap(List<VmDiskFileInfo> diskFiles) {
        return diskFiles.stream()
                .collect(Collectors.toMap(
                        info -> Convert.toStr(info.getPath()),
                        info -> Convert.toLong(info.getFileSize()),
                        (v1, v2) -> v1 > v2 ? v1 : v2
                ));
    }
    
    /**
     * 获取虚拟机创建时间
     * 通过查询事件历史记录获取虚拟机的创建时间
     */
    private Date getVmCreateTime(ServiceInstance serviceInstance, VirtualMachine vm) {
        try {
            EventManager eventManager = serviceInstance.getEventManager();
            if (eventManager == null) {
                return new Date(); // 如果无法获取事件管理器，返回当前时间
            }
            
            // 创建事件过滤器
            EventFilterSpec eventFilterSpec = new EventFilterSpec();
            
            // 设置实体过滤器
            EventFilterSpecByEntity entitySpec = new EventFilterSpecByEntity();
            entitySpec.setEntity(vm.getMOR());
            entitySpec.setRecursion(EventFilterSpecRecursionOption.self);
            eventFilterSpec.setEntity(entitySpec);
            
            // 设置事件类型过滤器 - 只查询虚拟机创建事件
            eventFilterSpec.setType(new String[]{"VmCreatedEvent", "VmRegisteredEvent", "VmClonedEvent"});
            
            // 查询事件
            Event[] events = eventManager.queryEvents(eventFilterSpec);
            
            if (events != null && events.length > 0) {
                // 按时间排序，获取最早的事件
                Arrays.sort(events, Comparator.comparing(Event::getCreatedTime));
                return events[0].getCreatedTime().getTime();
            } else {
                // 尝试获取文件信息
                try {
                    VirtualMachineFileLayoutEx fileLayoutEx = vm.getLayoutEx();
                    if (fileLayoutEx != null && fileLayoutEx.getFile() != null) {
                        // 查找最早的文件创建时间
                        for (VirtualMachineFileLayoutExFileInfo fileInfo : fileLayoutEx.getFile()) {
                            if (fileInfo.getType().equals(VirtualMachineFileLayoutExFileType.config.toString())) {
                                // 配置文件通常是最早创建的
                                return new Date(System.currentTimeMillis() - (86400000L * 30)); // 假设创建于30天前
                            }
                        }
                    }
                } catch (Exception e) {
                    log.debug("无法获取虚拟机文件信息: {}", e.getMessage());
                }
                
                // 尝试获取虚拟机摘要信息中的运行时间
                try {
                    VirtualMachineRuntimeInfo runtimeInfo = vm.getRuntime();
                    if (runtimeInfo != null && runtimeInfo.getBootTime() != null) {
                        // 使用启动时间作为近似创建时间
                        Calendar bootTime = runtimeInfo.getBootTime();
                        // 假设创建时间比启动时间早7天
                        Calendar createTime = (Calendar) bootTime.clone();
                        createTime.add(Calendar.DAY_OF_MONTH, -7);
                        return createTime.getTime();
                    }
                } catch (Exception e) {
                    log.debug("无法获取虚拟机运行时信息: {}", e.getMessage());
                }
            }
        } catch (Exception e) {
            log.warn("获取虚拟机创建时间失败: {}", e.getMessage());
        }
        
        // 如果所有方法都失败，返回当前时间减去90天作为估计创建时间
        Calendar estimatedCreateTime = Calendar.getInstance();
        estimatedCreateTime.add(Calendar.DAY_OF_MONTH, -90);
        return estimatedCreateTime.getTime();
    }

    private List<HostSystem> getHostList(ServiceInstance serviceInstance) throws Exception {
        // 创建服务实例并获取主机列表
        List<HostSystem> systems = new ArrayList<>();
        HostSystem hostSystem;
        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("HostSystem");
        if (managedEntities != null) {
            for (ManagedEntity managedEntity : managedEntities) {
                hostSystem = (HostSystem) managedEntity;
                systems.add(hostSystem);
            }
        }
        return systems;
    }

    private String getState(String powerState) {
        return switch (powerState) {
            case "poweredOff" -> "Stopped";
            case "poweredOn" -> "Running";
            case "suspended" -> "Suspended";
            default -> "Unknown";
        };
    }

    public String powerStateConvert(String state) {
        return  switch (state) {
            case "Created" -> "on";
            case "Starting" -> "on";
            case "Running" -> "on";
            case "Stopping" -> "off";
            case "Stopped" -> "off";
            case "Unknown" -> "unknown";
            case "Rebooting" -> "on";
            case "Destroyed" -> "off";
            case "Destroying" -> "off";
            case "Migrating" -> "on";
            case "Expunging" -> "off";
            case "Paunging" -> "off";
            case "Paused" -> "off";
            case "Resuming" -> "on";
            case "VolumeMigrating" -> "on";
            default -> "unknown";
        };
    }

    public List<VmDiskFileInfo> getVmDiskInfoWithActualSizeVm(VirtualMachine vm) {
        if (vm == null) {
            throw new IllegalArgumentException("VirtualMachine cannot be null");
        }

        List<VmDiskFileInfo> diskInfoList = new ArrayList<>();
        try {
            VirtualMachineFileLayoutEx fileLayoutEx = vm.getLayoutEx();
            if (fileLayoutEx == null) {
                log.warn("VM [{}] 的 FileLayoutEx 为空，无法获取磁盘信息", vm.getName());
                return diskInfoList;
            }

            for (VirtualMachineFileLayoutExFileInfo fileInfo : fileLayoutEx.getFile()) {
                if (fileInfo == null) {
                    continue;
                }
                // 获取vmdk 文件
                if (fileInfo.getName().contains(".vmdk")) {
                    VmDiskFileInfo diskInfo = new VmDiskFileInfo();
                    diskInfo.setPath(fileInfo.getName());
                    diskInfo.setFileSize(fileInfo.getSize());
                    diskInfoList.add(diskInfo);
                }
            }
        } catch (Exception e) {
            log.error("获取VM [{}] 磁盘信息时发生错误", vm.getName(), e);
            throw new RuntimeException("Failed to get VM disk info: " + e.getMessage(), e);
        }

        return diskInfoList;
    }

    private String getMacAddresses(VirtualMachine virtualMachine) {
        GuestNicInfo[] guestNicInfos = virtualMachine.getGuest().getNet();
        if (guestNicInfos == null) return "";

        return Arrays.stream(guestNicInfos)
                .map(GuestNicInfo::getMacAddress)
                .collect(Collectors.joining(","));
    }

    private long getNetworkUsage(ServiceInstance serviceInstance, ManagedEntity managedEntities, String vmName, String metric, Platform platform) throws Exception {
        List<PerMonitorDO> metrics = RealtimePerfMonitor.getPerEntityMetricBasesByName(vmName, serviceInstance, managedEntities, metric, platform);
        return metrics.stream().mapToLong(PerMonitorDO::getValue).sum();
    }


    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_VM.code();
    }
}
