/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.coder.zj.module.collector.remoting.netty.handler.processor;

import cn.coder.zj.module.collector.collect.strategy.TokenStrategyFactory;
import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.job.cache.TaskCacheModel;
import cn.coder.zj.module.collector.job.service.ScheduleTaskService;
import cn.coder.zj.module.collector.remoting.netty.config.CollectorConfig;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.autoDiscovery.AutoDiscoveryService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.util.StringUtils;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.List;

import static cn.coder.zj.module.collector.job.annotation.YQJobProcessor.TASK_CACHE;

/**
 * 通用客户端处理器
 * 处理所有类型的客户端消息
 * 
 * <AUTHOR>
 */
@Slf4j
public class UniversalClientProcessor implements ClientMessageProcessor {
    
    private final TaskExecutor taskExecutor;
    private final ScheduleTaskService scheduleTaskService;
    private final CollectorConfig collectorConfig;
    private final AutoDiscoveryService autoDiscoveryService;
    
    public UniversalClientProcessor(TaskExecutor taskExecutor,
                                  ScheduleTaskService scheduleTaskService,
                                  CollectorConfig collectorConfig,
                                  AutoDiscoveryService autoDiscoveryService) {
        this.taskExecutor = taskExecutor;
        this.scheduleTaskService = scheduleTaskService;
        this.collectorConfig = collectorConfig;
        this.autoDiscoveryService = autoDiscoveryService;
    }
    
    @Override
    public ClusterMsg.Message process(ChannelHandlerContext ctx, ClusterMsg.Message message) {
        ClusterMsg.MessageType messageType = message.getType();
        String clientId = message.getClientId();
        String jsonStr = message.getData();
        
        log.debug("[通用处理器] 客户端: {}, 消息类型: {}, 数据长度: {}", 
                clientId, messageType, jsonStr != null ? jsonStr.length() : 0);
        
        try {
            switch (messageType) {
                case INFO:
                case UPDATE:
                    return processPlatformInfo(jsonStr, clientId, messageType);
                case SCHEDULED_START:
                    return processScheduledStart(jsonStr, clientId);
                case SCHEDULED_INFO:
                    return processScheduledInfo(clientId);
                case ONLINE:
                    return processOnline(clientId);
                case OFFLINE:
                    return processOffline(clientId);
                case OTHER:
                    return processOther(jsonStr, message.getMetrics(), clientId);
                default:
                    log.warn("[通用处理器] 客户端: {}, 未知消息类型: {}", clientId, messageType);
                    return null;
            }
        } catch (Exception e) {
            log.error("[通用处理器] 客户端: {}, 消息类型: {}, 处理异常: {}", 
                    clientId, messageType, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 处理平台信息
     */
    private ClusterMsg.Message processPlatformInfo(String jsonStr, String clientId, ClusterMsg.MessageType messageType) {
        if (ClusterMsg.MessageType.UPDATE == messageType) {
            log.info("[平台信息更新] 客户端: {}, 收到更新数据指令", clientId);
        }
        
        try {
            Type listType = new TypeToken<List<Platform>>() {}.getType();
            List<Platform> platformInfo = new Gson().fromJson(jsonStr, listType);
            
            log.info("[平台信息处理] 客户端: {}, 收到平台数量: {}", clientId, platformInfo.size());
            
            for (Platform p : platformInfo) {
                // 验证平台信息的完整性
                if (!validatePlatform(p)) {
                    log.warn("[平台信息验证] 客户端: {}, 平台信息不完整，跳过处理: {}", clientId, p.getPlatformName());
                    continue;
                }
                
                taskExecutor.execute(() -> {
                    try {
                        log.info("[平台处理开始] 客户端: {}, 平台: {} (ID: {}, Type: {})", 
                                clientId, p.getPlatformName(), p.getPlatformId(), p.getTypeCode());
                        
                        AbstractToken abstractCollect = TokenStrategyFactory.invoke(p.getTypeCode());
                        
                        // 先执行preCheck进行连接验证（不更新状态）
                        abstractCollect.preCheck(p);

                        // 再执行token方法获取认证信息并更新最终状态
                        if (p.getState()==0){
                            abstractCollect.token(p);
                        }

                        
                        log.info("[平台处理完成] 客户端: {}, 平台: {} 处理成功", clientId, p.getPlatformName());
                        
                    } catch (Exception e) {
                        log.error("[平台处理异常] 客户端: {}, 平台: {} 处理失败: {}", 
                                clientId, p.getPlatformName(), e.getMessage(), e);
                    }
                });
            }
            
        } catch (Exception e) {
            log.error("[平台信息处理] 客户端: {}, 处理平台信息失败: {}", clientId, e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 验证平台信息的完整性
     */
    private boolean validatePlatform(Platform platform) {
        if (platform == null) {
            return false;
        }
        
        if (!StringUtils.hasText(platform.getPlatformName())) {
            log.warn("[平台验证] 平台名称为空");
            return false;
        }
        
        if (!StringUtils.hasText(platform.getTypeCode())) {
            log.warn("[平台验证] 平台类型为空: {}", platform.getPlatformName());
            return false;
        }
        
        if (platform.getPlatformId() == null) {
            log.warn("[平台验证] 平台ID为空: {}", platform.getPlatformName());
            return false;
        }
        
        return true;
    }
    
    /**
     * 处理定时任务启动
     */
    private ClusterMsg.Message processScheduledStart(String jsonStr, String clientId) {
        log.info("[定时任务启动] 客户端: {}, 开始处理定时任务", clientId);
        
        try {
            scheduleTaskService.clearAllTasks();
            Gson gson = new Gson();
            Type listType = new TypeToken<List<String>>() {}.getType();
            List<String> jobNames = gson.fromJson(jsonStr, listType);
            
            int successCount = 0;
            for (String jobName : jobNames) {
                TaskCacheModel taskCacheModel = TASK_CACHE.get(jobName);
                if (taskCacheModel != null) {
                    taskCacheModel.setStatus(1);
                    scheduleTaskService.addTask(taskCacheModel);
                    successCount++;
                } else {
                    log.warn("[定时任务] 未找到任务: {}", jobName);
                }
            }
            
            log.info("[定时任务启动] 客户端: {}, 启动任务成功: {}/{}", 
                    clientId, successCount, jobNames.size());
                    
        } catch (Exception e) {
            log.error("[定时任务启动] 客户端: {}, 启动任务失败: {}", 
                    clientId, e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 处理定时任务信息获取
     */
    private ClusterMsg.Message processScheduledInfo(String clientId) {
        log.debug("[定时任务信息] 客户端: {}, 获取定时任务信息", clientId);
        
        try {
            SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
            List<TaskCacheModel> cacheModels = scheduleTaskService.listTasks();
            
            ClusterMsg.Message response = ClusterMsg.Message.newBuilder()
                    .setData(new GsonBuilder()
                            .excludeFieldsWithoutExposeAnnotation()
                            .create().toJson(cacheModels))
                    .setClientId(collectorConfig.getClientId())
                    .setType(ClusterMsg.MessageType.SCHEDULED_INFO)
                    .setTime(System.currentTimeMillis())
                    .build();
            
            sendMessageService.sendMessage(CacheService.getCtx("ctx"), response);
            log.info("[定时任务信息] 客户端: {}, 发送任务信息成功, 任务数量: {}", 
                    clientId, cacheModels.size());
                    
        } catch (Exception e) {
            log.error("[定时任务信息] 客户端: {}, 发送任务信息失败: {}", 
                    clientId, e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 处理客户端上线
     */
    private ClusterMsg.Message processOnline(String clientId) {
        log.info("[客户端上线] 客户端: {}, 处理上线消息", clientId);
        
        try {
            Collection<TaskCacheModel> values = TASK_CACHE.values();
            int count = 0;
            
            for (TaskCacheModel taskCacheModel : values) {
                taskCacheModel.setStatus(1);
                scheduleTaskService.addTask(taskCacheModel);
                count++;
            }
            
            log.info("[客户端上线] 客户端: {}, 启动任务数量: {}", clientId, count);
            
        } catch (Exception e) {
            log.error("[客户端上线] 客户端: {}, 处理上线消息失败: {}", 
                    clientId, e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 处理客户端下线
     */
    private ClusterMsg.Message processOffline(String clientId) {
        log.info("[客户端下线] 客户端: {}, 处理下线消息", clientId);
        
        try {
            Collection<TaskCacheModel> values = TASK_CACHE.values();
            int count = 0;
            
            for (TaskCacheModel taskCacheModel : values) {
                taskCacheModel.setStatus(0);
                scheduleTaskService.removeTask(taskCacheModel);
                count++;
            }
            
            log.info("[客户端下线] 客户端: {}, 停止任务数量: {}", clientId, count);
            
        } catch (Exception e) {
            log.error("[客户端下线] 客户端: {}, 处理下线消息失败: {}", 
                    clientId, e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 处理其他类型消息
     */
    private ClusterMsg.Message processOther(String jsonStr, String metrics, String clientId) {
        log.debug("[其他消息] 客户端: {}, 指标: {}", clientId, metrics);
        autoDiscoveryService.autoDiscovery(jsonStr, metrics);
        return null;
    }
    
    @Override
    public ClusterMsg.MessageType getMessageType() {
        // 通用处理器支持所有类型
        return ClusterMsg.MessageType.OTHER;
    }
} 