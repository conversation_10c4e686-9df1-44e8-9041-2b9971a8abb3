package cn.coder.zj.module.collector.collect.metrics.cpu;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.util.*;

import static cn.coder.zj.module.collector.enums.CpuType.PROTOCOL_WIN_HONG_CPU;
import static cn.coder.zj.module.collector.enums.MetricNameType.CPU_USED_TASK;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class WinHongCpuImpl extends AbstractMetrics {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();
                Platform platform = (Platform) o;
                String token = platform.getWinHongPlatform().getToken();
                if (token == null) {
                    log.error("平台 {} token为空", platform.getPlatformName());
                    return;
                }
                metricDataList.addAll(vmdata(platform));
                metricDataList.addAll(hostData(platform));

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.CPU_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                log.info("collectVmData name: {}", Thread.currentThread().getName());
            });
        }
    }

    public List<MetricData> vmdata(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_CLOUDS, null, headers).getAsJsonObject().get("data").getAsJsonArray();

        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();

        //所有cup使用率
        String endTime = Convert.toStr(DateUtil.currentSeconds());
        String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);
        List<MetricData> metricDataList = new ArrayList<>();
        for (JsonElement host1 : hostArray) {
            JsonObject host = host1.getAsJsonObject();
            if (!Objects.equals(host.getAsJsonObject().get("status").getAsString(), "1")) {
                continue;
            }
            Map<String, String> param = new HashMap<>();
            param.put("domainIds", host.get("uuid").getAsString());
            param.put("startTime", startTime);
            param.put("endTime", endTime);
            JsonArray cpuRateList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HOST_CPU_RATE, param, headers).getAsJsonArray();
            if (!cpuRateList.isEmpty()) {
                MetricData metricData = new MetricData();
                metricData.setPlatformId(platform.getPlatformId());
                metricData.setResourceId(host.get("uuid").getAsString());
                metricData.setResourceName(host.get("name").getAsString());
                metricData.setType("vm");

                for (JsonElement cpuRate : cpuRateList) {
                    JsonObject usageItem = cpuRate.getAsJsonObject();
                    metricData.setResourceId(usageItem.get("domainId").getAsString());
                    metricData.setResourceName(usageItem.get("domainName").getAsString());
                    JsonArray data = usageItem.get("data").getAsJsonArray();
                    if (!data.isEmpty()) {
                        List<Long> timestamps = new ArrayList<>();
                        List<Double> values = new ArrayList<>();
                        for (JsonElement item : data) {
                            JsonObject cpu = item.getAsJsonObject();
                            timestamps.add( DateUtil.parse(cpu.get("time").getAsString()).getTime() / 1000);
                            values.add(cpu.get("value").getAsDouble());
                            metricData.setMetricName(CPU_USED_TASK.code());
                            metricData.setTimestamps(timestamps);
                            metricData.setValues(values);
                            metricDataList.add(metricData);
                            break;
                        }
                    }
                }

            }
        }

        return metricDataList;
    }

    public List<MetricData> hostData(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_HARDWARE_LIST, null, headers).getAsJsonObject().get("data").getAsJsonArray();;
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();

        List<MetricData> metricDataList = new ArrayList<>();
        for (JsonElement jsonElement : hostArray) {
            JsonObject host = jsonElement.getAsJsonObject();
            String uuid = host.get("id").getAsString();
            String endTime = Convert.toStr(DateUtil.currentSeconds());
            String startTime = Convert.toStr(DateUtil.offsetMinute(new Date(), -16).getTime() / 1000);

            Map<String, String> param = new HashMap<>();
            param.put("hostId", uuid);
            param.put("endTime", endTime);
            param.put("startTime", startTime);
            List<Long> timestamps = new ArrayList<>();
            List<Double> values = new ArrayList<>();
            JsonArray cpuRateList = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_CPU_RATE, param, headers).getAsJsonObject().get("data").getAsJsonArray();;
            MetricData metricData = new MetricData();
            metricData.setResourceId(uuid);
            metricData.setResourceName(host.get("hostname").getAsString());
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setType("host");
            for (JsonElement cpuRateI : cpuRateList) {
                JsonObject usageItem = cpuRateI.getAsJsonObject();
                timestamps.add( DateUtil.parse(usageItem.get("time").getAsString()).getTime() / 1000);
                values.add(usageItem.get("value").getAsDouble());
                metricData.setMetricName(CPU_USED_TASK.code());
                metricData.setTimestamps(timestamps);
                metricData.setValues(values);
                metricDataList.add(metricData);
                break;
            }
        }
        return metricDataList;
    }


    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            return GSON.fromJson(responseBody, JsonElement.class);
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_WIN_HONG_CPU.code();
    }
}
