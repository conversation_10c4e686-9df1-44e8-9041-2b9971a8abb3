package cn.coder.zj.module.collector.collect.basicdata.manager.sangfor;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_SXF_VM_VIC;

@Slf4j
public class SangForBasicVmNicDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VmNicData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VM_VIC.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VmNicData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie,"CSRFPreventionToken",token);

        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_CLOUDS, null, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<VmNicData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    VmNicData vmData = collectVmNicData(platform, jsonElement,headers);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理深信服云主机网络数据异常, hostMap: {}, error: {}", jsonElement.toString(), e.getMessage());
                }
            }
        }
        return dataList;
    }

    private VmNicData collectVmNicData(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();

        String vmid = getStringFromJson(jsonObject, "vmid", "");
        JsonObject netCloudInfo = getJsonObjectFromApi(platform.getPlatformUrl() + SangForApiConstant.GET_VM_CLOUD_INFO.replace("{vmid}", vmid), null, headers);
        VmNicData vmNicData = new VmNicData();
        vmNicData.setHostUuid(StrUtil.isNotEmpty(getStringFromJson(jsonObject,"uuid","")) ?
                getStringFromJson(jsonObject,"uuid","") : getStringFromJson(jsonObject,"vmid",""));
        String ifaceId = getStringFromJson(netCloudInfo, "iface_id", "");
        if(StrUtil.isNotEmpty(ifaceId)){
            vmNicData.setUuid(ifaceId);
        }else {
            vmNicData.setUuid(StrUtil.isNotEmpty(getStringFromJson(jsonObject,"uuid","")) ?
                    getStringFromJson(jsonObject,"uuid","") : getStringFromJson(jsonObject,"vmid",""));
        }
        vmNicData.setIp(getStringFromJson(netCloudInfo, "ip", ""));
        vmNicData.setIp6("");
        vmNicData.setPlatformId(platform.getPlatformId());
        vmNicData.setPlatformName(platform.getPlatformName());
        vmNicData.setMac(getStringFromJson(netCloudInfo, "mac_addr", ""));
        vmNicData.setDriver("virtio");
        vmNicData.setInClassicNetwork((byte) 0);
        vmNicData.setNetworkUuid(getStringFromJson(netCloudInfo, "peer_vlan_group_id", ""));
        return vmNicData;
    }

    @Override
    public String supportProtocol() {
        return BASIC_SXF_VM_VIC.code();
    }
}
