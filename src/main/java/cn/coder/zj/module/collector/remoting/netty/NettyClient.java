package cn.coder.zj.module.collector.remoting.netty;

import cn.coder.zj.module.collector.job.service.ScheduleTaskService;
import cn.coder.zj.module.collector.remoting.netty.config.CollectorConfig;
import cn.coder.zj.module.collector.remoting.netty.handler.AuthHandler;
import cn.coder.zj.module.collector.remoting.netty.handler.BusinessHandler;
import cn.coder.zj.module.collector.remoting.netty.handler.HeartHandler;
import cn.coder.zj.module.collector.service.autoDiscovery.AutoDiscoveryService;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.compression.ZlibCodecFactory;
import io.netty.handler.codec.compression.ZlibWrapper;
import io.netty.handler.codec.protobuf.ProtobufDecoder;
import io.netty.handler.codec.protobuf.ProtobufEncoder;
import io.netty.handler.codec.protobuf.ProtobufVarint32FrameDecoder;
import io.netty.handler.codec.protobuf.ProtobufVarint32LengthFieldPrepender;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyClient {

    private final TaskExecutor taskExecutor;

    private final CollectorConfig collectorConfig;

    private final ScheduleTaskService scheduleTaskService;

    private final AutoDiscoveryService autoDiscoveryService;

    public NettyClient(TaskExecutor taskExecutor, CollectorConfig collectorConfig, ScheduleTaskService scheduleTaskService, AutoDiscoveryService autoDiscoveryService) {
        this.taskExecutor = taskExecutor;
        this.collectorConfig = collectorConfig;
        this.scheduleTaskService = scheduleTaskService;
        this.autoDiscoveryService = autoDiscoveryService;
    }


    private final EventLoopGroup loop = new NioEventLoopGroup();

    public Bootstrap doConnect(Bootstrap bootstrap, EventLoopGroup eventLoopGroup,String host ,int port) {
        try {
            if (bootstrap != null) {
                bootstrap.group(eventLoopGroup);
                bootstrap.channel(NioSocketChannel.class);
                bootstrap.option(ChannelOption.SO_KEEPALIVE, true);
                bootstrap.handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    public void initChannel(SocketChannel ch) {
                        ChannelPipeline p = ch.pipeline();
                        p.addLast(ZlibCodecFactory.newZlibEncoder(ZlibWrapper.GZIP));
                        p.addLast(ZlibCodecFactory.newZlibDecoder(ZlibWrapper.GZIP));
                        p.addLast("idleStateHandler", new IdleStateHandler(0
                                , 10, 0, TimeUnit.SECONDS));
                        p.addLast(new ProtobufVarint32FrameDecoder());
                        p.addLast(new ProtobufDecoder(ClusterMsg.Message.getDefaultInstance()));
                        p.addLast(new ProtobufVarint32LengthFieldPrepender());
                        p.addLast(new ProtobufEncoder());
                        p.addLast("heartHandler", new HeartHandler(NettyClient.this,collectorConfig));
                        p.addLast("authHandler", new AuthHandler(collectorConfig));
                        p.addLast("businessHandler", new BusinessHandler(taskExecutor,scheduleTaskService,collectorConfig,autoDiscoveryService));
                    }
                });
                bootstrap.remoteAddress(host, port);
                ChannelFuture f = bootstrap.connect().addListener((ChannelFuture futureListener) -> {
                    final EventLoop eventLoop = futureListener.channel().eventLoop();
                    if (!futureListener.isSuccess()) {
                        log.warn("failed to connect to the server at {}:{}. retrying in 5 seconds!", host, port);
                        futureListener.channel().eventLoop().schedule(() -> doConnect(new Bootstrap(), eventLoop,host,port), 5, TimeUnit.SECONDS);
                    }
                });
                f.channel().closeFuture().sync();
            }
        } catch (InterruptedException e) {
            log.error("netty server error:{}", ExceptionUtils.getStackTrace(e));
            Thread.currentThread().interrupt();
        }
        return bootstrap;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        if (collectorConfig.isStart()) {
            Map<Integer, String> serverInfo = collectorConfig.getServerInfo();
            for (Map.Entry<Integer, String> entry : serverInfo.entrySet()) {
                Runnable voidFunc = () -> doConnect(new Bootstrap(), loop, entry.getValue(), entry.getKey());
                taskExecutor.execute(voidFunc);
            }
        }
    }


    public void stop() {
        try {
            loop.shutdownGracefully().sync();
            log.info("Netty client has been shut down.");
        } catch (InterruptedException e) {
            log.error("An error occurred while shutting down the Netty client: {}", ExceptionUtils.getStackTrace(e));
            Thread.currentThread().interrupt();
        }
    }



}
