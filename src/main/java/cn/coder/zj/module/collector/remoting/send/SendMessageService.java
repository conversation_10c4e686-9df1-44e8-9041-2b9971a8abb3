package cn.coder.zj.module.collector.remoting.send;

import cn.iocoder.zj.framework.common.message.ClusterMsg;
import io.netty.channel.ChannelHandlerContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 **/
@Service
public class SendMessageService {

    public void sendMessage(ChannelHandlerContext ctx, ClusterMsg.Message messageInfo) {
        ctx.channel().writeAndFlush(messageInfo);
    }
}
