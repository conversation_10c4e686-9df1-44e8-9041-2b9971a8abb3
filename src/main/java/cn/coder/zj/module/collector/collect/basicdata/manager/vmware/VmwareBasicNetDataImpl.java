package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL2Data;
import cn.iocoder.zj.framework.common.dal.manager.NetWorkL3Data;
import cn.iocoder.zj.framework.common.enums.MetricsType;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.HostPortGroup;
import com.vmware.vim25.HostVirtualNic;
import com.vmware.vim25.mo.*;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.rmi.RemoteException;
import java.util.*;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_NET;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_NET;
@Slf4j
public class VmwareBasicNetDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                try {
                    List<NetWorkL2Data> netWorkL2DataList = new ArrayList<>();
                    List<NetWorkL3Data> netWorkL3DataList = new ArrayList<>();
                    processNetWorkData(platform,netWorkL2DataList,netWorkL3DataList);
                    //l2网卡
                    if (!CollUtil.isEmpty(netWorkL2DataList)) {
                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(netWorkL2DataList)
                                .metricsName(BASIC_NET.code())
                                .metricsType(MetricsType.BASIC_NET_L2.code())
                                .build()));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                    }
                    //l3网卡
                    if(!CollUtil.isEmpty(netWorkL3DataList)){
                        ChannelHandlerContext ctx = CacheService.getCtx("ctx");
                        ClusterMsg.Message.Builder messageL3 = ClusterMsg.Message.newBuilder().setType(ClusterMsg.MessageType.BASIC)
                                .setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(netWorkL3DataList)
                                        .metricsName(BASIC_NET.code())
                                        .metricsType(MetricsType.BASIC_NET_L3.code())
                                        .build()));
                        messageL3.setTime(System.currentTimeMillis());
                        ctx.writeAndFlush(messageL3);
                    }
                } catch (RemoteException e) {
                    throw new RuntimeException(e);
                }
            });
            String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
            log.info("collect basic data end, cost {} seconds", endTimeFormatted);
        }
    }

    private void processNetWorkData(Platform platform, List<NetWorkL2Data> netWorkL2DataList, List<NetWorkL3Data> netWorkL3DataList) throws RemoteException {
        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();
        List<HostSystem> systems = getHostList(serviceInstance);
        for (HostSystem hostSystem : systems) {
            collectNetInfo(platform,hostSystem,netWorkL2DataList,netWorkL3DataList);
        }
    }

    private void collectNetInfo(Platform platform,HostSystem hostSystem, List<NetWorkL2Data> netWorkL2DataList, List<NetWorkL3Data> netWorkL3DataList) throws RemoteException {
        // 获取网络相关信息
        Network[] networks = hostSystem.getNetworks();
        HostVirtualNic[] vNic = hostSystem.getHostNetworkSystem().getNetworkInfo().getVnic();
        // 获取主机端口组信息
        List<Map<String, Object>> portGroupList = getHostPortGroups(hostSystem);
        // 处理分布式交换机
        Arrays.stream(networks).forEach(network -> {
            NetWorkL2Data netWorkL2Data = buildBaseNetworkDate(network, platform, portGroupList);
            netWorkL2DataList.add(netWorkL2Data);
        });

        // 处理虚拟网卡和L3网络
        for (HostVirtualNic hostVirtualNic : vNic) {
            NetWorkL2Data netWorkL2Data = new NetWorkL2Data();
            netWorkL2Data.setPhysicalInterface(hostVirtualNic.getDevice());
            netWorkL2Data.setVirtualNetworkId(0);
            // 设置平台信息
            setPlatformInfo(netWorkL2Data, platform);
            NetWorkL2Data netWorkL2 = buildVnicNetworkData(netWorkL2Data, hostVirtualNic, portGroupList);
            netWorkL2DataList.add(netWorkL2);
            // 创建L3网络信息
            NetWorkL3Data netWorkL3Data = createL3NetworkFromVirtualNic(platform, netWorkL2, hostVirtualNic);
            netWorkL3DataList.add(netWorkL3Data);
        }

    }

    private List<HostSystem> getHostList(ServiceInstance serviceInstance) throws RemoteException {
        // 创建服务实例并获取主机列表
        List<HostSystem> systems = new ArrayList<HostSystem>();
        HostSystem hostSystem = null;
        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("HostSystem");
        if (managedEntities != null) {
            for (ManagedEntity managedEntity : managedEntities) {
                hostSystem = (HostSystem) managedEntity;
                systems.add(hostSystem);
            }
        }
        return systems;
    }

    private List<Map<String, Object>> getHostPortGroups(HostSystem hostSystem){
        HostPortGroup[] portGroups = hostSystem.getConfig().getNetwork().getPortgroup();
        List<Map<String, Object>> portGroupList = new ArrayList<>();

        for (HostPortGroup portGroup : portGroups) {
            String name = portGroup.getSpec().getName();
            // 检查是否已存在相同名称的端口组
            boolean exists = portGroupList.stream()
                    .anyMatch(item -> item.get("name").equals(name));
            if (!exists) {
                Map<String, Object> data = new HashMap<>();
                data.put("name", name);
                data.put("vlanid", portGroup.getSpec().getVlanId());
                portGroupList.add(data);
            }
        }
        return portGroupList;
    }

    private NetWorkL2Data buildBaseNetworkDate(Network network, Platform platform,
                                             List<Map<String, Object>> portGroupList) {
        String networkName = network.getName();
        String c_uuid = network.getParent().getMOR().getVal();
        String uuid = c_uuid + network.getMOR().getVal() + networkName;

        NetWorkL2Data data = new NetWorkL2Data();
        data.setUuid(uuid);
        data.setName(networkName);
        data.setPhysicalInterface(networkName);
        data.setVirtualNetworkId(0);

        // 设置VLAN信息
        setVlanInfo(data, networkName, portGroupList);

        // 设置平台信息
        setPlatformInfo(data, platform);

        return data;
    }

    private NetWorkL2Data buildVnicNetworkData(NetWorkL2Data netWorkL2Data, HostVirtualNic vNic,List<Map<String, Object>> portGroupList) {
        netWorkL2Data.setUuid(vNic.getKey() + "_" + vNic.getSpec().ip.ipAddress);
        netWorkL2Data.setName(vNic.getDevice());

        // 设置VLAN信息
        setVlanInfo(netWorkL2Data, vNic.getDevice(), portGroupList);

        return netWorkL2Data;
    }

    // 从虚拟网卡创建L3网络信息
    private NetWorkL3Data createL3NetworkFromVirtualNic(Platform platform, NetWorkL2Data netWorkL2, HostVirtualNic hostVirtualNic) {
        String subnetMask = hostVirtualNic.getSpec().ip.subnetMask;
        int prefixLength = getPrefixLength(subnetMask);

        NetWorkL3Data netWorkL3 = new NetWorkL3Data();
        netWorkL3.setUuid(netWorkL2.getUuid() + "_3");
        netWorkL3.setL2NetworkUuid(netWorkL2.getUuid());
        netWorkL3.setName(hostVirtualNic.getDevice());
        netWorkL3.setL2NetworkName(netWorkL2.getName());
        netWorkL3.setType("L3BasicNetwork");
        netWorkL3.setNetworkCidr(hostVirtualNic.getSpec().ip.ipAddress + "/" + prefixLength);
        netWorkL3.setNextHopIp(hostVirtualNic.getSpec().ip.ipAddress);
        netWorkL3.setPlatformId(platform.getPlatformId());
        netWorkL3.setPlatformName(platform.getPlatformName());
//        netWorkL3.setTenantId(platform.getTenantId());
        netWorkL3.setTypeName("vmware");

        return netWorkL3;
    }

    private void setVlanInfo(NetWorkL2Data data, String name, List<Map<String, Object>> portGroupList) {
        portGroupList.stream()
                .filter(map -> name.equals(Convert.toStr(map.get("name"))))
                .findFirst()
                .ifPresent(map -> data.setVlan(Convert.toStr(map.get("vlanid"))));

        data.setType(StrUtil.isNotEmpty(data.getVlan()) ? "VLAN" : "FLAT");
    }

    private void setPlatformInfo(NetWorkL2Data data, Platform platform) {
        data.setTypeName("vmware");
        data.setPlatformName(platform.getPlatformName());
        data.setPlatformId(platform.getPlatformId());
        data.setRegionId(platform.getRegionId());
//        data.setRegionName(platform.getRegionName());
        data.setTenantId(1L);
    }

    private int getPrefixLength(String subnetMask) {
        String[] octets = subnetMask.split("\\.");
        int prefixLength = 0;

        for (String octet : octets) {
            int value = Integer.parseInt(octet);
            // Count the number of 1 bits in the octet
            prefixLength += Integer.bitCount(value);
        }

        return prefixLength;
    }

    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_NET.code();
    }
}
