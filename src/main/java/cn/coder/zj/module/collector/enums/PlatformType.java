package cn.coder.zj.module.collector.enums;


import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public enum PlatformType {

    ZS_TACK("zstack", "zstack平台"),

    VM_WARE("vmware", "vmware平台"),

    SANG_FOR("sangFor", "深信服平台"),

    H3C("h3c", "华三平台"),

    WIN_HONG("winhong", "云宏平台"),

    FUSION_ONE("fusionOne", "fusionOne平台"),

    IN_SPUR("inspur", "浪潮云平台"),

    IS_TACK("istack", "istack平台"),

    IS_TACK2("istack2", "istack2.0平台");;


    private final String code;

    private final String desc;



    public static List<String> getAllPlatformTypeCodes() {
        List<String> codes = new ArrayList<>();
        for (PlatformType platformType : PlatformType.values()) {
            codes.add(platformType.code());
        }
        return codes;
    }

    public String code() {
        return this.code;
    }


}
