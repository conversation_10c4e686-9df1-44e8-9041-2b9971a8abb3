package cn.coder.zj.module.collector.collect.basicdata.manager.winhong;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeSnapshotData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_SNAPSHOT;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_WIN_HONG_SNAPSHOT;

@Slf4j
public class WinHongBasicSnapshotDataImpl extends AbstractBasicData {

    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VolumeSnapshotData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_SNAPSHOT.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VolumeSnapshotData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getWinHongPlatform().getToken();

        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);

        JsonArray volumeSnapshots = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_SNAPSHOTS, null, headers).getAsJsonObject().get("data").getAsJsonArray();
        if (ObjectUtil.isNull(volumeSnapshots)) return new ArrayList<>();
        List<VolumeSnapshotData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(volumeSnapshots)) {
            for (JsonElement vs : volumeSnapshots) {
                VolumeSnapshotData volumeSnapshotDTO = new VolumeSnapshotData();
                JsonObject vsj = (JsonObject)vs;
                String domainId = vsj.get("domainId").getAsString();
                String name = vsj.get("name").getAsString();
                String showName = vsj.get("name").getAsString();
                String description = "";
                if (!vsj.get("description").isJsonNull()) {
                    description = vsj.get("description").getAsString();
                }
                String primaryStorageUuid = "";
                String type = "主机快照";
                String status = vsj.get("status").getAsString();
                if (status.equals("1")) {
                    status = "Enabled";
                } else {
                    status = "Disabled";
                }
                Boolean isMemorySnapshot = vsj.get("isMemorySnapshot").getAsBoolean();
//                        if (jsonObject.getBoolean("isMemorySnapshot")) {
//                            type = "整机";
//                        } else {
//                            type = "仅磁盘";
//                        }
                String volumeType = "Root";
                String latest = "true";
                Long platformId = platform.getPlatformId();
                String platformName = platform.getPlatformName();
                LocalDateTime dateTime = Instant.ofEpochMilli(vsj.get("creationTime").getAsLong())
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
                Date date = Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
                volumeSnapshotDTO.setCreateTime(date);
                long timestamp = vsj.get("creationTime").getAsLong() * 1000;
                volumeSnapshotDTO.setVCreateDate(new Date(timestamp));
                volumeSnapshotDTO.setVUpdateDate(new Date(timestamp));
                volumeSnapshotDTO.setUuid(domainId + ":" + name);
                volumeSnapshotDTO.setName(showName);
                volumeSnapshotDTO.setDescription(description);
                volumeSnapshotDTO.setHostUuid(domainId);
                volumeSnapshotDTO.setStatus(status);
                //查找根云盘uuid
//                        String voUuid=volumeApi.getVolumeByVmUuid(domainId).getData();
//                        volumeSnapshotDTO.setVolumeUuid(voUuid);
                volumeSnapshotDTO.setPrimaryStorageUuid(primaryStorageUuid);
                volumeSnapshotDTO.setType(type);
                volumeSnapshotDTO.setVolumeType(volumeType);
                volumeSnapshotDTO.setLatest(latest);
                volumeSnapshotDTO.setPlatformName(platformName);
                volumeSnapshotDTO.setPlatformId(platformId);
                volumeSnapshotDTO.setTypeName("winhong");
                volumeSnapshotDTO.setIsMemory(isMemorySnapshot);
                volumeSnapshotDTO.setFormat("raw");
                dataList.add(volumeSnapshotDTO);
            }
        }
        return dataList;
    }

    private JsonElement getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            return GSON.fromJson(response.body().string(), JsonElement.class);
        } catch (IOException e) {
            log.error("error collecting basic data: {}", e.getMessage());
            return new JsonObject();
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_WIN_HONG_SNAPSHOT.code();
    }
}
