package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromFsApi;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonObjectFromFsApi;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_HOST;
@Slf4j
public class FsOneBasicHostDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                List<HostData> list = collectData(platform);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_HOST.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<HostData> collectData(Platform platform) {
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }
        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_HOST_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject hosts = FsApiCacheService.getJsonObject(url, null, header);
        JsonArray hostArray = hosts.getAsJsonArray("hosts");
        if (ObjectUtil.isNull(hostArray)) return null;

        //获取存储
        JsonObject storagesObj = getJsonObjectFromFsApi(
                platform.getPlatformUrl() + FusionOneApiConstant.DATASTORES_LIST.replace("{siteId}",
                    platform.getFsOnePlatform().getSiteId()),
             null,
                    header);
        if (ObjectUtil.isNull(storagesObj)) return null;
        JsonArray storages = storagesObj.getAsJsonArray("datastores");
        long num = 0L;
        long used = 0L;
        for (Object storage : storages) {
            JsonObject jsonObject = (JsonObject) storage;
            num  += getLongFromJson(jsonObject ,"actualCapacityGB");
            used += getLongFromJson(jsonObject ,"actualFreeSizeGB");
        }

        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<HostData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    HostData hostData = collectHostInfo(platform, jsonElement,header,num,used);
                    if (hostData != null) {
                        dataList.add(hostData);
                    }
                } catch (Exception e) {
                    log.error("处理Fusionone宿主机数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private HostData collectHostInfo(Platform platform, JsonElement jsonElement, Map<String, String> header, long num, long used) {
        HostData hostData = new HostData();
        String platformUrl = platform.getPlatformUrl();
        String siteId = platform.getFsOnePlatform().getSiteId();

        JsonObject hostBase = GSON.toJsonTree(jsonElement).getAsJsonObject();
        if (ObjectUtil.isNull(hostBase)) return null;

        String hostId = getStringFromJson(hostBase, "uri");
        JsonObject hostInfo = getJsonObjectFromFsApi(platformUrl + hostId, null, header);
        String urn = getStringFromJson(hostBase,"urn");

        // 定义网络相关指标数组并直接构建JsonArray
        JsonObject requestBody = new JsonObject();
        JsonArray metricArray = new JsonArray();
        JsonArray requestArray = new JsonArray();
        String[] metricId = {"nic_byte_in", "nic_byte_out"};
        java.util.Arrays.stream(metricId).forEach(metricArray::add);
        requestBody.add("metricId", metricArray);
        requestBody.addProperty("urn", urn);
        requestArray.add(requestBody);
        JsonArray usageInfo = getJsonArrayFromFsApi(
                platformUrl + FusionOneApiConstant.GET_REAL_TIME_DATA.replace("{siteId}", siteId),
                header,
                requestArray);

        // 初始化网络指标默认值
        Map<String, String> metricValues = new HashMap<>() {{
            put("inBps", "0");
            put("outBps", "0");
        }};

        // 解析网络指标数据
        if (usageInfo != null) {
            usageInfo.forEach(item -> {
                JsonObject obj = (JsonObject) item;
                JsonArray values = obj.getAsJsonArray("value");
                values.forEach(value -> {
                    JsonObject json = (JsonObject) value;
                    String metric = getStringFromJson(json, "metricId");
                    String metricValue = getStringFromJson(json, "metricValue");
                    
                    if ("nic_byte_in".equals(metric)) {
                        metricValues.put("inBps", metricValue);
                    } else {
                        metricValues.put("outBps", metricValue);
                    }
                });
            });
        }

        String inBps = metricValues.get("inBps");
        String outBps = metricValues.get("outBps");
        String inPps = "0";
        hostData.setCpuOverPercent(new BigDecimal(1));
        hostData.setMemoryOverPercent(new BigDecimal(1));

        //可用区
        hostData.setAvailableManager(platform.getFsOnePlatform().getSiteName());
        hostData.setManager(platform.getPlatformName());
        //系统预留内存
        hostData.setReservedMemory(getBigFromJson(hostInfo,"memoryReserve").multiply(new BigDecimal(1024*1024)));
        //查询主机配置信息
        //清空 requestBody metricArray requestArray
        requestBody = new JsonObject();
        metricArray = new JsonArray();
        requestArray = new JsonArray();
        metricId = new String[]{
                "product_name",
                "product_serial",
                "product_mfg",
                "cpu_info"
        };
        java.util.Arrays.stream(metricId).forEach(metricArray::add);
        requestBody.add("metricId", metricArray);
        requestBody.addProperty("urn", urn);
        requestArray.add(requestBody);
        JsonArray realtimeData = getJsonArrayFromFsApi(
                platformUrl + FusionOneApiConstant.GET_REALTIME_DATA.replace("{siteId}", siteId),
                header,
                requestArray
        ).get(0).getAsJsonObject().getAsJsonArray("value");

        for (Object realtimeDatum : realtimeData) {
            JsonObject jsonObject = (JsonObject) realtimeDatum;
            if (getStringFromJson(jsonObject,"metricId").equals("product_mfg")){
                hostData.setBrandName(getStringFromJson(jsonObject,"metricValue"));
            }
            if (getStringFromJson(jsonObject,"metricId").equals("product_name")){
                hostData.setModel(getStringFromJson(jsonObject,"metricValue"));
            }
            if (getStringFromJson(jsonObject,"metricId").equals("product_serial")){
                hostData.setSerialNumber(getStringFromJson(jsonObject,"metricValue"));
            }

            if (getStringFromJson(jsonObject,"metricId").equals("cpu_info")){
                String metricValue = getStringFromJson(jsonObject,"metricValue");
                JsonArray jsonArray =  GSON.fromJson(metricValue, JsonArray.class);
                JsonObject firstObject = jsonArray.get(0).getAsJsonObject();
                String version = getStringFromJson(firstObject,"version");
                hostData.setCpuType(version);
            }
            hostData.setBandwidthDownstream(new BigDecimal(inBps).compareTo(BigDecimal.ZERO) < 0
                    ? new BigDecimal(inBps)
                    : new BigDecimal(inBps).multiply(new BigDecimal(1024)).setScale(0, RoundingMode.DOWN));
            hostData.setBandwidthUpstream(new BigDecimal(outBps).compareTo(BigDecimal.ZERO) < 0
                    ? new BigDecimal(outBps)
                    : new BigDecimal(outBps).multiply(new BigDecimal(1024)).setScale(0, RoundingMode.DOWN));
            hostData.setPacketRate(new BigDecimal(inPps));
            //基本信息
            hostData.setName(getStringFromJson(hostBase,"name"));
            hostData.setUuid(getStringFromJson(hostBase,"uuid"));
            hostData.setIp(getStringFromJson(hostBase,"ip"));
            hostData.setStatus(stateConvert(getStringFromJson(hostBase,"status")));
            hostData.setState("Enabled");
            //无集群信息，设置默认集群信息
            hostData.setClusterName(Optional.ofNullable(getStringFromJson(hostBase,"clusterName")).orElse("default cluster"));
            hostData.setClusterUuid(Optional.ofNullable(getStringFromJson(hostBase,"clusterUrn")).orElse("default cluster"));
            //cpu信息
            long totalSizeMCpu = getLongFromJson(hostInfo,"hostTotalCpuQuantity");
            long availableCpu = getLongFromJson(hostInfo,"cpuQuantity");
            BigDecimal totalCpu = BigDecimal.valueOf(totalSizeMCpu);
            long use = totalSizeMCpu - availableCpu;
            BigDecimal useCpu = BigDecimal.valueOf(use);
            BigDecimal cpu = useCpu.divide(totalCpu, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            hostData.setCpuNum(getIntFromJson(hostInfo,"hostTotalCpuQuantity"));
            hostData.setTotalCpuCapacity(totalSizeMCpu*(hostData.getCpuOverPercent().longValue()));

            hostData.setCpuCommitRate(new BigDecimal(use));
            hostData.setAvailableCpuCapacity(availableCpu);
            hostData.setCpuSockets(getIntFromJson(hostInfo,"cpuQuantity"));
            hostData.setCpuUsed(cpu);
            String arch = getStringFromJson(hostInfo,"arch") == null ? "" : getStringFromJson(hostInfo,"arch");
            if (arch.toLowerCase().contains("x86")) {
                hostData.setArchitecture("x86_64");
            } else if (arch.toLowerCase().contains("arm")) {
                hostData.setArchitecture("arm64");
            } else if (arch.isEmpty()) {
                hostData.setArchitecture("-");
            } else {
                hostData.setArchitecture(arch);
            }
            //磁盘信息
            hostData.setDiskUsedBytes(BigDecimal.valueOf((num - used)*1024*1024*1024));
            hostData.setDiskFreeBytes(BigDecimal.valueOf(used*1024*1024*1024));
            hostData.setDiskUsed(new BigDecimal(num).subtract(new BigDecimal(used)).divide(new BigDecimal(num), 10,
                            RoundingMode.HALF_UP).multiply(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP));
            hostData.setTotalDiskCapacity(BigDecimal.valueOf(num*1024*1024*1024));

            String computeResourceStatics = getStringFromJson(hostInfo,"computeResourceStatics");
            //获取内存信息
            JsonObject memoryObj = getJsonObjectFromFsApi(platform.getPlatformUrl() + computeResourceStatics,null,header);
            if (ObjectUtil.isNull(memoryObj)) return null;
            JsonObject memoryInfo = memoryObj.getAsJsonObject("memResource");
            //内存信息
            long allocatedSizeMB = getLongFromJson(memoryInfo,"allocatedSizeMB");
            long totalSizeMB = getLongFromJson(memoryInfo,"totalSizeMB");
            long realtimeUsedSizeMB = getLongFromJson(memoryInfo,"realtimeUsedSizeMB");
            BigDecimal allocated = BigDecimal.valueOf(allocatedSizeMB);
            BigDecimal total = BigDecimal.valueOf(totalSizeMB);
            long totalMemoryCapacity = totalSizeMB * 1024 * 1024;
            long availableMemoryCapacity = realtimeUsedSizeMB * 1024 * 1024;
            BigDecimal percentage = allocated.divide(total, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            hostData.setTotalMemoryCapacity(totalMemoryCapacity);
            hostData.setAvailableMemoryCapacity(availableMemoryCapacity);
            hostData.setMemoryUsed(percentage);

            hostData.setMemoryCommitRate(getBigFromJson(hostInfo,"memQuantityMB"));
            hostData.setTotalVirtualMemory(new BigDecimal(totalMemoryCapacity*(hostData.getMemoryOverPercent().longValue())));
            //平台信息
            hostData.setPlatformId(platform.getPlatformId());
            hostData.setPlatformName(platform.getPlatformName());
            hostData.setRegionId(platform.getRegionId());
            hostData.setTypeName(platform.getTypeCode());
            hostData.setDeleted(0);
        }
        return hostData;
    }

    private String stateConvert(String status) {
        if (status.equals("normal")) {
            return "Connected";
        }
        return "Disconnected";
    }
    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_HOST.code();
    }
}
