package cn.coder.zj.module.collector.collect.basicdata.manager.fusionone;
import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.fusionone.FusionOneApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StateUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static cn.coder.zj.module.collector.util.ApiUtil.*;
import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_FUSION_COMPUTE_VM;

@Slf4j
public class FsOneBasicVmDataImpl extends AbstractBasicData {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.FUSION_ONE.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                List<VmData> list = collectData(platformObj);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_VM.code())
                        .build();
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GsonUtil.GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<VmData> collectData(Object platformObj) {
        Platform platform = (Platform) platformObj;
        String token = platform.getFsOnePlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String url = platform.getPlatformUrl() + FusionOneApiConstant.GET_CLOUD_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        String hostUrl = platform.getPlatformUrl() + FusionOneApiConstant.GET_HOST_LIST.replace("{siteId}", platform.getFsOnePlatform().getSiteId());
        Map<String, String> header = new HashMap<>();
        header.put("Accept","application/json;version=8.0; charset=UTF-8");
        header.put("Host",platform.getFsOnePlatform().getHost());
        header.put("X-Auth-Token", token);
        JsonObject vmdata = FsApiCacheService.getJsonObject(url, null, header);
        JsonObject hostdata = FsApiCacheService.getJsonObject(hostUrl, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("vms");
        JsonArray hostArray = hostdata.getAsJsonArray("hosts");
        Map<String, String> urnUuidMap = StreamSupport.stream(hostArray.spliterator(), false)
                .map(JsonElement::getAsJsonObject)
                .collect(Collectors.toMap(
                        host -> getStringFromJson(host, "urn"),
                        host -> getStringFromJson(host, "uuid"),
                        (existing, replacement) -> existing
                ));

        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();
        List<VmData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(vmArray)) {
            for (JsonElement jsonElement : vmArray) {
                try {
                    VmData vmData = collectVmInfo(platform, jsonElement,header,urnUuidMap);
                    if (vmData != null) {
                        dataList.add(vmData);
                    }
                } catch (Exception e) {
                    log.error("处理Fusionone云盘数据异常, hostMap: {}, error: {}", "", e.getMessage());
                }
            }
        }
        return dataList;
    }

    private VmData collectVmInfo(Platform platform, JsonElement jsonElement, Map<String, String> headers,Map<String, String> urnUuidMap) {
        VmData vmData = new VmData();
        String platformUrl = platform.getPlatformUrl();
        String siteId = platform.getFsOnePlatform().getSiteId();

        // 基础信息获取
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        JsonObject cloudInfo = FsApiCacheService.getJsonObject(platformUrl + getStringFromJson(cloud, "uri"), null, headers);
        JsonObject vmConfig = cloudInfo.getAsJsonObject("vmConfig");
        JsonObject osOptions = cloudInfo.getAsJsonObject("osOptions");
        JsonObject properties = vmConfig.getAsJsonObject("properties");
        JsonObject haConfig = cloudInfo.getAsJsonObject("haConfig");

        // 基本信息设置
        vmData.setGuestOsType(getStringFromJson(osOptions, "guestOSName"));
        vmData.setClusterName(getStringFromJson(cloud, "clusterName"));
        vmData.setClusterUuid(getStringFromJson(cloud, "clusterUrn"));
        vmData.setHardwareUuid(urnUuidMap.get(getStringFromJson(cloud, "hostUrn")));
        vmData.setDeleted(getBooleanFromJson(cloud, "isTemplate") ? 1 : 0);
        vmData.setUuid(getStringFromJson(cloud, "uuid"));
        vmData.setName(getStringFromJson(cloud, "name", "-"));
        vmData.setVipIp("");

        // 网络信息设置
        JsonArray nics = vmConfig.getAsJsonArray("nics");
        if (nics != null && !nics.isEmpty()) {
            JsonObject nic = nics.get(0).getAsJsonObject();
            vmData.setIp(getStringFromJson(nic, "ip"));
            vmData.setMac(getStringFromJson(nic, "mac"));
        } else {
            vmData.setMac("");
        }

        // 硬件和状态信息设置
        String arch = getStringFromJson(cloud, "arch").toLowerCase();
        vmData.setArchitecture(arch.contains("x86") ? "x86_64" :
                arch.contains("arm") ? "arm64" :
                        arch.isEmpty() ? "-" : arch);

        String state = getStringFromJson(cloud, "status", "-");
        vmData.setState(StateUtil.stateConvert(state));
        vmData.setPowerState(StateUtil.powerStateConvert(state));
        vmData.setZoneName(platform.getFsOnePlatform().getSiteName());
        vmData.setHardwareName(getStringFromJson(cloudInfo, "hostName", ""));

        // HA和BIOS配置设置
        String hostFaultPolicy = getStringFromJson(haConfig, "hostFaultPolicy");
        vmData.setAutoInitType(StrUtil.isNotEmpty(hostFaultPolicy) && hostFaultPolicy.equals("3") ? "NeverStop" : "None");
        vmData.setGuideMode(getStringFromJson(properties, "bootFirmware").equals("BIOS") ? "Legacy" : "UEFI");
        vmData.setToolsInstalled(stateConvert(getStringFromJson(cloud, "toolInstallStatus")));
        vmData.setToolsRunStatus(stateConvert(getStringFromJson(cloud, "toolInstallStatus")).equals("false")?"stop":"run");


        // 磁盘信息处理
        Long[] sizes = {0L, 0L};
        BigDecimal diskUsage = BigDecimal.ZERO;
        JsonArray disks = vmConfig.getAsJsonArray("disks");
        if (disks != null && disks.size() > 0) {
            for (JsonElement diskElement : disks) {
                JsonObject disk = diskElement.getAsJsonObject();
                String volumeUrn = getStringFromJson(disk, "volumeUrn");

                String volumeUrl = platformUrl + FusionOneApiConstant.GET_VOLUMES.replace("{siteId}", siteId);
                JsonObject volumeResponse = FsApiCacheService.getJsonObject(
                        volumeUrl,
                        Map.of(
                                "volUrns", volumeUrn,
                                "limit", "100",
                                "refreshflag", "true",
                                "offset", "0"
                        ),
                        headers
                );
                JsonObject volume = volumeResponse.getAsJsonArray("volumes").get(0).getAsJsonObject();
                Long capacity = getLongFromJsonDouble(volume, "quantityGB") * 1024 * 1024 * 1024;
                Long allocation = getLongFromJsonDouble(volume, "userUsedSize") * 1024 * 1024;
                sizes[0] = capacity;
                sizes[1] = allocation;

                if (capacity != null && capacity > 0 && allocation != null) {
                    diskUsage = BigDecimal.valueOf(allocation).divide(BigDecimal.valueOf(capacity), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                }
                break;
            }
        }

        // CPU和内存信息设置
        vmData.setCpuNum(getIntFromJson(vmConfig.getAsJsonObject("cpu"), "quantity"));
        vmData.setMemorySize(getLongFromJson(vmConfig.getAsJsonObject("memory"), "quantityMB") * 1024 * 1024);

        // 性能指标获取
        JsonObject requestBody = new JsonObject();
        JsonArray metricArray = new JsonArray();
        metricArray.add("cpu_usage");
        metricArray.add("mem_usage");
        metricArray.add("disk_usage");
        metricArray.add("nic_byte_in");
        metricArray.add("nic_byte_out");
        requestBody.add("metricId", metricArray);
        requestBody.addProperty("urn", getStringFromJson(cloudInfo, "urn"));

        JsonArray requestArray = new JsonArray();
        requestArray.add(requestBody);

        JsonArray valueArray = getJsonArrayFromFsApi(
                platformUrl + FusionOneApiConstant.GET_REAL_TIME_DATA.replace("{siteId}", siteId),
                headers,
                requestArray
        ).get(0).getAsJsonObject().getAsJsonArray("value");

        BigDecimal[] metrics = {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        for (JsonElement element : valueArray) {
            JsonObject value = element.getAsJsonObject();
            String metricValue = getStringFromJson(value, "metricValue");
            if (StrUtil.isNotEmpty(metricValue)) {
                int index = switch (getStringFromJson(value, "metricId")) {
                    case "cpu_usage" -> 0;
                    case "mem_usage" -> 1;
                    case "disk_usage" -> 2;
                    case "nic_byte_in" -> 3;
                    case "nic_byte_out" -> 4;
                    default -> -1;
                };
                if (index >= 0) {
                    metrics[index] = index <= 2 ?
                            metrics[index].add(getBigFromJson(value, "metricValue")) :
                            getBigFromJson(value, "metricValue");
                }
            }
        }

        // 设置所有指标
        vmData.setMemoryUsed(metrics[1]);
        vmData.setCpuUsed(metrics[0]);
        if(vmData.getToolsInstalled().equals("true") && vmData.getToolsRunStatus().equals("run")){
            vmData.setDiskUsed(metrics[2]);
        }else {
            vmData.setDiskUsed(diskUsage);
        }

        String createTime = getStringFromJson(cloudInfo, "createTime");
        try {
            if (StrUtil.isNumeric(createTime)) {
                vmData.setVCreateDate(DateUtil.date(Long.parseLong(createTime) * 1000L));
            } else {
                vmData.setVCreateDate(DateUtil.parseDateTime(createTime));
            }
        } catch (Exception e) {
            vmData.setVCreateDate(DateUtil.date());
        }
        vmData.setRegionId(platform.getRegionId());
        vmData.setPlatformId(platform.getPlatformId());
        vmData.setPlatformName(platform.getPlatformName());
        vmData.setType(convertVmType(getIntFromJson(cloudInfo, "vmType")));
        vmData.setTypeName(platform.getTypeCode());
        vmData.setTotalDiskCapacity(new BigDecimal(sizes[0]));
        vmData.setDiskUsedBytes(new BigDecimal(sizes[1]));
        vmData.setDiskFreeBytes(new BigDecimal(sizes[0] - sizes[1]));
        vmData.setCloudSize(new BigDecimal(sizes[0]));
        vmData.setActualSize(new BigDecimal(sizes[1]));
        vmData.setNetworkInBytes(metrics[3]);
        vmData.setNetworkOutBytes(metrics[4]);

        return vmData;
    }

    private String stateConvert(String status) {
        if (status.equals("empty")) {
            return "false";
        }
        return "true";
    }

    private String convertVmType(Integer vmType) {
        if (vmType == null) return "未知类型";
        switch (vmType) {
            case 0: return "普通虚拟机";
            case 1: return "容灾虚拟机";
            case 2: return "占位虚拟机";
            default: return "未知类型";
        }
    }


    @Override
    public String supportProtocol() {
        return BASIC_FUSION_COMPUTE_VM.code();
    }
}
