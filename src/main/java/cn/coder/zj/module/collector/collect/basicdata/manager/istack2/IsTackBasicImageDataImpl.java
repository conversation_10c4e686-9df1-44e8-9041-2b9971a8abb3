package cn.coder.zj.module.collector.collect.basicdata.manager.istack2;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.TimeUtils;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_IMAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_IS_TACK_IMAGE;

@Slf4j
public class IsTackBasicImageDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK2.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformInfo : platformList) {
            Platform platform = (Platform) platformInfo;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(TimeUtils.withExecutionTime(
                    String.format("プラットフォーム[%s]のデータ処理", platform.getPlatformName()), IsTackBasicImageDataImpl.class.getSimpleName(), startTime, () -> {
                        List<ImageData> dataList = new ArrayList<>();

                        collectData(platform, dataList);
                        if (!dataList.isEmpty()) {
                            BasicCollectData build = BasicCollectData.builder().basicDataMap(dataList)
                                    .metricsName(BASIC_IMAGE.code())
                                    .build();
                            message.setType(ClusterMsg.MessageType.BASIC);
                            message.setData(GSON.toJson(build));
                            message.setTime(System.currentTimeMillis());
                            sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        }
                    }));
        }

    }

    private void collectData(Platform platform, List<ImageData> dataList) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_IMAGE_LIST, param, null)) {
            JsonArray asJsonArray = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray();
            if (asJsonArray.isEmpty()) {
                log.warn("平台 {} L2网卡数据为空", platform.getPlatformName());
                return;
            }
            asJsonArray.forEach(jsonElement -> {
                try {
                    JsonObject resultObj = jsonElement.getAsJsonObject();
                    ImageData imageData = new ImageData();


                    imageData.setPlatformId(platform.getPlatformId());
                    imageData.setPlatformName(platform.getPlatformName());
                    imageData.setUuid(resultObj.get("id").getAsString());
                    imageData.setName(resultObj.get("name").getAsString());
                    imageData.setStatus(resultObj.get("status").getAsString().equals("active") ? "Enabled" : "Disabled");
                    imageData.setFormat("qcow2");
                    boolean arch = resultObj.get("arch").isJsonNull();
                    if (arch) {
                        imageData.setCpuArch("x86_64");
                    } else {
                        imageData.setCpuArch(resultObj.get("arch").getAsString());
                    }
                    ;
                    imageData.setOsType(resultObj.get("os_type").getAsString());
                    // 转换大小为字节（GB to Bytes）
                    long size = 0L;
                    if (resultObj.has("size") && !resultObj.get("size").isJsonNull()) {
                        size = resultObj.get("size").getAsLong() * 1024L * 1024L * 1024L;
                    }
                    imageData.setSize(size);

                    imageData.setImageType("RootVolumeTemplate");
                    String visibility = resultObj.get("visibility").getAsString();
                    if ("shared".equals(visibility) || "public".equals(visibility)) {
                        imageData.setSharingScope("共享");
                    } else {
                        imageData.setSharingScope("不共享");
                    }
                    Date createDate = null;
                    if (resultObj.has("created_at") && !resultObj.get("created_at").isJsonNull()) {
                        String dateStr = resultObj.get("created_at").getAsString();
                        try {
                            ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateStr, DateTimeFormatter.ISO_DATE_TIME);
                            createDate = Date.from(zonedDateTime.toInstant());
                        } catch (Exception e) {
                            log.error("日期解析失败: {}", dateStr, e);
                        }
                    }
                    imageData.setVCreateDate(createDate);
                    imageData.setVUpdateDate(createDate);
                    imageData.setTag("");
                    imageData.setOsLanguage("");
                    imageData.setMinMemory(null);
                    BigDecimal minDisk = resultObj.get("min_disk").getAsBigDecimal() != null ?
                            BigDecimal.valueOf(resultObj.get("min_disk").getAsLong() * 1024 * 1024 * 1024) : null;
                    imageData.setMinDisk(minDisk);
                    imageData.setDiskDriver("");
                    imageData.setNetworkDriver("");
                    imageData.setBootMode("");
                    imageData.setRemoteProtocol("");
                    imageData.setApplicationPlatform(resultObj.get("os_type").getAsString());
                    imageData.setDeleted(0);
                    dataList.add(imageData);
                } catch (Exception e) {
                    log.error("平台 {} L2网卡数据解析异常：{}", platform.getPlatformName(), e.getMessage());
                }
            });
        } catch (IOException e) {
            log.error("平台 {} L2网卡数据获取失败：{}", platform.getPlatformName(), e.getMessage());
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_IS_TACK_IMAGE.code();
    }
}
