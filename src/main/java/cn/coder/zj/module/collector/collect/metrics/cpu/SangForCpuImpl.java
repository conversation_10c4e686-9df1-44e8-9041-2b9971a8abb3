package cn.coder.zj.module.collector.collect.metrics.cpu;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.ApiCacheService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.CpuType.PROTOCOL_SXF_CPU;
import static cn.coder.zj.module.collector.enums.MetricNameType.CPU_USED_TASK;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;

@Slf4j
public class SangForCpuImpl extends AbstractMetrics {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();
                Platform platform = (Platform) o;
                // 获取所有VM的UUID并组合成标签
                List<MonitorInfo> vmUuids = platform.getSxfPlatform().getVmUuids();
                List<MonitorInfo> hostUuids = platform.getSxfPlatform().getHostUuids();
                metricDataList.addAll(vmUUid(vmUuids, platform));
                metricDataList.addAll(hostUUid(hostUuids, platform));
                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.CPU_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                log.info("collectVmData name: {}", Thread.currentThread().getName());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect collectData data end, cost {} seconds", endTimeFormatted);
            });
        }
    }

    public List<MetricData> vmUUid(List<MonitorInfo> vmUuids, Platform platform) {
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie, "CSRFPreventionToken", token);
        String platformUrl = platform.getPlatformUrl();
        List<MetricData> metricDataList = new ArrayList<>();
        vmUuids.forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(CPU_USED_TASK.code());
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("vm");
            String url = platformUrl + SangForApiConstant.GET_VM_DETAIL.replace("{vmid}", info.getUuid());
            JsonObject obj = ApiCacheService.getJsonObject(url, null, headers);
            if (obj.size() > 0 && obj.has("cpu_sheet")) {
                MetricData result = dealInfo(obj.getAsJsonObject("cpu_sheet").getAsJsonArray("hour"), "CPU使用率", metricData);
                if (result != null) {
                    metricDataList.add(result);
                }
            }
        });

        return metricDataList;
    }

    public List<MetricData> hostUUid(List<MonitorInfo> hostUuids, Platform platform) {
        String token = platform.getSxfPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        String cookie = platform.getSxfPlatform().getLoginAuthCookie();
        Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie, "CSRFPreventionToken", token);
        String platformUrl = platform.getPlatformUrl();
        List<MetricData> metricDataList = new ArrayList<>();

        hostUuids.forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(CPU_USED_TASK.code());
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("host");

            JsonArray obj = getJsonArrayFromApi(platformUrl + SangForApiConstant.GET_HOST_DETAIL.replace("{nodeId}", info.getUuid()) + "cpu", null, headers);
            if (!obj.isEmpty()) {
                MetricData result = dealInfo(obj, "CPU使用率", metricData);
                if (result != null) {
                    metricDataList.add(result);
                }
            }
        });
        return metricDataList;
    }


    private MetricData dealInfo(JsonArray data, String label, MetricData metricData) {
        if (data == null || data.isEmpty()) {
            return null;
        }

        // 处理VM的数据格式
        if (data.get(0).getAsJsonObject().has("name")) {
            JsonArray valArr = new JsonArray();
            Long time = 0L;
            Long interval = 0L;

            for (JsonElement element : data) {
                JsonObject item = element.getAsJsonObject();
                String name = item.get("name").getAsString();

                if (name.contains(label)) {
                    valArr = item.get("data").getAsJsonArray();
                } else if (name.contains("time")) {
                    JsonObject timeData = item.get("data").getAsJsonObject();
                    time = timeData.get("start").getAsLong();
                    interval = timeData.get("interval").getAsLong();
                }
            }

            if (!valArr.isEmpty()) {
                JsonObject lastData = new JsonObject();
                lastData.addProperty("val", valArr.get(valArr.size() - 1).getAsDouble());
                lastData.addProperty("time", time + interval * (valArr.size() - 1));

                metricData.setTimestamps(Arrays.asList(lastData.get("time").getAsLong()));
                metricData.setValues(Arrays.asList(lastData.get("val").getAsDouble()));
                return metricData;
            }
        }
        // 处理Host的数据格式
        else {
            for (JsonElement element : data) {
                JsonObject item = element.getAsJsonObject();
                if (item.get("name").getAsString().contains(label)) {
                    JsonArray cpuData = item.get("data").getAsJsonArray();
                    if (cpuData != null && !cpuData.isEmpty()) {
                        double cpuValue = cpuData.get(cpuData.size() - 1).getAsDouble();
                        metricData.setTimestamps(Arrays.asList(System.currentTimeMillis()));
                        metricData.setValues(Arrays.asList(cpuValue));
                        return metricData;
                    }
                    break;
                }
            }
        }

        return null;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_SXF_CPU.code();
    }
}
