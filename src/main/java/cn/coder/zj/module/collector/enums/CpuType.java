package cn.coder.zj.module.collector.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public enum CpuType {

    PROTOCOL_ZS_TACK_CPU("zs_tack_cpu", "zstack cpu"),
    PROTOCOL_IS_TACK_CPU("is_tack_cpu", "is tack cpu"),
    PROTOCOL_VMWARE_CPU("vmware_cpu", "vmware cpu"),
    PROTOCOL_SXF_CPU("sxf_cpu", "sxf cpu"),
    PROTOCOL_WIN_HONG_CPU("win_hong_cpu", "win hong cpu"),
    PROTOCOL_FUSION_COMPUTE_CPU("fusion_compute_cpu", "fusionCompute cpu"),
    PROTOCOL_IN_SPUR_CPU("in_spur_cpu", "inspur cpu");

    private final String code;

    private final String desc;


    public static CpuType fromCode(String code) {
        for (CpuType typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String code() {
        return this.code;
    }
    public static List<String> getAllCpuTypeCodes() {
        List<String> codes = new ArrayList<>();
        for (CpuType cpuType : CpuType.values()) {
            codes.add(cpuType.code());
        }
        return codes;
    }
}
