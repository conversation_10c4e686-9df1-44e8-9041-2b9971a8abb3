package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.ServiceInstance;
import com.vmware.vim25.mo.VirtualMachine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.List;

import static cn.coder.zj.module.collector.service.vmware.VmComputerResourceSummary.getVmList;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_VM_VIC;
@Slf4j
public class VmwareBasicVmNicDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute( () -> {
                try {
                    List<VmNicData> vmNicDataList = handleVmNicData(platform);
                    if (!CollUtil.isEmpty(vmNicDataList)) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(vmNicDataList)
                                .metricsName(BASIC_VM_VIC.code())
                                .build();

                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
                } catch (Exception e) {
                    log.error("平台 [{}] 宿主机网络数据收集失败", platform.getPlatformName(), e);
                    throw new RuntimeException(e);
                }
            });
        }
    }

    public List<VmNicData> handleVmNicData(Platform platform) throws Exception {
        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();
        List<VirtualMachine> virtualMachines = getVmList(serviceInstance);
        if (virtualMachines != null) {
            return processVmNic(virtualMachines, platform);
        }else {
            return new ArrayList<>();
        }
    }


    public List<VmNicData> processVmNic(List<VirtualMachine> virtualMachines, Platform platform) {
        List<VmNicData> vmNicDataList = new ArrayList<>();
        for (VirtualMachine vm : virtualMachines) {
            List<VmNicData> vmNicDataS = extractVmNics(vm,platform);
            vmNicDataList.addAll(vmNicDataS);
        }
        return vmNicDataList;
    }

    public List<VmNicData> extractVmNics(VirtualMachine virtualMachine,Platform platform) {
        VirtualMachineSummary virtualMachineSummary = virtualMachine.getSummary();
        String hostUuid = virtualMachine.getMOR().getVal() + "-" + virtualMachineSummary.getConfig().getUuid();
        VirtualDevice[] devices = virtualMachine.getConfig().getHardware().getDevice();

        List<VmNicData> vmNicDataList = new ArrayList<>();

        if (devices != null) {
            for (VirtualDevice virtualDevice : devices) {
                if (virtualDevice instanceof VirtualEthernetCard ethernetCard) {
                    VirtualDeviceBackingInfo backing = ethernetCard.getBacking();
                    VmNicData vmNicData = new VmNicData();
                    vmNicData.setHostUuid(hostUuid);

                    // 根据不同的网卡类型处理
                    if (backing instanceof VirtualEthernetCardDistributedVirtualPortBackingInfo dvsBacking) {
                        // 分布式虚拟交换机
                        vmNicData.setUuid(dvsBacking.getPort().getPortgroupKey() + "_" + dvsBacking.getPort().getPortKey() + "_" + hostUuid);
                        vmNicData.setNetworkUuid(dvsBacking.getPort().getPortgroupKey());
                    } else if (backing instanceof VirtualEthernetCardNetworkBackingInfo standardBacking) {
                        // 标准虚拟交换机
                        vmNicData.setUuid(standardBacking.getDeviceName() + "_" + ethernetCard.getKey() + "_" + hostUuid);
                        vmNicData.setNetworkUuid(standardBacking.getDeviceName());
                    } else {
                        // 跳过其他类型的网卡
                        log.warn("未知的网卡类型: {}", backing.getClass().getName());
                        continue;
                    }

                    vmNicData.setIp("");
                    vmNicData.setIp6("");
                    vmNicData.setPlatformId(platform.getPlatformId());
                    vmNicData.setPlatformName(platform.getPlatformName());
                    vmNicData.setMac(ethernetCard.getMacAddress());
                    vmNicData.setDriver(virtualDevice.getClass().getSimpleName().toLowerCase().replace("virtual", "").replace("virtualmachine", ""));
                    vmNicData.setInClassicNetwork((byte) 0);
                    vmNicDataList.add(vmNicData);
                }
            }
        }
        return vmNicDataList;
    }
    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_VM_VIC.code();
    }
}
