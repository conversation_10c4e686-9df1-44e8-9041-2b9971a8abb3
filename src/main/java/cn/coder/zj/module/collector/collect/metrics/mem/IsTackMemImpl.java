package cn.coder.zj.module.collector.collect.metrics.mem;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static cn.coder.zj.module.collector.enums.MemType.PROTOCOL_IS_TACK_MEM;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class IsTackMemImpl extends AbstractMetrics {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            Platform platform = (Platform) o;
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();

                // 获取所有VM的UUID并组合成标签
                List<MonitorInfo> vmUuids = platform.getIsTackPlatform().getVmUuids();
                List<MonitorInfo> hostUuids = platform.getIsTackPlatform().getHostUuids();

                memUsedVm(vmUuids, hostUuids, platform, metricDataList);
                memFreeHost(vmUuids, hostUuids, platform, metricDataList);
                memUsage(vmUuids, hostUuids, platform, metricDataList);
                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.MEM_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);

            });
        }
    }

    private List<MetricData> memUsage(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmUsageData(vmUuids, platform, MEM_USAGE_TASK.code(), metricDataList);
        getMetricHostUsageData(platform, MEM_USAGE_TASK.code(), metricDataList);
        return metricDataList;
    }

    private List<MetricData> memFreeHost(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, platform, MEM_FREE_TASK.code(), "mem_util", metricDataList);
        getMetricHostData(hostUuids, platform, MEM_FREE_TASK.code(), "mem_util", metricDataList);
        return metricDataList;
    }

    private List<MetricData> memUsedVm(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, platform, MEM_USED_TASK.code(), "mem_util", metricDataList);
        getMetricHostData(hostUuids, platform, MEM_USED_TASK.code(), "mem_util", metricDataList);
        return metricDataList;
    }

    private List<MetricData> getMetricVmUsageData(List<MonitorInfo> vmUuids, Platform platform, String code, List<MetricData> metricDataList) {
        vmUuids.stream().forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(code);
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("vm");
            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            param.put("uuid", info.getUuid());
            param.put("item_names", "mem_util");
            param.put("device_type", "vm");
            param.put("from", String.valueOf(DateUtil.currentSeconds() * 1000));
            param.put("to", String.valueOf(DateUtil.currentSeconds() * 1000));
            List<Long> timestamps = new ArrayList<>();
            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_REALTIME, param, null)) {
                GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray().forEach(jsonElement -> {
                    JsonArray asJsonArray = jsonElement.getAsJsonObject().getAsJsonArray("values");
                    if (!asJsonArray.isEmpty()) {
                        JsonObject asJsonObject = asJsonArray.get(0).getAsJsonObject();
                        String time = asJsonObject.get("sampling_time").getAsString();
                        BigDecimal sampling_value = asJsonObject.get("sampling_value").getAsBigDecimal().setScale(2, RoundingMode.HALF_UP);
                        timestamps.add(Long.valueOf(time));
                        metricData.setTimestamps(timestamps);
                        metricData.setValues(Collections.singletonList(sampling_value.doubleValue()));
                        metricDataList.add(metricData);
                    }
                });
            } catch (IOException e) {
                log.error("error collecting IsTackCpuImpl basic data: {}", e.getMessage());
            }
        });
        return metricDataList;
    }

    private List<MetricData> getMetricHostUsageData(Platform platform, String code, List<MetricData> metricDataList) {
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_HARDWARE, param, null)) {
            if (response == null || !response.isSuccessful() || response.body() == null) {
                log.warn("平台 {} 请求失败或响应为空", platform.getPlatformName());
                return metricDataList;
            }

            JsonObject jsonObject = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
            if (!jsonObject.has("results") || jsonObject.get("results").isJsonNull()) {
                log.warn("平台 {} 没有数据返回", platform.getPlatformName());
                return metricDataList;
            }

            JsonArray results = jsonObject.get("results").getAsJsonArray();
            if (results.isEmpty()) {
                log.warn("平台 {} 返回空数据集", platform.getPlatformName());
                return metricDataList;
            }
            results.forEach(result -> {
                try {
                    JsonObject resultObj = result.getAsJsonObject();
                    MetricData metricData = new MetricData();
                    metricData.setPlatformId(platform.getPlatformId());
                    metricData.setMetricName(code);
                    metricData.setResourceId(resultObj.get("uuid").getAsString());
                    metricData.setResourceName(resultObj.get("name").getAsString());
                    metricData.setType("host");

                    if (!resultObj.has("memory_used_rate") || resultObj.get("memory_used_rate").isJsonNull()) {
                        log.warn("平台 {} 主机 {} 内存使用率数据为空", platform.getPlatformName(), resultObj.get("uuid").getAsString());
                        return;
                    }

                    BigDecimal memoryUsedRate = resultObj.get("memory_used_rate").getAsBigDecimal().multiply(new BigDecimal(100));
                    metricData.setTimestamps(Collections.singletonList(System.currentTimeMillis()));
                    // 将 BigDecimal 转换为 Double 并封装为 List
                    metricData.setValues(Collections.singletonList(memoryUsedRate.doubleValue()));
                    metricDataList.add(metricData);
                } catch (Exception e) {
                    log.error("处理主机数据时发生错误: {}", e.getMessage());
                }
            });
        } catch (IOException e) {
            log.error("获取主机基础数据时发生错误: {}", e.getMessage());
        }
        return metricDataList;
    }


    private List<MetricData> getMetricHostData(List<MonitorInfo> hostUuids, Platform platform, String code, String labelName, List<MetricData> metricDataList) {
        hostUuids.stream().forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(code);
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("host");

            Map<String, String> hard = new HashMap<>();
            hard.put("os_id", platform.getPassword());
            hard.put("ct_user_id", platform.getUsername());
            hard.put("uuid", info.getUuid());
            long memorySize = 0;
            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_HARDWARE, hard, null)) {
                JsonObject jsonObject = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
                if (!jsonObject.has("results") || jsonObject.get("results").isJsonNull()) {
                    log.warn("平台 {} 主机 {} 没有数据返回", platform.getPlatformName(), info.getUuid());
                    return;
                }
                JsonArray results = jsonObject.get("results").getAsJsonArray();
                if (results.isEmpty()) {
                    log.warn("平台 {} 主机 {} 返回空数据集", platform.getPlatformName(), info.getUuid());
                    return;
                }
                // 直接获取第一条数据
                memorySize = results.get(0).getAsJsonObject().get("memory_size").getAsLong();
            } catch (IOException e) {
                log.error("error collecting host basic data: {}", e.getMessage());
            }


            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            param.put("uuid", info.getUuid());
            param.put("item_name", labelName);
            param.put("from", String.valueOf(DateUtil.currentSeconds() * 1000));
            param.put("to", String.valueOf(DateUtil.currentSeconds() * 1000));

            List<Long> timestamps = new ArrayList<>();
            List<Double> values = new ArrayList<>();

            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_HOST_REALTIME, param, null)) {
                long finalMemorySize = memorySize;
                GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray().forEach(jsonElement -> {

                    String time = jsonElement.getAsJsonObject().get("sampling_time").getAsString();
                    BigDecimal sampling_value = jsonElement.getAsJsonObject().get("sampling_value").getAsBigDecimal();
                    // 计算空闲
                    if ("MemoryFreeBytes".equals(code)) {
                        //内存空闲情况
                        BigDecimal freePercentage = new BigDecimal(100).subtract(sampling_value);
                        double memValue = freePercentage.multiply(new BigDecimal(finalMemorySize))
                                .setScale(2, RoundingMode.HALF_UP)
                                .doubleValue();
                        memValue = memValue * 1024;
                        values.add(memValue);
                    } else {
                        // 使用
                        double memValue = sampling_value.multiply(new BigDecimal(finalMemorySize))
                                .setScale(2, RoundingMode.HALF_UP)
                                .doubleValue();
                        memValue = memValue * 1024;
                        values.add(memValue);
                    }
                    timestamps.add(Long.valueOf(time));
                    metricData.setTimestamps(timestamps);
                    metricData.setValues(values);
                    metricDataList.add(metricData);
                });
            } catch (IOException e) {
                log.error("error collecting host basic data: {}", e.getMessage());
            }
        });
        return metricDataList;
    }

    private List<MetricData> getMetricVmData(List<MonitorInfo> vmUuids, Platform platform, String code, String labelName, List<MetricData> metricDataList) {
        vmUuids.stream().forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(code);
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("vm");

            Map<String, String> vm = new HashMap<>();
            vm.put("os_id", platform.getPassword());
            vm.put("ct_user_id", platform.getUsername());
            vm.put("instance_uuid", info.getUuid());

            long memorySize = 0;
            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_DETAIL, vm, null)) {
                if (response == null || !response.isSuccessful() || response.body() == null) {
                    log.warn("平台 {} 虚拟机 {} 请求失败或响应为空", platform.getPlatformName(), info.getUuid());
                    return;
                }

                String responseBody = response.body().string();
                if (responseBody.isEmpty()) {
                    log.warn("平台 {} 虚拟机 {} 响应体为空", platform.getPlatformName(), info.getUuid());
                    return;
                }

                JsonElement jsonElement = GSON.fromJson(responseBody, JsonElement.class);
                if (jsonElement == null || !jsonElement.isJsonObject()) {
                    log.warn("平台 {} 虚拟机 {} 响应数据格式错误", platform.getPlatformName(), info.getUuid());
                    return;
                }

                JsonObject jsonObject = jsonElement.getAsJsonObject();
                if (!jsonObject.has("results") || jsonObject.get("results").isJsonNull()) {
                    log.warn("平台 {} 主机 {} jsonObject没有数据返回", platform.getPlatformName(), info.getUuid());
                    return;
                }
                JsonArray results = jsonObject.get("results").getAsJsonArray();
                if (results.isEmpty()) {
                    log.warn("平台 {} 主机 {} results返回空数据集", platform.getPlatformName(), info.getUuid());
                    return;
                }
                // 获取虚拟机内存大小(GB)并转换为字节(Bytes)
                long ramGB = results.get(0).getAsJsonObject()
                        .get("flavor").getAsJsonObject()
                        .get("ram").getAsLong();
                // 将GB转换为Bytes: GB * 1024(MB) * 1024(KB) * 1024(B)
                BigDecimal memoryInBytes = BigDecimal.valueOf(ramGB)
                        .multiply(BigDecimal.valueOf(1024 * 1024 * 1024))
                        .setScale(0, RoundingMode.HALF_UP);
                memorySize = memoryInBytes.longValue();
            } catch (IOException e) {
                log.error("error collecting host basic data: {}", e.getMessage());
            }

            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            param.put("uuid", info.getUuid());
            param.put("item_names", labelName);
            param.put("device_type", "vm");
            param.put("from", String.valueOf(DateUtil.currentSeconds() * 1000));
            param.put("to", String.valueOf(DateUtil.currentSeconds() * 1000));

            List<Long> timestamps = new ArrayList<>();
            List<Double> values = new ArrayList<>();

            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_REALTIME, param, null)) {
                long finalMemorySize = memorySize;
                GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray().forEach(jsonElement -> {
                    JsonArray asJsonArray = jsonElement.getAsJsonObject().getAsJsonArray("values");

                    if (!asJsonArray.isEmpty()) {
                        JsonObject asJsonObject = asJsonArray.get(0).getAsJsonObject();
                        String time = asJsonObject.get("sampling_time").getAsString();
                        BigDecimal sampling_value = asJsonObject.get("sampling_value").getAsBigDecimal();
                        // 计算空闲
                        if ("MemoryFreeBytes".equals(code)) {
                            //内存空闲情况
                            //内存空闲情况：先计算空闲百分比，再转换为实际字节数
                            BigDecimal freePercentage = new BigDecimal(100).subtract(sampling_value)
                                    .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);

                            double memValue = freePercentage.multiply(new BigDecimal(finalMemorySize))
                                    .setScale(2, RoundingMode.HALF_UP)
                                    .doubleValue();
                            values.add(memValue);
                        } else {
                            // 使用
                            BigDecimal percentage = sampling_value.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                            double memValue = percentage.multiply(new BigDecimal(finalMemorySize))
                                    .setScale(2, RoundingMode.HALF_UP)
                                    .doubleValue();
                            values.add(memValue);
                        }
                        timestamps.add(Long.valueOf(time));
                        metricData.setTimestamps(timestamps);
                        metricData.setValues(values);
                        metricDataList.add(metricData);
                    }
                });
            } catch (IOException e) {
                log.error("error collecting IsTackCpuImpl basic data: {}", e.getMessage());
            }
        });
        return metricDataList;
    }


    @Override
    public String supportProtocol() {
        return PROTOCOL_IS_TACK_MEM.code();
    }
}
