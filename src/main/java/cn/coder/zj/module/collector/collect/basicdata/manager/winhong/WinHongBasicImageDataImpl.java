package cn.coder.zj.module.collector.collect.basicdata.manager.winhong;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.winhong.WinHongApiConstant;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StateUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static cn.coder.zj.module.collector.util.CommonUtil.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.*;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_WIN_HONG_IMAGE;

@Slf4j
public class WinHongBasicImageDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.WIN_HONG.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) platformObj;
                String token = platform.getWinHongPlatform().getToken();
                if (token == null) {
                    log.error("平台 {} token为空", platform.getPlatformName());
                    return;
                }
                List<ImageData> list = collectData(platform);
                BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                        .metricsName(BASIC_IMAGE.code())
                        .build();
                message.setType(ClusterMsg.MessageType.BASIC);
                message.setData(GSON.toJson(build));
                message.setTime(System.currentTimeMillis());
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
            });
        }
    }

    private List<ImageData> collectData(Platform platform) {
        String token = platform.getWinHongPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        Map<String, String> headers = Collections.singletonMap("Cookie", "SESSION=" + token);
        JsonArray hostArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_IMAGES, null, headers);
        if (ObjectUtil.isNull(hostArray)) return new ArrayList<>();
        List<ImageData> dataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(hostArray)) {
            for (JsonElement jsonElement : hostArray) {
                try {
                    ImageData imageData = collectImageInfo(platform, jsonElement,headers);
                    if (imageData != null) {
                        dataList.add(imageData);
                    }
                } catch (Exception e) {
                    log.error("处理主机数据异常, hostMap: {}, error: {}", jsonElement, e.getMessage());
                }
            }
        }
        return dataList;
    }

    private ImageData collectImageInfo(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        JsonObject jsonObject = GSON.toJsonTree(jsonElement).getAsJsonObject();
        ImageData imageInfo = new ImageData();

        // 设置基本信息
        imageInfo.setUuid(getStringFromJson(jsonObject,"id",""));
        imageInfo.setName(getStringFromJson(jsonObject,"name",""));
        imageInfo.setStatus("Enabled");
        imageInfo.setFormat("qcow2");

        //cpu架构 1.32位 2.64位 3.aarch64 4.mips64el
        String cpuArch = getStringFromJson(jsonObject,"cpuArch","");
        switch (cpuArch) {
            case "1": imageInfo.setCpuArch("x86_32"); break;
            case "2": imageInfo.setCpuArch("x86_64"); break;
            case "3": imageInfo.setCpuArch("aarch64"); break;
            case "4": imageInfo.setCpuArch("mips64el"); break;
        }

        //系统类型 0.其他 1.linux 2.windows
        String osType = getStringFromJson(jsonObject,"osType","");
        switch (osType) {
            case "0":
                imageInfo.setOsType("Other");
                imageInfo.setApplicationPlatform("Other");
                break;
            case "1":
                imageInfo.setOsType("Linux");
                imageInfo.setApplicationPlatform("Linux");
                break;
            case "2":
                imageInfo.setOsType("Windows");
                imageInfo.setApplicationPlatform("Windows");
                break;
        }

        // 设置大小和类型信息
        imageInfo.setSize(getLongFromJson(jsonObject,"deviceDiskTotalCapacity"));
        imageInfo.setImageType("RootVolumeTemplate");

        //共享范围
        String share = getStringFromJson(jsonObject,"share","");
        imageInfo.setSharingScope("1".equals(share) ? "不共享" : "2".equals(share) ? "共享" : "");

        // 设置其他属性
        imageInfo.setOsLanguage("");
        imageInfo.setMinMemory(getBigFromJson(jsonObject, "memory"));
        imageInfo.setMinDisk(getBigFromJson(jsonObject, "deviceDiskTotalCapacity"));
        imageInfo.setDiskDriver("");
        imageInfo.setNetworkDriver("");
        imageInfo.setBootMode("");
        imageInfo.setRemoteProtocol("");

        // 设置平台信息
        imageInfo.setPlatformId(platform.getPlatformId());
        imageInfo.setPlatformName(platform.getPlatformName());

        Map<String, String> param = new HashMap<>(4);
        param.put("domainTemplateId", imageInfo.getUuid());
        param.put("order", "desc");
        param.put("size", "1");
        param.put("start", "1");

        JsonArray logsArray = getJsonArrayFromApi(platform.getPlatformUrl() + WinHongApiConstant.GET_IMAGE_OPERATION_LOGS, param, headers);
        if (logsArray != null && !logsArray.isEmpty()) {
            JsonObject firstLog = logsArray.get(0).getAsJsonObject();
            JsonObject lastLog = logsArray.get(logsArray.size() - 1).getAsJsonObject();

            imageInfo.setVCreateDate(DateUtil.date(firstLog.get("operateTime").getAsLong()));
            imageInfo.setVUpdateDate(DateUtil.date(lastLog.get("operateTime").getAsLong()));
        }

        return imageInfo;
    }

    private JsonArray getJsonArrayFromApi(String url, Map<String, String> params, Map<String, String> headers) {
        try (Response response = OkHttpService.getSync(url, params, headers)) {
            String responseBody = response.body().string();
            JsonElement element = GSON.fromJson(responseBody, JsonElement.class);

            // 检查响应是否为有效的JSON对象
            if (element != null && element.isJsonObject()) {
                JsonElement dataElement = element.getAsJsonObject().get("data");
                // 检查data字段是否为数组
                if (dataElement != null && dataElement.isJsonArray()) {
                    return dataElement.getAsJsonArray();
                }
            }
            log.warn("API返回的数据格式不正确, URL: {}, 响应内容: {}", url, responseBody);
            return new JsonArray();
        } catch (Exception e) {
            log.error("获取API数据失败, URL: {}, 错误: {}", url, e.getMessage());
            return new JsonArray();
        }
    }


    @Override
    public String supportProtocol() {
        return BASIC_WIN_HONG_IMAGE.code();
    }
}
