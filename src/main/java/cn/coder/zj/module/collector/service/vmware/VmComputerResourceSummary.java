package cn.coder.zj.module.collector.service.vmware;

import cn.hutool.core.convert.Convert;
import com.vmware.vim25.*;
import com.vmware.vim25.mo.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * &#064;ClassName  : VmComputerResourceSummary  //类名
 * &#064;Description  :   虚拟机//描述
 * &#064;Author  : <EMAIL> //作者
 * &#064;Date:  2024/7/31  9:21
 */
@Slf4j
public class VmComputerResourceSummary {

    public static List<VirtualMachine> getVmList(ServiceInstance serviceInstance) throws Exception {
        List<VirtualMachine> virtualMachines = new ArrayList<>();
        VirtualMachine virtualMachine = null;

        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("VirtualMachine");
        if (managedEntities != null && managedEntities.length > 0) {
            for (ManagedEntity managedEntity : managedEntities) {
                virtualMachine = (VirtualMachine) managedEntity;
                virtualMachines.add(virtualMachine);

            }
        } else {
            return null;
        }
        return virtualMachines;
    }

    public static List<VmDiskFileInfo> getVmDiskFileList(ServiceInstance serviceInstance, String vmName, Datastore[] datastores) {
        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        Set<VmDiskFileInfo> uniqueSet = ConcurrentHashMap.newKeySet();
        List<CompletableFuture<Void>> futures = Arrays.stream(datastores)
                .map(datastore -> CompletableFuture.runAsync(() -> searchDatastore(datastore, vmName, uniqueSet), executor))
                .toList();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executor.shutdown();
        return new ArrayList<>(uniqueSet);
    }


    private static void searchDatastore(Datastore datastore, String vmName, Set<VmDiskFileInfo> uniqueSet) {
        try {
            HostDatastoreBrowser browser = datastore.getBrowser();
            HostDatastoreBrowserSearchSpec searchSpec = new HostDatastoreBrowserSearchSpec();
            searchSpec.setMatchPattern(new String[]{vmName + "/*.vmdk"});
            FileQueryFlags fileQueryFlags = new FileQueryFlags();
            fileQueryFlags.setFileSize(true);
            fileQueryFlags.setFileType(true);
            fileQueryFlags.setFileOwner(true);
            fileQueryFlags.setModification(true);
            searchSpec.setDetails(fileQueryFlags);
            Task task = browser.searchDatastoreSubFolders_Task(vmName, searchSpec);
            task.waitForTask();
            if (task.getTaskInfo().getState() == TaskInfoState.success) {
                ArrayOfHostDatastoreBrowserSearchResults arrayOfResults = (ArrayOfHostDatastoreBrowserSearchResults) task.getTaskInfo().getResult();
                for (HostDatastoreBrowserSearchResults result : arrayOfResults.getHostDatastoreBrowserSearchResults()) {
                    if (result.getFile() != null) {
                        for (FileInfo fileInfo : result.getFile()) {
                            if (fileInfo instanceof VmDiskFileInfo) {
                                uniqueSet.add((VmDiskFileInfo) fileInfo);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error searching datastore: " + datastore.getName(), e);
        }
    }

    public static List<VmDiskFileInfo> getVmDiskInfoWithActualSizeVm(VirtualMachine vm) throws Exception {
        if (vm == null) {
            throw new IllegalArgumentException("VirtualMachine cannot be null");
        }

        List<VmDiskFileInfo> diskInfoList = new ArrayList<>();
        try {
        VirtualMachineFileLayoutEx fileLayoutEx = vm.getLayoutEx();
            if (fileLayoutEx == null) {
                log.warn("VM [{}] 的 FileLayoutEx 为空，无法获取磁盘信息", vm.getName());
                return diskInfoList;
            }

            for (VirtualMachineFileLayoutExFileInfo fileInfo : fileLayoutEx.getFile()) {
                if (fileInfo == null) {
                    continue;
                }
                // 获取vmdk 文件
                if (fileInfo.getName().contains(".vmdk")) {
                    VmDiskFileInfo diskInfo = new VmDiskFileInfo();
                    diskInfo.setPath(fileInfo.getName());
                    diskInfo.setFileSize(fileInfo.getSize());
                    diskInfoList.add(diskInfo);
                }
            }
        } catch (Exception e) {
            log.error("获取VM [{}] 磁盘信息时发生错误", vm.getName(), e);
            throw new RuntimeException("Failed to get VM disk info: " + e.getMessage(), e);
        }

        return diskInfoList;
    }

    public static List<VmDiskFileInfo> getVmDiskInfoWithActualSize(VirtualMachine vm) throws Exception {
        List<VmDiskFileInfo> diskInfoList = new ArrayList<>();
        VirtualMachineFileLayoutEx fileLayoutEx = vm.getLayoutEx();
        if (fileLayoutEx != null) {
            for (VirtualMachineFileLayoutExFileInfo fileInfo : fileLayoutEx.getFile()) {
                // 获取vmdk 文件
                if (Convert.toStr(fileInfo.getType()).equals(Convert.toStr(VirtualMachineFileLayoutExFileType.diskExtent.name())) ) {
                    VmDiskFileInfo diskInfo = new VmDiskFileInfo();
                    diskInfo.setPath(fileInfo.getName());
                    diskInfo.setFileSize(fileInfo.getSize());
                    diskInfoList.add(diskInfo);
                }
            }
        }

        return diskInfoList;
    }

    private static Date getVmCreationTime(ServiceInstance serviceInstance, VirtualMachine vm) throws Exception {
        EventManager eventManager = serviceInstance.getEventManager();
        EventFilterSpec eventFilter = new EventFilterSpec();

        EventFilterSpecByEntity entityFilter = new EventFilterSpecByEntity();
        entityFilter.setEntity(vm.getMOR());
        entityFilter.setRecursion(EventFilterSpecRecursionOption.self);
        eventFilter.setEntity(entityFilter);
//
//        EventFilterSpecByUsername uFilter = new EventFilterSpecByUsername();
//        uFilter.setSystemUser(false);
//        uFilter.setUserList(new String[] {"administrator"});

//        Calendar endTime = Calendar.getInstance();
//        eventFilter.setEn(endTime);

        Event[] events = eventManager.queryEvents(eventFilter);
        for (Event event : events) {
            String typeName = event.getClass().getName();
            int lastDot = typeName.lastIndexOf('.');
            if (lastDot != -1) {
                typeName = typeName.substring(lastDot + 1);
            }
            System.out.println("Time:" + event.getCreatedTime().getTime());

//            if (event.getClass().getName().equals("VmCreatedEvent")) {
//                return event.getCreatedTime().getTime();
//            }
        }

        return null;
    }

}
