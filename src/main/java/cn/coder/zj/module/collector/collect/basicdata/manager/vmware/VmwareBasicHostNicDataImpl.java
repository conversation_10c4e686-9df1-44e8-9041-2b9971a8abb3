package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.vmware.vim25.HostHardwareSummary;
import com.vmware.vim25.HostVirtualNic;
import com.vmware.vim25.PhysicalNic;
import com.vmware.vim25.mo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_HOST_VIC;
@Slf4j
public class VmwareBasicHostNicDataImpl extends AbstractBasicData {

    private Long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.VM_WARE.code());
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            taskExecutor.execute(() -> {
                try {
                    List<HostNicData> hostNicDataList = handleHostNicData(platform);
                    if (!CollUtil.isEmpty(hostNicDataList)) {
                        BasicCollectData build = BasicCollectData.builder().basicDataMap(hostNicDataList)
                                .metricsName(BASIC_HOST_VIC.code())
                                .build();

                        message.setType(ClusterMsg.MessageType.BASIC);
                        message.setData(GsonUtil.GSON.toJson(build));
                        message.setTime(System.currentTimeMillis());
                        sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                        String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                        log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    }
                } catch (Exception e) {
                    log.error("平台 [{}] 宿主机网络数据收集失败", platform.getPlatformName(), e);
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private List<HostNicData> handleHostNicData(Platform platform) throws Exception {
        ServiceInstance serviceInstance = platform.getVmWarePlatform().getServiceInstance();
        //获取主机list数据
        List<HostSystem> hostDataList = getHostList(serviceInstance);
        if (hostDataList.isEmpty()) {
            log.warn("平台 [{}] 未找到宿主机网络", platform.getPlatformName());
            return null;
        }
        return processHostNic(hostDataList, platform);
    }

    private List<HostNicData> processHostNic(List<HostSystem> hostDataList, Platform platform) {
        List<HostNicData> hostNicDataList = new ArrayList<>();
        for (HostSystem hostSystem : hostDataList) {
            List<HostNicData> hostNicDataList1 = collectHostNicInfo(platform, hostSystem);
            hostNicDataList.addAll(hostNicDataList1);
        }
        return hostNicDataList;
    }

    private List<HostNicData> collectHostNicInfo(Platform platform, HostSystem hostSystem) {
        List<HostNicData> hostNicDataList = new ArrayList<>();
        try {
            // 获取基础信息
            String clusterUuid = hostSystem.getParent().getMOR().getVal();
            HostHardwareSummary hostHardwareSummary = hostSystem.getSummary().getHardware();
            String vms = hostSystem.getMOR().getVal();
            String hostUUid = clusterUuid + hostHardwareSummary.getUuid() + vms;

            // 获取网络相关信息
            PhysicalNic[] pnic = hostSystem.getConfig().getNetwork().getPnic();
            Network[] networks = hostSystem.getNetworks();
            HostVirtualNic[] vnic = hostSystem.getHostNetworkSystem().getNetworkInfo().getVnic();
            String mac = pnic.length > 0 ? pnic[0].getMac() : null;
            // 处理分布式交换机
            Arrays.stream(networks).forEach(network -> {
                HostNicData hostNicData = new HostNicData();
                hostNicData.setHardwareUuid(hostUUid);
                String c_uuid = network.getParent().getMOR().getVal();
                String l2Uuid = c_uuid + network.getMOR().getVal() + network.getName();
                hostNicData.setL2NetworkUuid(l2Uuid);
                hostNicData.setUuid(hostUUid + "-" + l2Uuid + "_3");
                hostNicData.setL2NetworkName(network.getName());
                hostNicData.setState(true);
                hostNicData.setPlatformId(platform.getPlatformId());
                hostNicData.setPlatformName(platform.getPlatformName());
                if (mac != null) {
                    hostNicData.setMac(mac);
                }
                hostNicDataList.add(hostNicData);
            });

            // 处理虚拟网卡和L3网络
            for (HostVirtualNic hostVirtualNic : vnic) {
                // 创建网卡信息
                HostNicData hostNicData = createNicFromVirtualNic(platform, hostUUid, mac, hostVirtualNic);
                hostNicDataList.add(hostNicData);
            }
        } catch (Exception e) {
            log.error("收集主机网卡信息失败: {}", e.getMessage(), e);
        }
        return hostNicDataList;
    }

    private List<HostSystem> getHostList(ServiceInstance serviceInstance) throws Exception {
        // 创建服务实例并获取主机列表
        List<HostSystem> systems = new ArrayList<HostSystem>();
        HostSystem hostSystem = null;
        ManagedEntity[] managedEntities = new InventoryNavigator(serviceInstance.getRootFolder())
                .searchManagedEntities("HostSystem");
        if (managedEntities != null) {
            for (ManagedEntity managedEntity : managedEntities) {
                hostSystem = (HostSystem) managedEntity;
                systems.add(hostSystem);
            }
        }
        return systems;
    }

    // 从虚拟网卡创建网卡信息
    private HostNicData createNicFromVirtualNic(Platform platform,String hostUuid,String mac,HostVirtualNic hostVirtualNic) {
        HostNicData hostNicData = new HostNicData();
        hostNicData.setHardwareUuid(hostUuid);
        hostNicData.setPlatformId(platform.getPlatformId());
        hostNicData.setPlatformName(platform.getPlatformName());
        if (mac != null) {
            hostNicData.setMac(mac);
        }

        // 设置网络信息
        hostNicData.setIpSubnet(hostVirtualNic.getDevice());
        String subnetMask = hostVirtualNic.getSpec().ip.subnetMask;
        int prefixLength = getPrefixLength(subnetMask);
        hostNicData.setIpAddresses(hostVirtualNic.getSpec().ip.ipAddress + "/" + prefixLength);
        hostNicData.setState(hostVirtualNic.getSpec().tsoEnabled);
        hostNicData.setNetworkType("管理口");

        // 设置UUID和名称
        String l2NetworkUuid = hostVirtualNic.getKey() + "_" + hostVirtualNic.getSpec().ip.ipAddress;
        hostNicData.setL2NetworkUuid(l2NetworkUuid);
        hostNicData.setUuid(hostUuid  + "-" + l2NetworkUuid + "_3");
        hostNicData.setL2NetworkName(hostVirtualNic.getDevice());

        return hostNicData;
    }

    private int getPrefixLength(String subnetMask) {
        String[] octets = subnetMask.split("\\.");
        int prefixLength = 0;

        for (String octet : octets) {
            int value = Integer.parseInt(octet);
            // Count the number of 1 bits in the octet
            prefixLength += Integer.bitCount(value);
        }

        return prefixLength;
    }


    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_HOST_VIC.code();
    }
}
