package cn.coder.zj.module.collector.collect.basicdata.manager.vmware;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.iocoder.zj.framework.common.message.ClusterMsg;

import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_VMWARE_STORAGE_POOL;

public class VmwareBasicStoragePoolDataImpl extends AbstractBasicData {
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {

    }

    @Override
    public String supportProtocol() {
        return BASIC_VMWARE_STORAGE_POOL.code();
    }
}
