package cn.coder.zj.module.collector.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum MetricNameType {

    CPU_USED_TASK("CPUUsedUtilization", "CPU使用率"),

    MEM_USAGE_TASK("MemoryUsage", "内存使用率"),

    MEM_USED_TASK("MemoryUsedBytes", "内存使用量"),

    MEM_FREE_TASK("MemoryFreeBytes", "内存空闲容量"),

    DISK_READ_TASK("DiskReadBytes", "磁盘读速度"),

    DISK_WRITE_TASK("DiskWriteBytes", "磁盘写速度"),

    DISK_READ_OPS("DiskReadOps", "磁盘读OPS"),

    DISK_WRITE_OPS("DiskWriteOps", "磁盘写OPS"),

    DISK_USED_TASK("DiskUsedUtilization", "磁盘使用率"),

    DISK_USED_TASK_TOOLS("DiskUsedUtilizationTools","内部监控磁盘使用率"),

    NETWORK_OUT_TASK("NetworkOutBytes", "网卡发送数据速率"),

    NETWORK_IN_TASK("NetworkInBytes", "网卡接收数据速率");

    private final String code;

    private final String desc;

    public static MetricNameType fromCode(String code) {
        for (MetricNameType typeEnum : values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String code() {
        return this.code;
    }

}