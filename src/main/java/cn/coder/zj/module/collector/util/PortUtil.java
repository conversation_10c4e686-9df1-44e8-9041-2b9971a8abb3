package cn.coder.zj.module.collector.util;


import java.util.regex.Matcher;
import java.util.regex.Pattern;



public class PortUtil {


    public static String removeProtocolAndPort(String url) {
        // 去除协议部分
        String noProtocol = url.replaceFirst("^(http://|https://)", "");
        // 去除端口号部分
        String noPort = noProtocol.replaceFirst(":\\d+$", "");
        return noPort;
    }

    public static String extractPort(String url) {
        // 定义正则表达式模式来匹配端口号
        Pattern pattern = Pattern.compile(":\\d+$");
        Matcher matcher = pattern.matcher(url);

        // 查找匹配的端口号
        if (matcher.find()) {
            // 去掉冒号并返回端口号
            return matcher.group().substring(1);
        } else {
            // 如果没有找到端口号，根据协议返回默认端口号
            if (url.startsWith("https://")) {
                return "443";
            } else if (url.startsWith("http://")) {
                return "80";
            }
        }

        // 如果没有找到协议，返回空字符串
        return "";
    }
}
