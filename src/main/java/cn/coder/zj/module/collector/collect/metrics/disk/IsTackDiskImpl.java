package cn.coder.zj.module.collector.collect.metrics.disk;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.DiskType.PROTOCOL_IS_TACK_DISK;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class IsTackDiskImpl extends AbstractMetrics {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            Platform platform = (Platform) o;
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();
                // 获取所有VM的UUID并组合成标签
                List<MonitorInfo> vmUuids = platform.getIsTackPlatform().getVmUuids();
                List<MonitorInfo> hostUuids = platform.getIsTackPlatform().getHostUuids();

                // 磁盘读速度
                diskReadSpeed(vmUuids, hostUuids, platform, metricDataList);
                // 磁盘写速度
                diskWriteSpeed(vmUuids, hostUuids, platform, metricDataList);
                // 磁盘读OPS
                diskReadOps(vmUuids, hostUuids, platform, metricDataList);
                // 磁盘写OPS
                diskWriteOps(vmUuids, hostUuids, platform, metricDataList);
                // 磁盘使用率
                diskUsed(platform, metricDataList);

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.DISK_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
            });
        }
    }


    private List<MetricData> diskWriteOps(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, platform, DISK_WRITE_OPS.code(), "disk_write_requests_rate", metricDataList,"vm");
        getMetricHostData(hostUuids, platform, DISK_WRITE_OPS.code(), "disk_write_requests_rate", metricDataList,"host");
        return metricDataList;
    }


    private List<MetricData> diskReadOps(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, platform, DISK_READ_OPS.code(), "disk_read_requests_rate", metricDataList,"vm");
        getMetricHostData(hostUuids, platform, DISK_READ_OPS.code(), "disk_read_requests_rate", metricDataList,"host");
        return metricDataList;
    }

    private List<MetricData> diskWriteSpeed(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, platform, DISK_WRITE_TASK.code(), "disk_write_bytes_rate", metricDataList,"vm");
        getMetricHostData(hostUuids, platform, DISK_WRITE_TASK.code(), "disk_write_bytes_rate", metricDataList,"host");
        return metricDataList;
    }

    private List<MetricData> diskReadSpeed(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, platform, DISK_READ_TASK.code(), "disk_read_bytes_rate", metricDataList,"vm");
        getMetricHostData(hostUuids, platform, DISK_READ_TASK.code(), "disk_read_bytes_rate", metricDataList,"host");
        return metricDataList;
    }


    private List<MetricData> diskUsed(Platform platform, List<MetricData> metricDataList) {
        getMetricStorageData(platform, DISK_USED_TASK.code(), metricDataList);
        return metricDataList;
    }

    private List<MetricData> getMetricStorageData(Platform platform, String code, List<MetricData> metricDataList) {
        MetricData metricData = new MetricData();
        metricData.setPlatformId(platform.getPlatformId());
        metricData.setMetricName(code);
        Map<String, String> param = new HashMap<>();
        param.put("os_id", platform.getPassword());
        param.put("ct_user_id", platform.getUsername());
        List<Long> timestamps = new ArrayList<>();
        List<Double> values = new ArrayList<>();
        try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_STORAGE_LIST, param, null)) {
            JsonObject jsonObject = GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject();
            if (!jsonObject.has("results") || jsonObject.get("results").isJsonNull()) {

            }
            JsonArray results = jsonObject.get("results").getAsJsonArray();
            if (results.isEmpty()) {
                log.warn("平台 {} 返回空数据集", platform.getPlatformName());

            }
            results.forEach(jsonElement -> {
                JsonObject storage = jsonElement.getAsJsonObject();

                metricData.setResourceId(storage.get("uuid").getAsString());
                metricData.setResourceName(storage.get("uuid").getAsString());
                double diskValue = storage.get("capacity_used_rate").getAsDouble();
                // 判断是否需要处理小数位数
                String strValue = String.valueOf(diskValue);
                if (strValue.contains(".")) {
                    int decimalLength = strValue.split("\\.")[1].length();
                    if (decimalLength >= 4) {
                        diskValue = Math.round(diskValue * 100.0) / 100.0;
                    }
                }
                timestamps.add(System.currentTimeMillis());
                values.add(diskValue);
                metricData.setTimestamps(timestamps);
                metricData.setValues(values);
                metricDataList.add(metricData);
            });
        } catch (IOException e) {
            log.error("error collecting Storage basic data: {}", e.getMessage());
        }
        return metricDataList;
    }


    private List<MetricData> getMetricHostData(List<MonitorInfo> hostUuids, Platform platform, String code, String labelName, List<MetricData> metricDataList,String type) {
        hostUuids.stream().forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(code);
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType(type);

            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            param.put("uuid", info.getUuid());
            param.put("item_name", labelName);
            param.put("from", String.valueOf(DateUtil.currentSeconds() * 1000));
            param.put("to", String.valueOf(DateUtil.currentSeconds() * 1000));

            List<Long> timestamps = new ArrayList<>();
            List<Double> values = new ArrayList<>();

            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_HOST_REALTIME, param, null)) {
                GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray().forEach(jsonElement -> {
                    String time = jsonElement.getAsJsonObject().get("sampling_time").getAsString();
                    double diskValue = jsonElement.getAsJsonObject().get("sampling_value").getAsDouble();
                    if (labelName.contains("bytes_rate")) {
                        diskValue = diskValue * 1024;
                    }
                    // 判断是否需要处理小数位数
                    String strValue = String.valueOf(diskValue);
                    if (strValue.contains(".")) {
                        int decimalLength = strValue.split("\\.")[1].length();
                        if (decimalLength > 4) {
                            diskValue = Math.round(diskValue * 100.0) / 100.0;
                        }
                    }
                    timestamps.add(Long.valueOf(time));
                    values.add(diskValue);
                    metricData.setTimestamps(timestamps);
                    metricData.setValues(values);
                    metricDataList.add(metricData);
                });
            } catch (IOException e) {
                log.error("error collecting host basic data: {}", e.getMessage());
            }
        });
        return metricDataList;
    }

    private List<MetricData> getMetricVmData(List<MonitorInfo> vmUuids, Platform platform, String code, String labelName, List<MetricData> metricDataList,String type) {
        vmUuids.stream().forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(code);
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType(type);
            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            param.put("uuid", info.getUuid());
            param.put("item_names", labelName);
            param.put("device_type", "vm");
            param.put("from", String.valueOf(DateUtil.currentSeconds() * 1000));
            param.put("to", String.valueOf(DateUtil.currentSeconds() * 1000));

            List<Long> timestamps = new ArrayList<>();
            List<Double> values = new ArrayList<>();

            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_REALTIME, param, null)) {
                GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray().forEach(jsonElement -> {
                    JsonArray asJsonArray = jsonElement.getAsJsonObject().getAsJsonArray("values");
                    if (!asJsonArray.isEmpty()) {
                        JsonObject asJsonObject = asJsonArray.get(0).getAsJsonObject();
                        String time = asJsonObject.get("sampling_time").getAsString();
                        double diskValue = asJsonObject.get("sampling_value").getAsDouble();
                        if (labelName.contains("bytes_rate")) {
                            diskValue = diskValue * 1024;
                        }
                        timestamps.add(Long.valueOf(time));
                        values.add(diskValue);
                        metricData.setTimestamps(timestamps);
                        metricData.setValues(values);
                        metricDataList.add(metricData);
                    }
                });
            } catch (IOException e) {
                log.error("error collecting IsTackCpuImpl basic data: {}", e.getMessage());
            }
        });
        return metricDataList;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_IS_TACK_DISK.code();
    }
}
