package cn.coder.zj.module.collector;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@EnableAsync
@EnableScheduling
@SpringBootApplication(exclude= {DataSourceAutoConfiguration.class})
public class Collector {
    public static void main(String[] args) {
        SpringApplication.run(Collector.class, args);
    }
}