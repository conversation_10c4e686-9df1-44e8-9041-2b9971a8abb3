package cn.coder.zj.module.collector.collect.metrics.net;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.sanfor.SangForApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.ApiCacheService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_IN_TASK;
import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_OUT_TASK;
import static cn.coder.zj.module.collector.enums.NetType.PROTOCOL_SXF_NET;
import static cn.coder.zj.module.collector.util.ApiUtil.getJsonArrayFromApi;

@Slf4j
public class SangForNetImpl extends AbstractMetrics {
    protected long startTime;
    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.SANG_FOR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();
                Platform platform = (Platform) o;
                String token = platform.getSxfPlatform().getToken();
                if (token == null) {
                    log.error("平台 {} token为空", platform.getPlatformName());
                    return;
                }

                String cookie = platform.getSxfPlatform().getLoginAuthCookie();
                Map<String, String> headers = Map.of("Cookie", "LoginAuthCookie=" + cookie, "CSRFPreventionToken", token);

                // 获取所有VM的UUID并组合成标签
                List<MonitorInfo> vmUuids = platform.getSxfPlatform().getVmUuids();
                List<MonitorInfo> hostUuids = platform.getSxfPlatform().getHostUuids();
                if (vmUuids == null || vmUuids.isEmpty()) {
                    log.error("平台 {} VM UUID为空", platform.getPlatformName());
                    return;
                }

                netVmData(vmUuids, platform, headers, metricDataList);
                netHostData(hostUuids, platform, headers, metricDataList);

                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect collectData data end, cost {} seconds", endTimeFormatted);
                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.NET_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
            });
        }
    }

    private List<MetricData> netVmData(List<MonitorInfo> vmUuids, Platform platform, Map<String, String> headers, List<MetricData> metricDataList) {
        String platformUrl = platform.getPlatformUrl();
        Long currentTime = System.currentTimeMillis();

        vmUuids.forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(NETWORK_OUT_TASK.code());
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("vm");

            String url = platformUrl + SangForApiConstant.GET_VM_DETAIL.replace("{vmid}", info.getUuid());
            JsonObject obj = ApiCacheService.getJsonObject(url, null, headers);
            if (obj.size() <= 0 || !obj.has("net_sheet")) {
                return;
            }

            JsonObject netSheet = obj.getAsJsonObject("net_sheet");
            boolean hasTotal = netSheet.has("total");

            JsonArray netData = hasTotal ?
                    netSheet.getAsJsonObject("total").getAsJsonObject("bps").getAsJsonArray("hour") :
                    netSheet.getAsJsonObject("bps").getAsJsonArray("hour");

            Double netOut = dealInfo(netData, "发送");
            Double netIn = dealInfo(netData, "接收");

            if (netOut != null) {
                metricData.setValues(Arrays.asList(netOut));
                metricData.setTimestamps(Arrays.asList(currentTime));
                metricDataList.add(metricData);
            }

            if (netIn != null) {
                MetricData writeData = BeanUtil.copyProperties(metricData, MetricData.class);
                writeData.setMetricName(NETWORK_IN_TASK.code());
                writeData.setValues(Arrays.asList(netIn));
                writeData.setTimestamps(Arrays.asList(currentTime));
                writeData.setType("vm");
                metricDataList.add(writeData);
            }
        });

        return metricDataList;
    }

    private List<MetricData> netHostData(List<MonitorInfo> hostUuids, Platform platform, Map<String, String> headers, List<MetricData> metricDataList) {
        String platformUrl = platform.getPlatformUrl();
        Long currentTime = System.currentTimeMillis();

        hostUuids.forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(NETWORK_OUT_TASK.code());
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("host");

            String baseUrl = platformUrl + SangForApiConstant.GET_HOST_DETAIL.replace("{nodeId}", info.getUuid());
            JsonArray netData = getJsonArrayFromApi(baseUrl + "net", null, headers);

            if (!CollUtil.isEmpty(netData)) {
                Double netOut = dealInfo(netData, "发送");
                Double netIn = dealInfo(netData, "接收");

                if (netOut != null) {
                    metricData.setValues(Arrays.asList(netOut));
                    metricData.setTimestamps(Arrays.asList(currentTime));
                    metricDataList.add(metricData);
                }

                if (netIn != null) {
                    MetricData writeData = BeanUtil.copyProperties(metricData, MetricData.class);
                    writeData.setMetricName(NETWORK_IN_TASK.code());
                    writeData.setValues(Arrays.asList(netIn));
                    writeData.setTimestamps(Arrays.asList(currentTime));
                    writeData.setType("host");
                    metricDataList.add(writeData);
                }
            }
        });

        return metricDataList;
    }

    private Double dealInfo(JsonArray data, String label) {
        if (data == null || data.isEmpty()) {
            return null;
        }

        // 处理VM的数据格式
        if (data.get(0).getAsJsonObject().has("name")) {
            JsonArray valArr = new JsonArray();

            for (JsonElement element : data) {
                JsonObject item = element.getAsJsonObject();
                String name = item.get("name").getAsString();

                if (name.contains(label)) {
                    valArr = item.get("data").getAsJsonArray();
                    break;
                }
            }

            if (!valArr.isEmpty()) {
                double value = valArr.get(valArr.size() - 1).getAsDouble();
                return Double.parseDouble(String.format("%.2f", value));
            }
        }
        // 处理Host的数据格式
        else {
            for (JsonElement element : data) {
                JsonObject item = element.getAsJsonObject();
                if (item.get("name").getAsString().contains(label)) {
                    JsonArray cpuData = item.get("data").getAsJsonArray();
                    if (cpuData != null && !cpuData.isEmpty()) {
                        double value = cpuData.get(cpuData.size() - 1).getAsDouble();
                        return Double.parseDouble(String.format("%.2f", value));
                    }
                    break;
                }
            }
        }

        return null;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_SXF_NET.code();
    }
}
