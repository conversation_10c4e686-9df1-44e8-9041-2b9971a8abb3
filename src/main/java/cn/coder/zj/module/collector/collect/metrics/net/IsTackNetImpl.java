package cn.coder.zj.module.collector.collect.metrics.net;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.istack.IStackApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.common.MonitorInfo;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.asyncDemo.OkHttpService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.core.task.TaskExecutor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_IN_TASK;
import static cn.coder.zj.module.collector.enums.MetricNameType.NETWORK_OUT_TASK;
import static cn.coder.zj.module.collector.enums.NetType.PROTOCOL_IS_TACK_NET;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class IsTackNetImpl extends AbstractMetrics {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object o : platformList) {
            Platform platform = (Platform) o;
            taskExecutor.execute(() -> {
                List<MetricData> metricDataList = new ArrayList<>();

                // 获取所有VM的UUID并组合成标签
                List<MonitorInfo> vmUuids = platform.getIsTackPlatform().getVmUuids();
                List<MonitorInfo> hostUuids = platform.getIsTackPlatform().getHostUuids();

                netVm(vmUuids, hostUuids, platform, metricDataList);
                netHost(vmUuids, hostUuids, platform, metricDataList);

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.NET_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
//                        log.info("collectVmData name: {}", Thread.currentThread().getName());
            });
        }
    }

    private void netVm(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, platform, NETWORK_OUT_TASK.code(), "net_out_bytes_rate", metricDataList);
        getMetricHostData(hostUuids, platform, NETWORK_OUT_TASK.code(), "net_out_bytes_rate", metricDataList);
    }


    private void netHost(List<MonitorInfo> vmUuids, List<MonitorInfo> hostUuids, Platform platform, List<MetricData> metricDataList) {
        getMetricVmData(vmUuids, platform, NETWORK_IN_TASK.code(), "net_in_bytes_rate", metricDataList);
        getMetricHostData(hostUuids, platform, NETWORK_IN_TASK.code(), "net_in_bytes_rate", metricDataList);
    }


    private List<MetricData> getMetricHostData(List<MonitorInfo> hostUuids, Platform platform, String code, String labelName, List<MetricData> metricDataList) {
        hostUuids.stream().forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(code);
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("host");

            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            param.put("uuid", info.getUuid());
            param.put("item_name", labelName);
            param.put("from", String.valueOf(DateUtil.currentSeconds() * 1000));
            param.put("to", String.valueOf(DateUtil.currentSeconds() * 1000));

            List<Long> timestamps = new ArrayList<>();
            List<Double> values = new ArrayList<>();

            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_HOST_REALTIME, param, null)) {
                GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray().forEach(jsonElement -> {
                    String time = jsonElement.getAsJsonObject().get("sampling_time").getAsString();
                    double value = jsonElement.getAsJsonObject().get("sampling_value").getAsDouble();
                    if (labelName.contains("bytes_rate")) {
                        value = value * 1024;
                    }
                    timestamps.add(Long.valueOf(time));
                    values.add(value);
                    metricData.setTimestamps(timestamps);
                    metricData.setValues(values);
                    metricDataList.add(metricData);
                });
            } catch (IOException e) {
                log.error("error collecting host basic data: {}", e.getMessage());
            }
        });
        return metricDataList;
    }

    private List<MetricData> getMetricVmData(List<MonitorInfo> vmUuids, Platform platform, String code, String labelName, List<MetricData> metricDataList) {
        vmUuids.stream().forEach(info -> {
            MetricData metricData = new MetricData();
            metricData.setPlatformId(platform.getPlatformId());
            metricData.setMetricName(code);
            metricData.setResourceId(info.getUuid());
            metricData.setResourceName(info.getName());
            metricData.setType("vm");
            Map<String, String> param = new HashMap<>();
            param.put("os_id", platform.getPassword());
            param.put("ct_user_id", platform.getUsername());
            param.put("uuid", info.getUuid());
            param.put("item_names", labelName);
            param.put("device_type", "vm");
            param.put("from", String.valueOf(DateUtil.currentSeconds() * 1000));
            param.put("to", String.valueOf(DateUtil.currentSeconds() * 1000));

            List<Long> timestamps = new ArrayList<>();
            List<Double> values = new ArrayList<>();

            try (Response response = OkHttpService.getSync(platform.getPlatformUrl() + IStackApiConstant.GET_VM_REALTIME, param, null)) {
                GSON.fromJson(response.body().string(), JsonElement.class).getAsJsonObject().get("results").getAsJsonArray().forEach(jsonElement -> {
                    JsonArray asJsonArray = jsonElement.getAsJsonObject().getAsJsonArray("values");
                    if (!asJsonArray.isEmpty()) {
                        JsonObject asJsonObject = asJsonArray.get(0).getAsJsonObject();
                        String time = asJsonObject.get("sampling_time").getAsString();
                        double value = asJsonObject.get("sampling_value").getAsDouble();
                        timestamps.add(Long.valueOf(time));
                        values.add(value);
                        metricData.setTimestamps(timestamps);
                        metricData.setValues(values);
                        metricDataList.add(metricData);
                    }
                });
            } catch (IOException e) {
                log.error("error collecting IsTackCpuImpl basic data: {}", e.getMessage());
            }
        });
        return metricDataList;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_IS_TACK_NET.code();
    }
}
