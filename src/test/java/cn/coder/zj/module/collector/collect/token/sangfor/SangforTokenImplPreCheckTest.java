package cn.coder.zj.module.collector.collect.token.sangfor;

import cn.coder.zj.module.collector.dal.platform.Platform;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 深信服平台连接验证测试类 - 专门测试修改后的preCheck方法
 * 验证修改后的行为：
 * 1. 完成所有3次尝试
 * 2. 只在最后根据整体结果判断成功或失败
 * 3. 避免中间过程的误导性日志
 */
@Slf4j
@SpringBootTest
public class SangforTokenImplPreCheckTest {

    @Test
    public void testPreCheckWithUnreachableHost() {
        // 测试不可达的深信服平台
        Platform unreachablePlatform = new Platform();
        unreachablePlatform.setPlatformName("深信服测试平台-不可达");
        unreachablePlatform.setPlatformUrl("http://**************:8080");
        unreachablePlatform.setUsername("admin");
        unreachablePlatform.setPassword("password");
        
        SangforTokenImpl tokenImpl = new SangforTokenImpl();
        
        log.info("=== 测试修改后的深信服preCheck方法 - 不可达平台 ===");
        log.info("开始测试不可达平台: {}", unreachablePlatform.getPlatformUrl());
        log.info("预期行为: 完成3次尝试后，最终判定为离线，不会出现误导性的'连接成功'日志");
        
        long startTime = System.currentTimeMillis();
        
        tokenImpl.preCheck(unreachablePlatform);
        
        long endTime = System.currentTimeMillis();
        log.info("不可达平台测试完成，耗时: {}ms", endTime - startTime);
        
        // 验证平台状态应该为离线
        assert unreachablePlatform.getState() == 1L : "不可达平台状态应该为离线(1)";
        log.info("✓ 验证通过: 不可达平台最终状态为离线");
    }

    @Test
    public void testPreCheckWithReachableHost() {
        // 测试可达的深信服平台
        Platform reachablePlatform = new Platform();
        reachablePlatform.setPlatformName("深信服测试平台-可达");
        reachablePlatform.setPlatformUrl("http://**************:8080");
        reachablePlatform.setUsername("admin");
        reachablePlatform.setPassword("password");
        
        SangforTokenImpl tokenImpl = new SangforTokenImpl();
        
        log.info("=== 测试修改后的深信服preCheck方法 - 可达平台 ===");
        log.info("开始测试可达平台: {}", reachablePlatform.getPlatformUrl());
        log.info("预期行为: 完成3次尝试，根据实际连接情况判定最终状态");
        
        long startTime = System.currentTimeMillis();
        
        tokenImpl.preCheck(reachablePlatform);
        
        long endTime = System.currentTimeMillis();
        log.info("可达平台测试完成，耗时: {}ms", endTime - startTime);
        
        // 注意：这里不验证状态，因为即使网络可达，API可能不存在
        log.info("平台最终状态: {} (0=在线, 1=离线)", reachablePlatform.getState());
    }

    @Test
    public void testBehaviorComparison() {
        log.info("=== 深信服平台行为对比测试 ===");
        log.info("修改前的问题:");
        log.info("1. 只要有一次成功就提前退出，可能导致网络不稳定时的误判");
        log.info("2. 缺乏完整的统计信息");
        log.info("3. 可能存在HTTP连接池状态混乱");
        log.info("");
        log.info("修改后的改进:");
        log.info("1. 完成所有3次尝试，提供完整的统计信息");
        log.info("2. 最后根据成功/失败次数给出明确的判定结果");
        log.info("3. 优化日志级别，避免误导");
        log.info("");
        
        // 测试不可达平台
        testPreCheckWithUnreachableHost();
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试可达平台
        testPreCheckWithReachableHost();
        
        log.info("=== 深信服平台行为对比测试完成 ===");
    }

    @Test
    public void testMultiplePlatformsConcurrently() {
        log.info("=== 深信服平台并发测试 ===");
        
        // 创建多个平台进行并发测试
        Platform platform1 = new Platform();
        platform1.setPlatformName("深信服平台1");
        platform1.setPlatformUrl("http://**************:8080");
        platform1.setUsername("admin");
        platform1.setPassword("password");
        
        Platform platform2 = new Platform();
        platform2.setPlatformName("深信服平台2");
        platform2.setPlatformUrl("http://**************:8080");
        platform2.setUsername("admin");
        platform2.setPassword("password");
        
        SangforTokenImpl tokenImpl = new SangforTokenImpl();
        
        // 并发执行
        Thread thread1 = new Thread(() -> {
            log.info("线程1开始测试平台1");
            tokenImpl.preCheck(platform1);
            log.info("线程1完成，平台1状态: {}", platform1.getState());
        });
        
        Thread thread2 = new Thread(() -> {
            log.info("线程2开始测试平台2");
            tokenImpl.preCheck(platform2);
            log.info("线程2完成，平台2状态: {}", platform2.getState());
        });
        
        thread1.start();
        thread2.start();
        
        try {
            thread1.join();
            thread2.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("=== 深信服平台并发测试完成 ===");
        log.info("平台1最终状态: {}", platform1.getState());
        log.info("平台2最终状态: {}", platform2.getState());
    }
}
