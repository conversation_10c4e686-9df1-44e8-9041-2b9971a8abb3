package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.dal.platform.ZsTackPlatform;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ZStackClientWrapperMultiPlatform 测试类
 * 验证多平台支持和线程安全功能
 *
 * {{RIPER-5:
 *   Action: "Added"
 *   Task_ID: "ZStack多平台支持测试"
 *   Timestamp: "2025-01-30T12:30:00Z"
 *   Authoring_Role: "LD"
 *   Principle_Applied: "测试驱动开发 + 并发安全验证"
 *   Quality_Check: "多平台并发测试覆盖，实例隔离验证"
 * }}
 */
class ZStackClientWrapperMultiPlatformTest {

    private Platform platform1;
    private Platform platform2;
    private Platform platform3;

    @BeforeEach
    void setUp() {
        // 创建测试平台1 - sessionId认证
        platform1 = new Platform();
        platform1.setPlatformId(1L);
        platform1.setPlatformName("ZStack-Platform-1");
        platform1.setPlatformUrl("http://*************:8080");
        platform1.setUsername("admin");
        platform1.setPassword("password123");
        platform1.setAkType(0); // sessionId认证
        platform1.setState(1);
        
        ZsTackPlatform zsPlatform1 = ZsTackPlatform.builder()
                .token("test-session-token-1")
                .type(0)
                .build();
        platform1.setZsTackPlatform(zsPlatform1);

        // 创建测试平台2 - AccessKey认证
        platform2 = new Platform();
        platform2.setPlatformId(2L);
        platform2.setPlatformName("ZStack-Platform-2");
        platform2.setPlatformUrl("http://192.168.1.200:8080");
        platform2.setUsername("access-key-id-2");
        platform2.setPassword("access-key-secret-2");
        platform2.setAkType(1); // AccessKey认证
        platform2.setState(1);

        // 创建测试平台3 - 不同端口
        platform3 = new Platform();
        platform3.setPlatformId(3L);
        platform3.setPlatformName("ZStack-Platform-3");
        platform3.setPlatformUrl("https://192.168.1.300:443");
        platform3.setUsername("access-key-id-3");
        platform3.setPassword("access-key-secret-3");
        platform3.setAkType(1); // AccessKey认证
        platform3.setState(1);
    }

    @Test
    @DisplayName("测试多平台实例创建和缓存")
    void testMultiPlatformInstanceCreation() {
        // 清理之前的实例
        ZStackClientWrapperMultiPlatform.clearAllPlatformInstances();
        
        // 验证初始状态
        String initialStats = ZStackClientWrapperMultiPlatform.getPlatformInstanceStats();
        assertTrue(initialStats.contains("总实例数: 0"));
        
        // 模拟多平台API调用（会创建实例）
        assertDoesNotThrow(() -> {
            try {
                ZStackClientWrapperMultiPlatform.validateLoginMultiPlatform(platform1);
            } catch (Exception e) {
                // 预期的网络连接异常
            }
            
            try {
                ZStackClientWrapperMultiPlatform.validateLoginMultiPlatform(platform2);
            } catch (Exception e) {
                // 预期的网络连接异常
            }
            
            try {
                ZStackClientWrapperMultiPlatform.validateLoginMultiPlatform(platform3);
            } catch (Exception e) {
                // 预期的网络连接异常
            }
        });
        
        // 验证实例已创建
        String finalStats = ZStackClientWrapperMultiPlatform.getPlatformInstanceStats();
        assertTrue(finalStats.contains("总实例数: 3"));
        assertTrue(finalStats.contains("ZStack-Platform-1"));
        assertTrue(finalStats.contains("ZStack-Platform-2"));
        assertTrue(finalStats.contains("ZStack-Platform-3"));
    }

    @Test
    @DisplayName("测试并发多平台访问的线程安全性")
    void testConcurrentMultiPlatformAccess() throws InterruptedException {
        ZStackClientWrapperMultiPlatform.clearAllPlatformInstances();
        
        int threadCount = 6; // 每个平台2个线程
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        // 创建并发任务
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        // 平台1的并发访问
        futures[0] = CompletableFuture.runAsync(() -> {
            try {
                ZStackClientWrapperMultiPlatform.queryZonesMultiPlatform(platform1);
            } catch (Exception e) {
                // 预期的网络异常
            } finally {
                latch.countDown();
            }
        });
        
        futures[1] = CompletableFuture.runAsync(() -> {
            try {
                ZStackClientWrapperMultiPlatform.queryHostsMultiPlatform(platform1);
            } catch (Exception e) {
                // 预期的网络异常
            } finally {
                latch.countDown();
            }
        });
        
        // 平台2的并发访问
        futures[2] = CompletableFuture.runAsync(() -> {
            try {
                ZStackClientWrapperMultiPlatform.queryVmInstancesMultiPlatform(platform2);
            } catch (Exception e) {
                // 预期的网络异常
            } finally {
                latch.countDown();
            }
        });
        
        futures[3] = CompletableFuture.runAsync(() -> {
            try {
                ZStackClientWrapperMultiPlatform.querySecurityGroupsMultiPlatform(platform2);
            } catch (Exception e) {
                // 预期的网络异常
            } finally {
                latch.countDown();
            }
        });
        
        // 平台3的并发访问
        futures[4] = CompletableFuture.runAsync(() -> {
            try {
                ZStackClientWrapperMultiPlatform.queryClustersMultiPlatform(platform3);
            } catch (Exception e) {
                // 预期的网络异常
            } finally {
                latch.countDown();
            }
        });
        
        futures[5] = CompletableFuture.runAsync(() -> {
            try {
                ZStackClientWrapperMultiPlatform.queryVolumesMultiPlatform(platform3);
            } catch (Exception e) {
                // 预期的网络异常
            } finally {
                latch.countDown();
            }
        });
        
        // 等待所有任务完成
        assertTrue(latch.await(30, TimeUnit.SECONDS), "并发测试应在30秒内完成");
        
        // 等待所有CompletableFuture完成
        CompletableFuture.allOf(futures).join();
        
        // 验证所有平台实例都已创建
        String stats = ZStackClientWrapperMultiPlatform.getPlatformInstanceStats();
        assertTrue(stats.contains("总实例数: 3"));
    }

    @Test
    @DisplayName("测试平台实例清理功能")
    void testPlatformInstanceCleanup() {
        ZStackClientWrapperMultiPlatform.clearAllPlatformInstances();
        
        // 创建实例
        assertDoesNotThrow(() -> {
            try {
                ZStackClientWrapperMultiPlatform.validateLoginMultiPlatform(platform1);
                ZStackClientWrapperMultiPlatform.validateLoginMultiPlatform(platform2);
            } catch (Exception e) {
                // 预期的网络异常
            }
        });
        
        // 验证实例已创建
        String stats = ZStackClientWrapperMultiPlatform.getPlatformInstanceStats();
        assertTrue(stats.contains("总实例数: 2"));
        
        // 清理单个平台实例
        ZStackClientWrapperMultiPlatform.clearPlatformInstance(platform1);
        stats = ZStackClientWrapperMultiPlatform.getPlatformInstanceStats();
        assertTrue(stats.contains("总实例数: 1"));
        assertFalse(stats.contains("ZStack-Platform-1"));
        assertTrue(stats.contains("ZStack-Platform-2"));
        
        // 清理所有实例
        ZStackClientWrapperMultiPlatform.clearAllPlatformInstances();
        stats = ZStackClientWrapperMultiPlatform.getPlatformInstanceStats();
        assertTrue(stats.contains("总实例数: 0"));
    }

    @Test
    @DisplayName("测试认证信息设置")
    void testAuthenticationSetting() {
        // 测试sessionId认证
        assertDoesNotThrow(() -> {
            Object mockAction = new Object() {
                public String sessionId;
            };
            ZStackClientWrapperMultiPlatform.setAuthentication(mockAction, platform1);
            // 验证sessionId已设置（通过反射）
            assertEquals("test-session-token-1", mockAction.getClass().getField("sessionId").get(mockAction));
        });
        
        // 测试AccessKey认证
        assertDoesNotThrow(() -> {
            Object mockAction = new Object() {
                public String accessKeyId;
                public String accessKeySecret;
            };
            ZStackClientWrapperMultiPlatform.setAuthentication(mockAction, platform2);
            // 验证AccessKey已设置
            assertEquals("access-key-id-2", mockAction.getClass().getField("accessKeyId").get(mockAction));
            assertEquals("access-key-secret-2", mockAction.getClass().getField("accessKeySecret").get(mockAction));
        });
    }

    @Test
    @DisplayName("测试异常处理")
    void testExceptionHandling() {
        // 测试空平台参数
        assertThrows(IllegalArgumentException.class, () -> {
            ZStackClientWrapperMultiPlatform.executeWithMultiPlatformClient(null, () -> "test");
        });
        
        // 测试空URL
        Platform invalidPlatform = new Platform();
        invalidPlatform.setPlatformId(999L);
        invalidPlatform.setPlatformName("Invalid-Platform");
        invalidPlatform.setPlatformUrl(null);
        
        assertThrows(IllegalArgumentException.class, () -> {
            ZStackClientWrapperMultiPlatform.validateLoginMultiPlatform(invalidPlatform);
        });
        
        // 测试无效端口
        Platform invalidPortPlatform = new Platform();
        invalidPortPlatform.setPlatformId(998L);
        invalidPortPlatform.setPlatformName("Invalid-Port-Platform");
        invalidPortPlatform.setPlatformUrl("http://*************:99999");
        
        assertThrows(IllegalArgumentException.class, () -> {
            ZStackClientWrapperMultiPlatform.validateLoginMultiPlatform(invalidPortPlatform);
        });
    }

    @Test
    @DisplayName("测试重试机制")
    void testRetryMechanism() {
        // 测试带重试的区域查询
        assertThrows(RuntimeException.class, () -> {
            ZStackClientWrapperMultiPlatform.queryZonesMultiPlatformWithRetry(platform1, 2);
        });
        
        // 验证重试逻辑不会抛出意外异常
        assertDoesNotThrow(() -> {
            try {
                ZStackClientWrapperMultiPlatform.queryZonesMultiPlatformWithRetry(platform1, 1);
            } catch (RuntimeException e) {
                // 预期的运行时异常
                assertTrue(e.getMessage().contains("多平台区域查询失败"));
            }
        });
    }
}
