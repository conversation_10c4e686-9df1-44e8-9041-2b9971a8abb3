package cn.coder.zj.module.collector.collect.token.vmware;

import cn.coder.zj.module.collector.dal.platform.Platform;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * VMware平台连接验证测试类 - 专门测试修改后的preCheck方法
 * 验证修改后的行为：
 * 1. 完成所有3次尝试
 * 2. 只在最后根据整体结果判断成功或失败
 * 3. 避免中间过程的误导性日志
 */
@Slf4j
@SpringBootTest
public class VmWareTokenImplPreCheckTest {

    @Test
    public void testPreCheckWithUnreachableHost() {
        // 测试不可达的VMware平台
        Platform unreachablePlatform = new Platform();
        unreachablePlatform.setPlatformName("VMware测试平台-不可达");
        unreachablePlatform.setPlatformUrl("https://**************/sdk");
        unreachablePlatform.setUsername("<EMAIL>");
        unreachablePlatform.setPassword("password");
        
        VmWareTokenImpl tokenImpl = new VmWareTokenImpl();
        
        log.info("=== 测试修改后的VMware preCheck方法 - 不可达平台 ===");
        log.info("开始测试不可达平台: {}", unreachablePlatform.getPlatformUrl());
        log.info("预期行为: 完成3次尝试后，最终判定为离线，不会出现误导性的'连接成功'日志");
        
        long startTime = System.currentTimeMillis();
        
        tokenImpl.preCheck(unreachablePlatform);
        
        long endTime = System.currentTimeMillis();
        log.info("不可达平台测试完成，耗时: {}ms", endTime - startTime);
        
        log.info("✓ VMware不可达平台测试完成");
    }

    @Test
    public void testPreCheckWithReachableHost() {
        // 测试可达的VMware平台
        Platform reachablePlatform = new Platform();
        reachablePlatform.setPlatformName("VMware测试平台-可达");
        reachablePlatform.setPlatformUrl("https://**************/sdk");
        reachablePlatform.setUsername("<EMAIL>");
        reachablePlatform.setPassword("password");
        
        VmWareTokenImpl tokenImpl = new VmWareTokenImpl();
        
        log.info("=== 测试修改后的VMware preCheck方法 - 可达平台 ===");
        log.info("开始测试可达平台: {}", reachablePlatform.getPlatformUrl());
        log.info("预期行为: 完成3次尝试，根据实际连接情况判定最终状态");
        
        long startTime = System.currentTimeMillis();
        
        tokenImpl.preCheck(reachablePlatform);
        
        long endTime = System.currentTimeMillis();
        log.info("可达平台测试完成，耗时: {}ms", endTime - startTime);
        
        log.info("✓ VMware可达平台测试完成");
    }

    @Test
    public void testBehaviorComparison() {
        log.info("=== VMware平台行为对比测试 ===");
        log.info("修改前的问题:");
        log.info("1. 只要有一次成功就提前退出，可能导致网络不稳定时的误判");
        log.info("2. 缺乏完整的统计信息");
        log.info("3. 可能存在VMware SDK全局状态问题");
        log.info("");
        log.info("修改后的改进:");
        log.info("1. 完成所有3次尝试，提供完整的统计信息");
        log.info("2. 最后根据成功/失败次数给出明确的判定结果");
        log.info("3. 增加同步保护，避免潜在的SDK状态问题");
        log.info("4. 优化日志级别，避免误导");
        log.info("");
        
        // 测试不可达平台
        testPreCheckWithUnreachableHost();
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试可达平台
        testPreCheckWithReachableHost();
        
        log.info("=== VMware平台行为对比测试完成 ===");
    }

    @Test
    public void testMultiplePlatformsConcurrently() {
        log.info("=== VMware平台并发测试 ===");
        
        // 创建多个平台进行并发测试
        Platform platform1 = new Platform();
        platform1.setPlatformName("VMware平台1");
        platform1.setPlatformUrl("https://**************/sdk");
        platform1.setUsername("<EMAIL>");
        platform1.setPassword("password");
        
        Platform platform2 = new Platform();
        platform2.setPlatformName("VMware平台2");
        platform2.setPlatformUrl("https://**************/sdk");
        platform2.setUsername("<EMAIL>");
        platform2.setPassword("password");
        
        VmWareTokenImpl tokenImpl = new VmWareTokenImpl();
        
        // 并发执行
        Thread thread1 = new Thread(() -> {
            log.info("线程1开始测试VMware平台1");
            tokenImpl.preCheck(platform1);
            log.info("线程1完成VMware平台1测试");
        });
        
        Thread thread2 = new Thread(() -> {
            log.info("线程2开始测试VMware平台2");
            tokenImpl.preCheck(platform2);
            log.info("线程2完成VMware平台2测试");
        });
        
        thread1.start();
        thread2.start();
        
        try {
            thread1.join();
            thread2.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("=== VMware平台并发测试完成 ===");
    }

    @Test
    public void testVMwareSpecificIssues() {
        log.info("=== VMware平台特有问题测试 ===");
        log.info("VMware平台可能存在的问题:");
        log.info("1. VMware SDK可能存在全局状态问题");
        log.info("2. ServiceInstance连接泄露问题");
        log.info("3. 网络超时处理问题");
        log.info("");
        log.info("修复措施:");
        log.info("1. 增加synchronized同步保护");
        log.info("2. 确保ServiceInstance正确关闭");
        log.info("3. 增加超时时间到60秒");
        log.info("4. 完整的异常处理和日志记录");
        
        // 测试不可达平台，验证修复效果
        testPreCheckWithUnreachableHost();
        
        log.info("=== VMware平台特有问题测试完成 ===");
    }
}
