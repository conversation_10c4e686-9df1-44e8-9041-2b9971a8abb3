package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.dal.platform.Platform;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * ZStack平台连接验证测试类 - 专门测试修改后的preCheck方法
 * 验证修改后的行为：
 * 1. 完成所有3次尝试
 * 2. 只在最后根据整体结果判断成功或失败
 * 3. 避免中间过程的"登录成功"日志误导
 */
@Slf4j
@SpringBootTest
public class ZsTackTokenImplPreCheckTest {

    @Test
    public void testPreCheckWithUnreachableHost() {
        // 测试不可达的平台
        Platform unreachablePlatform = new Platform();
        unreachablePlatform.setPlatformName("测试平台A-不可达");
        unreachablePlatform.setPlatformUrl("http://**************:8080");
        unreachablePlatform.setUsername("admin");
        unreachablePlatform.setPassword("password");
        
        ZsTackTokenImpl tokenImpl = new ZsTackTokenImpl();
        
        log.info("=== 测试修改后的preCheck方法 - 不可达平台 ===");
        log.info("开始测试不可达平台: {}", unreachablePlatform.getPlatformUrl());
        log.info("预期行为: 完成3次尝试后，最终判定为离线，不会出现误导性的'登录成功'日志");
        
        long startTime = System.currentTimeMillis();
        
        tokenImpl.preCheck(unreachablePlatform);
        
        long endTime = System.currentTimeMillis();
        log.info("不可达平台测试完成，耗时: {}ms", endTime - startTime);
        
        // 验证平台状态应该为离线
        assert unreachablePlatform.getState() == 1L : "不可达平台状态应该为离线(1)";
        log.info("✓ 验证通过: 不可达平台最终状态为离线");
    }

    @Test
    public void testPreCheckWithReachableHost() {
        // 测试可达的平台
        Platform reachablePlatform = new Platform();
        reachablePlatform.setPlatformName("测试平台B-可达");
        reachablePlatform.setPlatformUrl("http://**************:8080");
        reachablePlatform.setUsername("admin");
        reachablePlatform.setPassword("password");
        
        ZsTackTokenImpl tokenImpl = new ZsTackTokenImpl();
        
        log.info("=== 测试修改后的preCheck方法 - 可达平台 ===");
        log.info("开始测试可达平台: {}", reachablePlatform.getPlatformUrl());
        log.info("预期行为: 完成3次尝试，根据实际连接情况判定最终状态");
        
        long startTime = System.currentTimeMillis();
        
        tokenImpl.preCheck(reachablePlatform);
        
        long endTime = System.currentTimeMillis();
        log.info("可达平台测试完成，耗时: {}ms", endTime - startTime);
        
        // 注意：这里不验证状态，因为即使网络可达，认证可能失败
        log.info("平台最终状态: {} (0=在线, 1=离线)", reachablePlatform.getState());
    }

    @Test
    public void testBehaviorComparison() {
        log.info("=== 行为对比测试 ===");
        log.info("修改前的问题:");
        log.info("1. 每次尝试成功都会打印'登录成功'，即使平台实际不可达");
        log.info("2. 只要有一次成功就提前退出，可能导致网络不稳定时的误判");
        log.info("");
        log.info("修改后的改进:");
        log.info("1. 中间过程只打印debug级别日志，避免误导");
        log.info("2. 完成所有3次尝试，提供完整的统计信息");
        log.info("3. 最后根据成功/失败次数给出明确的判定结果");
        log.info("");
        
        // 测试不可达平台
        testPreCheckWithUnreachableHost();
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试可达平台
        testPreCheckWithReachableHost();
        
        log.info("=== 行为对比测试完成 ===");
    }
}
