package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.dal.platform.Platform;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * ZStack平台连接验证并发测试类
 * 专门测试多线程竞争问题的修复效果
 */
@Slf4j
@SpringBootTest
public class ZsTackTokenImplConcurrencyTest {

    @Test
    public void testConcurrentPreCheck() throws InterruptedException {
        log.info("=== 开始并发测试：验证多线程竞争问题修复 ===");
        
        // 创建两个不同的平台：一个不可达，一个可达
        Platform unreachablePlatform = createUnreachablePlatform();
        Platform reachablePlatform = createReachablePlatform();
        
        ZsTackTokenImpl tokenImpl = new ZsTackTokenImpl();
        
        // 使用CountDownLatch确保两个线程同时开始
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(2);
        
        ExecutorService executor = Executors.newFixedThreadPool(2);
        
        // 线程1：测试不可达平台
        executor.submit(() -> {
            try {
                startLatch.await(); // 等待开始信号
                log.info("线程1开始：测试不可达平台 {}", unreachablePlatform.getPlatformUrl());
                long startTime = System.currentTimeMillis();
                
                tokenImpl.preCheck(unreachablePlatform);
                
                long endTime = System.currentTimeMillis();
                log.info("线程1完成：不可达平台测试耗时 {}ms，最终状态: {}", 
                        endTime - startTime, unreachablePlatform.getState());
                        
                // 验证不可达平台应该为离线状态
                if (unreachablePlatform.getState() == 1L) {
                    log.info("✓ 线程1验证通过：不可达平台正确判定为离线");
                } else {
                    log.error("✗ 线程1验证失败：不可达平台被误判为在线！");
                }
                
            } catch (Exception e) {
                log.error("线程1异常: {}", e.getMessage());
            } finally {
                finishLatch.countDown();
            }
        });
        
        // 线程2：测试可达平台
        executor.submit(() -> {
            try {
                startLatch.await(); // 等待开始信号
                log.info("线程2开始：测试可达平台 {}", reachablePlatform.getPlatformUrl());
                long startTime = System.currentTimeMillis();
                
                tokenImpl.preCheck(reachablePlatform);
                
                long endTime = System.currentTimeMillis();
                log.info("线程2完成：可达平台测试耗时 {}ms，最终状态: {}", 
                        endTime - startTime, reachablePlatform.getState());
                        
                log.info("线程2结果：可达平台状态为 {} (0=在线, 1=离线)", reachablePlatform.getState());
                
            } catch (Exception e) {
                log.error("线程2异常: {}", e.getMessage());
            } finally {
                finishLatch.countDown();
            }
        });
        
        // 启动并发测试
        log.info("启动并发测试...");
        startLatch.countDown(); // 发出开始信号
        
        // 等待两个线程完成，最多等待5分钟
        boolean finished = finishLatch.await(5, TimeUnit.MINUTES);
        
        if (finished) {
            log.info("=== 并发测试完成 ===");
            log.info("修复前的问题：不可达平台可能被误判为在线（由于多线程竞争）");
            log.info("修复后的效果：每个平台都能得到正确的判定结果");
        } else {
            log.error("并发测试超时！");
        }
        
        executor.shutdown();
    }
    
    @Test
    public void testSequentialPreCheck() {
        log.info("=== 开始顺序测试：对比并发测试结果 ===");
        
        Platform unreachablePlatform = createUnreachablePlatform();
        Platform reachablePlatform = createReachablePlatform();
        
        ZsTackTokenImpl tokenImpl = new ZsTackTokenImpl();
        
        // 顺序测试不可达平台
        log.info("顺序测试不可达平台...");
        tokenImpl.preCheck(unreachablePlatform);
        log.info("顺序测试结果：不可达平台状态 = {}", unreachablePlatform.getState());
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 顺序测试可达平台
        log.info("顺序测试可达平台...");
        tokenImpl.preCheck(reachablePlatform);
        log.info("顺序测试结果：可达平台状态 = {}", reachablePlatform.getState());
        
        log.info("=== 顺序测试完成 ===");
    }
    
    private Platform createUnreachablePlatform() {
        Platform platform = new Platform();
        platform.setPlatformName("并发测试-不可达平台");
        platform.setPlatformUrl("http://172.16.100.108:8080");
        platform.setUsername("admin");
        platform.setPassword("password");
        return platform;
    }
    
    private Platform createReachablePlatform() {
        Platform platform = new Platform();
        platform.setPlatformName("并发测试-可达平台");
        platform.setPlatformUrl("http://172.16.100.205:8080");
        platform.setUsername("admin");
        platform.setPassword("password");
        return platform;
    }
}
