# ZStack异步线程安全调用指南

## 问题背景

在异步线程中调用ZStack API时，可能遇到以下问题：
1. **认证失败**: "access key id: xxx does not existed or disabled"
2. **Token过期**: sessionId认证模式下token失效
3. **Platform对象状态不一致**: 异步线程中Platform对象被其他线程修改

## 根本原因分析

### 1. ZStack SDK全局配置问题
- ZStack SDK使用全局静态配置(`ZSClient.configure`)
- 多线程环境下配置可能被覆盖
- 异步线程中的配置与主线程不一致

### 2. 认证信息传递问题
- **sessionId模式** (`akType=0`): 依赖`platform.getZsTackPlatform().getToken()`
- **AccessKey模式** (`akType=1`): 依赖`platform.getUsername()`和`platform.getPassword()`
- 异步线程中Platform对象状态可能不完整

### 3. 线程安全问题
- Platform对象在异步执行期间可能被修改
- 缓存中的认证信息可能过期
- 并发访问导致的竞争条件

## 解决方案

### 1. 使用异步安全的API方法

#### 推荐方式：使用`executeWithClientAsync`
```java
// 异步线程中的安全调用
taskExecutor.execute(() -> {
    try {
        // 使用异步安全的执行方法
        QuerySecurityGroupAction.Result result = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
            QuerySecurityGroupAction action = new QuerySecurityGroupAction();
            // setAuthentication会自动处理认证信息验证
            ZStackClientWrapper.setAuthentication(action, platform);
            return action.call();
        });
        
        // 处理结果
        processSecurityGroups(result);
        
    } catch (RuntimeException e) {
        log.error("异步调用ZStack API失败: {}", e.getMessage(), e);
        // 处理异常，可能需要重新获取认证信息
        handleAuthenticationError(platform, e);
    }
});
```

#### 或使用专门的异步方法：
```java
// 使用专门为异步设计的方法
taskExecutor.execute(() -> {
    try {
        QuerySecurityGroupAction.Result result = ZStackClientWrapper.querySecurityGroupsAsync(platform);
        processSecurityGroups(result);
    } catch (RuntimeException e) {
        log.error("异步查询安全组失败: {}", e.getMessage(), e);
        handleAuthenticationError(platform, e);
    }
});
```

### 2. 认证信息预检查

在异步任务开始前，确保认证信息有效：

```java
public void executeAsyncTask(Platform platform) {
    // 预检查平台状态
    if (platform.getState() != null && platform.getState() == 1) {
        log.warn("平台 {} 处于离线状态，跳过异步任务", platform.getPlatformName());
        return;
    }
    
    // 预检查认证信息
    if (platform.getAkType() == 0) {
        // sessionId模式 - 检查token
        if (platform.getZsTackPlatform() == null || 
            platform.getZsTackPlatform().getToken() == null || 
            platform.getZsTackPlatform().getToken().trim().isEmpty()) {
            
            log.warn("平台 {} token无效，重新获取认证信息", platform.getPlatformName());
            // 重新获取token
            refreshPlatformToken(platform);
        }
    } else {
        // AccessKey模式 - 检查AccessKey
        if (platform.getUsername() == null || platform.getPassword() == null) {
            log.error("平台 {} AccessKey信息不完整", platform.getPlatformName());
            return;
        }
    }
    
    // 执行异步任务
    taskExecutor.execute(() -> {
        ZStackClientWrapper.executeWithClientAsync(platform, () -> {
            // 执行具体的API调用
            return performZStackOperation(platform);
        });
    });
}
```

### 3. 错误处理和重试机制

```java
private void handleAuthenticationError(Platform platform, RuntimeException e) {
    String errorMessage = e.getMessage();
    
    if (errorMessage.contains("does not existed or disabled") || 
        errorMessage.contains("Token为空或无效")) {
        
        log.warn("平台 {} 认证失败，尝试重新获取认证信息: {}", 
            platform.getPlatformName(), errorMessage);
        
        try {
            // 重新获取认证信息
            refreshPlatformAuthentication(platform);
            
            // 可选：重试操作
            retryOperation(platform);
            
        } catch (Exception retryException) {
            log.error("重新获取认证信息失败: {}", retryException.getMessage(), retryException);
            // 标记平台为离线状态
            markPlatformOffline(platform);
        }
    }
}

private void refreshPlatformAuthentication(Platform platform) {
    // 获取token实现
    AbstractToken tokenImpl = TokenStrategyFactory.invoke(platform.getTypeCode());
    if (tokenImpl != null) {
        tokenImpl.token(platform);
    }
}
```

## 最佳实践

### 1. 异步任务设计原则
- **状态检查优先**: 在异步任务开始前检查平台状态
- **认证信息验证**: 确保认证信息完整有效
- **异常处理完善**: 提供完整的错误处理和恢复机制
- **日志记录详细**: 记录关键操作和错误信息

### 2. Platform对象管理
- **避免直接修改**: 在异步线程中避免修改Platform对象
- **使用深拷贝**: 必要时创建Platform对象的副本
- **缓存管理**: 及时更新缓存中的Platform信息

### 3. 性能优化
- **批量操作**: 尽可能批量处理多个API调用
- **连接复用**: 利用ZStackClientWrapper的连接缓存机制
- **超时控制**: 设置合理的超时时间避免长时间阻塞

## 常见错误和解决方案

### 错误1: "access key id: xxx does not existed or disabled"
**原因**: AccessKey认证信息无效或Platform对象状态不一致
**解决**: 使用`executeWithClientAsync`方法，包含完整的认证信息验证

### 错误2: "Token为空或无效"
**原因**: sessionId认证模式下token过期或未正确设置
**解决**: 在异步调用前重新获取token，或使用AccessKey认证模式

### 错误3: "ZsTackPlatform对象为空"
**原因**: Platform对象未正确初始化或在异步线程中被清空
**解决**: 确保在异步调用前Platform对象已完整初始化

## 迁移指南

### 现有代码迁移
将现有的异步ZStack API调用：
```java
// 旧方式 - 可能存在线程安全问题
taskExecutor.execute(() -> {
    ZStackClientWrapper.querySecurityGroups(platform);
});
```

迁移为：
```java
// 新方式 - 异步线程安全
taskExecutor.execute(() -> {
    try {
        ZStackClientWrapper.querySecurityGroupsAsync(platform);
        // 或使用通用方法
        // ZStackClientWrapper.executeWithClientAsync(platform, () -> {
        //     return ZStackClientWrapper.querySecurityGroups(platform);
        // });
    } catch (RuntimeException e) {
        log.error("异步调用失败: {}", e.getMessage(), e);
        handleAuthenticationError(platform, e);
    }
});
```

## 总结

通过使用增强的异步安全API和完善的错误处理机制，可以有效解决ZStack平台在异步线程中的认证问题。关键是：

1. **使用专门的异步安全方法**
2. **完善的认证信息预检查**
3. **健壮的错误处理和重试机制**
4. **详细的日志记录和监控**

这些改进确保了在多线程和异步环境下ZStack API调用的稳定性和可靠性。
