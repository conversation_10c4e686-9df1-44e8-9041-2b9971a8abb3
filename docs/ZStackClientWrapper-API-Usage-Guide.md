# ZStackClientWrapper 异步安全API使用指南

## 概述

ZStackClientWrapper提供了完整的ZStack SDK异步安全包装方法，解决了多线程环境下的认证和配置竞争问题。本文档详细介绍了所有可用的API方法及其使用方式。

## API分类

### 1. 基础数据收集相关API

#### 虚拟机相关
```java
// 查询所有虚拟机
QueryVmInstanceAction.Result vmResult = ZStackClientWrapper.queryVmInstancesAsync(platform);

// 获取虚拟机Guest Tools信息
GetVmGuestToolsInfoAction.Result toolsInfo = ZStackClientWrapper.getVmGuestToolsInfoWithUuidAsync(platform, vmUuid);
```

#### 主机相关
```java
// 查询所有主机
QueryHostAction.Result hostResult = ZStackClientWrapper.queryHostsAsync(platform);

// 查询特定条件的主机
List<String> conditions = List.of("uuid=" + hostUuid);
QueryHostAction.Result specificHost = ZStackClientWrapper.queryHostsWithConditionsAsync(platform, conditions);

// 查询主机网络接口
QueryHostNetworkInterfaceAction.Result nicResult = ZStackClientWrapper.queryHostNetworkInterfacesAsync(platform);

// 查询特定主机的网络接口
List<String> nicConditions = List.of("hostUuid=" + hostUuid);
QueryHostNetworkInterfaceAction.Result hostNics = ZStackClientWrapper.queryHostNetworkInterfacesWithConditionsAsync(platform, nicConditions);
```

#### 存储相关
```java
// 查询存储卷
QueryVolumeAction.Result volumeResult = ZStackClientWrapper.queryVolumesAsync(platform);

// 查询主存储
QueryPrimaryStorageAction.Result storageResult = ZStackClientWrapper.queryPrimaryStoragesAsync(platform);

// 查询卷快照
QueryVolumeSnapshotAction.Result snapshotResult = ZStackClientWrapper.queryVolumeSnapshotsAsync(platform);
```

#### 网络相关
```java
// 查询L2网络
QueryL2NetworkAction.Result l2Result = ZStackClientWrapper.queryL2NetworksAsync(platform);

// 查询L3网络
QueryL3NetworkAction.Result l3Result = ZStackClientWrapper.queryL3NetworksAsync(platform);

// 查询特定L3网络
List<String> l3Conditions = List.of("uuid=" + l3NetworkUuid);
QueryL3NetworkAction.Result specificL3 = ZStackClientWrapper.queryL3NetworksWithConditionsAsync(platform, l3Conditions);
```

#### 其他基础资源
```java
// 查询区域
QueryZoneAction.Result zoneResult = ZStackClientWrapper.queryZonesAsync(platform);

// 查询集群
QueryClusterAction.Result clusterResult = ZStackClientWrapper.queryClustersAsync(platform);

// 查询KVM集群
List<String> clusterConditions = List.of("hypervisorType=KVM");
QueryClusterAction.Result kvmClusters = ZStackClientWrapper.queryClustersWithConditionsAsync(platform, clusterConditions);

// 查询镜像
QueryImageAction.Result imageResult = ZStackClientWrapper.queryImagesAsync(platform);

// 查询VPC路由器
QueryVpcRouterAction.Result vpcResult = ZStackClientWrapper.queryVpcRoutersAsync(platform);

// 查询安全组
QuerySecurityGroupAction.Result secGroupResult = ZStackClientWrapper.querySecurityGroupsAsync(platform);
```

### 2. 指标数据收集相关API

#### 通用指标查询
```java
// 通用指标数据查询
List<String> labels = List.of("VMUuid=" + vmUuid);
GetMetricDataAction.Result metricResult = ZStackClientWrapper.getMetricDataAsync(platform, "ZStack/VM", "CPUAverageUsedUtilization", labels);
```

#### 虚拟机指标
```java
// CPU使用率
GetMetricDataAction.Result cpuUsage = ZStackClientWrapper.getVmMetricDataAsync(platform, "CPUAverageUsedUtilization", vmUuid);

// 内存使用率
GetMetricDataAction.Result memUsage = ZStackClientWrapper.getVmMetricDataAsync(platform, "MemoryUsedInPercent", vmUuid);

// 磁盘使用率
GetMetricDataAction.Result diskUsage = ZStackClientWrapper.getVmMetricDataAsync(platform, "DiskUsedCapacityInPercent", vmUuid);

// 网络流入
GetMetricDataAction.Result networkIn = ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkInBytes", vmUuid);

// 网络流出
GetMetricDataAction.Result networkOut = ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkOutBytes", vmUuid);
```

#### 主机指标
```java
// 主机CPU使用率
GetMetricDataAction.Result hostCpu = ZStackClientWrapper.getHostMetricDataAsync(platform, "CPUAverageUsedUtilization", hostUuid);

// 主机内存使用率
GetMetricDataAction.Result hostMem = ZStackClientWrapper.getHostMetricDataAsync(platform, "MemoryUsedInPercent", hostUuid);

// 主机磁盘使用率
GetMetricDataAction.Result hostDisk = ZStackClientWrapper.getHostMetricDataAsync(platform, "DiskAllUsedCapacityInPercent", hostUuid);

// 主机网络流量
GetMetricDataAction.Result hostNetIn = ZStackClientWrapper.getHostMetricDataAsync(platform, "NetworkAllInBytes", hostUuid);
GetMetricDataAction.Result hostNetOut = ZStackClientWrapper.getHostMetricDataAsync(platform, "NetworkAllOutBytes", hostUuid);
```

#### 存储指标
```java
// 主存储容量使用率
GetMetricDataAction.Result storageUsage = ZStackClientWrapper.getPrimaryStorageMetricDataAsync(platform, "TotalPhysicalCapacityInBytes", storageUuid);

// 主存储可用容量
GetMetricDataAction.Result storageAvailable = ZStackClientWrapper.getPrimaryStorageMetricDataAsync(platform, "AvailablePhysicalCapacityInBytes", storageUuid);
```

## 实际使用示例

### 示例1：基础数据收集实现迁移

**旧代码（存在线程安全问题）：**
```java
taskExecutor.execute(() -> {
    QueryHostAction hostAction = new QueryHostAction();
    if (platform.getAkType() == 0) {
        hostAction.sessionId = platform.getZsTackPlatform().getToken();
    } else {
        hostAction.accessKeyId = platform.getUsername();
        hostAction.accessKeySecret = platform.getPassword();
    }
    QueryHostAction.Result hostResult = hostAction.call();
    // 处理结果...
});
```

**新代码（线程安全）：**
```java
taskExecutor.execute(() -> {
    try {
        QueryHostAction.Result hostResult = ZStackClientWrapper.queryHostsAsync(platform);
        // 处理结果...
    } catch (RuntimeException e) {
        log.error("主机数据收集失败: {}", e.getMessage(), e);
        handleCollectionError(platform, e);
    }
});
```

### 示例2：指标数据收集实现迁移

**旧代码：**
```java
taskExecutor.execute(() -> {
    GetMetricDataAction action = new GetMetricDataAction();
    action.namespace = "ZStack/VM";
    action.metricName = "CPUAverageUsedUtilization";
    action.labels = List.of("VMUuid=" + vmUuid);
    if (platform.getAkType() == 0) {
        action.sessionId = platform.getZsTackPlatform().getToken();
    } else {
        action.accessKeyId = platform.getUsername();
        action.accessKeySecret = platform.getPassword();
    }
    GetMetricDataAction.Result result = action.call();
    // 处理结果...
});
```

**新代码：**
```java
taskExecutor.execute(() -> {
    try {
        GetMetricDataAction.Result result = ZStackClientWrapper.getVmMetricDataAsync(platform, "CPUAverageUsedUtilization", vmUuid);
        // 处理结果...
    } catch (RuntimeException e) {
        log.error("VM指标收集失败: {}", e.getMessage(), e);
        handleMetricError(platform, e);
    }
});
```

### 示例3：复杂查询条件的使用

```java
taskExecutor.execute(() -> {
    try {
        // 查询特定集群的主机
        List<String> clusterConditions = List.of("clusterUuid=" + clusterUuid);
        QueryHostAction.Result clusterHosts = ZStackClientWrapper.queryHostsWithConditionsAsync(platform, clusterConditions);
        
        // 查询特定主机的网络接口
        List<String> nicConditions = List.of("hostUuid=" + hostUuid);
        QueryHostNetworkInterfaceAction.Result hostNics = ZStackClientWrapper.queryHostNetworkInterfacesWithConditionsAsync(platform, nicConditions);
        
        // 处理结果...
    } catch (RuntimeException e) {
        log.error("复杂查询失败: {}", e.getMessage(), e);
        handleQueryError(platform, e);
    }
});
```

## 错误处理最佳实践

### 统一错误处理方法
```java
private void handleCollectionError(Platform platform, RuntimeException e) {
    String errorMessage = e.getMessage();
    
    if (errorMessage != null && (errorMessage.contains("session expired") || 
        errorMessage.contains("does not existed or disabled") ||
        errorMessage.contains("Token为空或无效"))) {
        
        log.warn("平台 {} 认证失败，尝试重新获取认证信息: {}", platform.getPlatformName(), errorMessage);
        
        try {
            // 重新获取认证信息
            AbstractToken tokenImpl = TokenStrategyFactory.invoke(platform.getTypeCode());
            if (tokenImpl != null) {
                tokenImpl.token(platform);
                // 可选：重试操作
                retryOperation(platform);
            }
        } catch (Exception retryException) {
            log.error("重新获取认证信息失败: {}", retryException.getMessage());
            markPlatformOffline(platform);
        }
        
    } else if (errorMessage != null && errorMessage.contains("网络不可达")) {
        log.warn("平台 {} 网络连接失败: {}", platform.getPlatformName(), errorMessage);
        markPlatformOffline(platform);
        
    } else {
        log.error("平台 {} 数据收集发生未知错误: {}", platform.getPlatformName(), errorMessage);
    }
}
```

## 性能优化建议

1. **批量查询**：尽可能使用批量查询减少API调用次数
2. **条件过滤**：使用条件查询减少不必要的数据传输
3. **异常处理**：实现完善的异常处理和重试机制
4. **日志记录**：添加适当的日志记录便于问题排查

## 总结

通过使用ZStackClientWrapper提供的异步安全API方法，您可以：

1. **彻底解决异步环境下的线程安全问题**
2. **简化代码实现，减少重复的认证设置代码**
3. **获得统一的错误处理和重试机制**
4. **提高系统的稳定性和可靠性**
5. **便于代码维护和扩展**

所有新的API方法都遵循相同的设计模式，确保了一致性和可预测性。建议逐步将现有代码迁移到新的异步安全API，以获得更好的稳定性和性能。
