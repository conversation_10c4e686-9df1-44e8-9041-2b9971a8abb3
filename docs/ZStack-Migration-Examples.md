# ZStack异步安全API迁移示例

## 迁移概述

本文档提供了将现有ZStack API调用迁移到异步安全方法的具体示例。每个示例都展示了迁移前后的代码对比，以及需要注意的要点。

## 基础数据收集模块迁移示例

### 1. ZsTackBasicHostDataImpl 迁移

**迁移前：**
```java
taskExecutor.execute(() -> {
    String token = platform.getZsTackPlatform().getToken();
    if (token == null) {
        log.error("平台 {} token为空", platform.getPlatformName());
        return;
    }
    
    QueryZoneAction zoneAction = new QueryZoneAction();
    if (platform.getAkType() == 0) {
        zoneAction.sessionId = token;
    } else {
        zoneAction.accessKeyId = platform.getUsername();
        zoneAction.accessKeySecret = platform.getPassword();
    }
    List<?> regionList = zoneAction.call().value.inventories;
    
    QueryHostAction hostAction = new QueryHostAction();
    if (platform.getAkType() == 0) {
        hostAction.sessionId = token;
    } else {
        hostAction.accessKeyId = platform.getUsername();
        hostAction.accessKeySecret = platform.getPassword();
    }
    QueryHostAction.Result hostResult = hostAction.call();
    // 处理结果...
});
```

**迁移后：**
```java
taskExecutor.execute(() -> {
    try {
        // 使用异步安全的API调用
        QueryZoneAction.Result zoneResult = ZStackClientWrapper.queryZonesAsync(platform);
        List<?> regionList = zoneResult.value.inventories;
        
        QueryHostAction.Result hostResult = ZStackClientWrapper.queryHostsAsync(platform);
        
        // 处理结果...
        processHostData(hostResult, regionList, platform);
        
    } catch (RuntimeException e) {
        log.error("平台 {} 主机数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
        handleCollectionError(platform, e);
    }
});
```

### 2. ZsTackBasicVmDataImpl 迁移

**迁移前：**
```java
taskExecutor.execute(() -> {
    String token = platform.getZsTackPlatform().getToken();
    if (token == null) {
        log.error("平台 {} token为空", platform.getPlatformName());
        return;
    }
    
    QueryZoneAction zoneAction = new QueryZoneAction();
    if (platform.getAkType()==0){
        zoneAction.sessionId = token;
    }else {
        zoneAction.accessKeyId = platform.getUsername();
        zoneAction.accessKeySecret = platform.getPassword();
    }
    List<?> regionList = zoneAction.call().value.inventories;
    
    QueryVmInstanceAction action = new QueryVmInstanceAction();
    if (platform.getAkType()==0){
        action.sessionId = token;
    }else {
        action.accessKeyId = platform.getUsername();
        action.accessKeySecret = platform.getPassword();
    }
    QueryVmInstanceAction.Result vmResult = action.call();
    // 处理结果...
});
```

**迁移后：**
```java
taskExecutor.execute(() -> {
    try {
        QueryZoneAction.Result zoneResult = ZStackClientWrapper.queryZonesAsync(platform);
        List<?> regionList = zoneResult.value.inventories;
        
        QueryVmInstanceAction.Result vmResult = ZStackClientWrapper.queryVmInstancesAsync(platform);
        
        // 处理结果...
        processVmData(vmResult, regionList, platform);
        
    } catch (RuntimeException e) {
        log.error("平台 {} 虚拟机数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
        handleCollectionError(platform, e);
    }
});
```

### 3. ZsTackBasicVolumeInfoDataImpl 迁移

**迁移前：**
```java
private void collectData(Platform platform, List<VolumeInfoData> volumeInfoDataList) {
    QueryVolumeAction action = new QueryVolumeAction();
    action.conditions = asList();
    if (platform.getAkType()==0){
        action.sessionId = platform.getZsTackPlatform().getToken();
    }else {
        action.accessKeyId = platform.getUsername();
        action.accessKeySecret = platform.getPassword();
    }
    QueryVolumeAction.Result res = action.call();

    QueryVmInstanceAction vmaction = new QueryVmInstanceAction();
    if (platform.getAkType()==0){
        vmaction.sessionId = platform.getZsTackPlatform().getToken();
    }else {
        vmaction.accessKeyId = platform.getUsername();
        vmaction.accessKeySecret = platform.getPassword();
    }
    QueryVmInstanceAction.Result vmRes = vmaction.call();
    // 处理结果...
}
```

**迁移后：**
```java
private void collectData(Platform platform, List<VolumeInfoData> volumeInfoDataList) {
    try {
        // 使用异步安全的API调用
        QueryVolumeAction.Result volumeResult = ZStackClientWrapper.queryVolumesAsync(platform);
        QueryVmInstanceAction.Result vmResult = ZStackClientWrapper.queryVmInstancesAsync(platform);
        
        // 处理结果...
        processVolumeData(volumeResult, vmResult, platform, volumeInfoDataList);
        
    } catch (RuntimeException e) {
        log.error("平台 {} 存储卷数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
        throw e; // 重新抛出异常，让上层处理
    }
}
```

### 4. ZsTackBasicNetDataImpl 迁移

**迁移前：**
```java
private List<NetWorkL2Data> collectDataL2(Platform platform) {
    List<NetWorkL2Data> dataList = new ArrayList<>();
    String token = platform.getZsTackPlatform().getToken();
    QueryL2NetworkAction action = new QueryL2NetworkAction();
    if (platform.getAkType()==0){
        action.sessionId = token;
    }else {
        action.accessKeyId = platform.getUsername();
        action.accessKeySecret = platform.getPassword();
    }
    QueryL2NetworkAction.Result res = action.call();
    // 处理结果...
}

private List<NetWorkL3Data> collectDataL3(Platform platform, List<NetWorkL2Data> listL2) {
    List<NetWorkL3Data> dataList = new ArrayList<>();
    String token = platform.getZsTackPlatform().getToken();
    QueryL3NetworkAction l3action = new QueryL3NetworkAction();
    if (platform.getAkType()==0){
        l3action.sessionId = token;
    }else{
        l3action.accessKeyId = platform.getUsername();
        l3action.accessKeySecret = platform.getPassword();
    }
    QueryL3NetworkAction.Result l3res = l3action.call();
    // 处理结果...
}
```

**迁移后：**
```java
private List<NetWorkL2Data> collectDataL2(Platform platform) {
    try {
        QueryL2NetworkAction.Result result = ZStackClientWrapper.queryL2NetworksAsync(platform);
        return processL2NetworkData(result, platform);
    } catch (RuntimeException e) {
        log.error("平台 {} L2网络数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
        throw e;
    }
}

private List<NetWorkL3Data> collectDataL3(Platform platform, List<NetWorkL2Data> listL2) {
    try {
        QueryL3NetworkAction.Result result = ZStackClientWrapper.queryL3NetworksAsync(platform);
        return processL3NetworkData(result, listL2, platform);
    } catch (RuntimeException e) {
        log.error("平台 {} L3网络数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
        throw e;
    }
}
```

## 指标数据收集模块迁移示例

### 1. ZsTackCpuImpl 迁移

**迁移前：**
```java
private List<MetricData> getMetricData(List<MonitorInfo> uuids, String token, String namespace, String labelPrefix, Platform platform, String type) {
    List<MetricData> metricDataList = new ArrayList<>();
    for (MonitorInfo monitorInfo : uuids) {
        GetMetricDataAction action = new GetMetricDataAction();
        action.namespace = namespace;
        action.metricName = "CPUAverageUsedUtilization";
        action.labels = List.of(labelPrefix + monitorInfo.getUuid());
        if (platform.getAkType() == 0) {
            action.sessionId = token;
        } else {
            action.accessKeyId = platform.getUsername();
            action.accessKeySecret = platform.getPassword();
        }
        GetMetricDataAction.Result result = action.call();
        // 处理结果...
    }
    return metricDataList;
}
```

**迁移后：**
```java
private List<MetricData> getMetricData(List<MonitorInfo> uuids, Platform platform, String type) {
    List<MetricData> metricDataList = new ArrayList<>();
    for (MonitorInfo monitorInfo : uuids) {
        try {
            GetMetricDataAction.Result result;
            if ("vm".equals(type)) {
                result = ZStackClientWrapper.getVmMetricDataAsync(platform, "CPUAverageUsedUtilization", monitorInfo.getUuid());
            } else {
                result = ZStackClientWrapper.getHostMetricDataAsync(platform, "CPUAverageUsedUtilization", monitorInfo.getUuid());
            }
            
            // 处理结果...
            processMetricResult(result, monitorInfo, metricDataList);
            
        } catch (RuntimeException e) {
            log.error("平台 {} {}({}) CPU指标收集失败: {}", 
                platform.getPlatformName(), type, monitorInfo.getUuid(), e.getMessage());
            // 继续处理其他资源，不中断整个流程
        }
    }
    return metricDataList;
}
```

### 2. ZsTackMemImpl 迁移

**迁移前：**
```java
private List<MetricData> getMetricData(List<MonitorInfo> uuids, String token, String namespace, String labelPrefix, Platform platform, String code, List<MetricData> metricDataList, String type) {
    for (MonitorInfo monitorInfo : uuids) {
        GetMetricDataAction action = new GetMetricDataAction();
        action.namespace = namespace;
        action.metricName = getMetricNameByCode(code);
        action.labels = List.of(labelPrefix + monitorInfo.getUuid());
        if (platform.getAkType() == 0) {
            action.sessionId = token;
        } else {
            action.accessKeyId = platform.getUsername();
            action.accessKeySecret = platform.getPassword();
        }
        GetMetricDataAction.Result result = action.call();
        // 处理结果...
    }
    return metricDataList;
}
```

**迁移后：**
```java
private List<MetricData> getMetricData(List<MonitorInfo> uuids, Platform platform, String code, List<MetricData> metricDataList, String type) {
    String metricName = getMetricNameByCode(code);
    
    for (MonitorInfo monitorInfo : uuids) {
        try {
            GetMetricDataAction.Result result;
            if ("vm".equals(type)) {
                result = ZStackClientWrapper.getVmMetricDataAsync(platform, metricName, monitorInfo.getUuid());
            } else {
                result = ZStackClientWrapper.getHostMetricDataAsync(platform, metricName, monitorInfo.getUuid());
            }
            
            // 处理结果...
            processMemoryMetricResult(result, monitorInfo, code, metricDataList);
            
        } catch (RuntimeException e) {
            log.error("平台 {} {}({}) 内存指标({})收集失败: {}", 
                platform.getPlatformName(), type, monitorInfo.getUuid(), code, e.getMessage());
            // 继续处理其他资源
        }
    }
    return metricDataList;
}
```

## 带条件查询的迁移示例

### 1. 主机网络接口查询迁移

**迁移前：**
```java
QueryHostNetworkInterfaceAction macAction = new QueryHostNetworkInterfaceAction();
macAction.conditions = asList("hostUuid=" + uuid);
if (platform.getAkType() == 0) {
    macAction.sessionId = token;
} else {
    macAction.accessKeyId = platform.getUsername();
    macAction.accessKeySecret = platform.getPassword();
}
QueryHostNetworkInterfaceAction.Result macResult = macAction.call();
```

**迁移后：**
```java
try {
    List<String> conditions = List.of("hostUuid=" + uuid);
    QueryHostNetworkInterfaceAction.Result macResult = ZStackClientWrapper.queryHostNetworkInterfacesWithConditionsAsync(platform, conditions);
    // 处理结果...
} catch (RuntimeException e) {
    log.error("平台 {} 主机({})网络接口查询失败: {}", platform.getPlatformName(), uuid, e.getMessage());
    // 处理错误...
}
```

### 2. 集群查询迁移

**迁移前：**
```java
QueryClusterAction clusters = new QueryClusterAction();
clusters.conditions = asList("hypervisorType=KVM");
if (platform.getAkType()==0){
    clusters.sessionId = token;
}else{
    clusters.accessKeyId = platform.getUsername();
    clusters.accessKeySecret = platform.getPassword();
}
QueryClusterAction.Result clusterResult = clusters.call();
```

**迁移后：**
```java
try {
    List<String> conditions = List.of("hypervisorType=KVM");
    QueryClusterAction.Result clusterResult = ZStackClientWrapper.queryClustersWithConditionsAsync(platform, conditions);
    // 处理结果...
} catch (RuntimeException e) {
    log.error("平台 {} KVM集群查询失败: {}", platform.getPlatformName(), e.getMessage());
    // 处理错误...
}
```

## 迁移检查清单

### 迁移前检查
- [ ] 识别所有异步执行的ZStack API调用
- [ ] 确认当前的错误处理机制
- [ ] 备份现有代码

### 迁移过程
- [ ] 替换API调用为ZStackClientWrapper方法
- [ ] 移除手动的认证设置代码
- [ ] 添加try-catch异常处理
- [ ] 更新日志记录

### 迁移后验证
- [ ] 功能测试：确保数据收集正常
- [ ] 并发测试：验证多线程环境下的稳定性
- [ ] 错误处理测试：验证异常情况的处理
- [ ] 性能测试：确保性能没有显著下降

## 常见问题和解决方案

### Q1: 迁移后出现"平台离线"错误
**A:** 检查平台状态验证逻辑，确保`platform.getState()`返回正确的状态值。

### Q2: 某些API调用仍然失败
**A:** 检查是否使用了正确的异步安全方法，确保所有ZStack API调用都通过ZStackClientWrapper。

### Q3: 性能下降
**A:** 检查是否有不必要的重复API调用，考虑使用批量查询优化。

### Q4: 日志中出现大量认证失败
**A:** 检查token刷新机制，确保认证信息及时更新。

## 总结

通过系统性的迁移，您可以：

1. **彻底解决异步线程安全问题**
2. **简化代码维护**
3. **提高系统稳定性**
4. **获得统一的错误处理**

建议按模块逐步迁移，每完成一个模块就进行充分测试，确保迁移的稳定性和可靠性。
