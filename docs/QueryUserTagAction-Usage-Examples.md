# QueryUserTagAction 异步方法使用指南

## 概述

本文档介绍如何使用ZStackClientWrapper中新实现的QueryUserTagAction异步方法，这些方法提供了线程安全的用户标签查询功能。

## 新增方法

### 1. `queryUserTagsAsync(Platform platform, String resourceUuid, String resourceType)`

根据资源UUID和资源类型查询用户标签的异步方法。

### 2. `queryUserTagsWithConditionsAsync(Platform platform, List<String> conditions)`

支持自定义查询条件的用户标签查询异步方法。

## 使用示例

### 基础用法：查询卷的用户标签

```java
// 异步查询卷的用户标签
taskExecutor.execute(() -> {
    try {
        String volumeUuid = "volume-uuid-123";
        String resourceType = "VolumeVO";
        
        QueryUserTagAction.Result result = ZStackClientWrapper.queryUserTagsAsync(
            platform, 
            volumeUuid, 
            resourceType
        );
        
        // 处理查询结果
        List<String> tagValues = new ArrayList<>();
        for (Object inventory : result.value.inventories) {
            JsonObject tagDO = GsonUtil.GSON.toJsonTree(inventory)
                .getAsJsonObject()
                .getAsJsonObject("tagPattern");
            String tag = getStringFromJson(tagDO, "name", "");
            String taguuid = getStringFromJson(tagDO, "uuid", "");
            if (StrUtil.isNotEmpty(tag) && StrUtil.isNotEmpty(taguuid)) {
                tagValues.add(tag + "&" + taguuid);
            }
        }
        
        String resultTag = tagValues.isEmpty() ? "" : 
            (tagValues.size() == 1 ? tagValues.get(0) : String.join(",", tagValues));
        
        log.info("卷 {} 的标签: {}", volumeUuid, resultTag);
        
    } catch (RuntimeException e) {
        log.error("查询用户标签失败: {}", e.getMessage(), e);
    }
});
```

### 查询虚拟机的用户标签

```java
// 异步查询虚拟机的用户标签
taskExecutor.execute(() -> {
    try {
        String vmUuid = "vm-uuid-456";
        
        QueryUserTagAction.Result result = ZStackClientWrapper.queryUserTagsAsync(
            platform, 
            vmUuid, 
            "VmInstanceVO"
        );
        
        // 处理结果...
        processVmTags(result);
        
    } catch (RuntimeException e) {
        log.error("查询虚拟机标签失败: {}", e.getMessage(), e);
    }
});
```

### 查询主机的用户标签

```java
// 异步查询主机的用户标签
taskExecutor.execute(() -> {
    try {
        String hostUuid = "host-uuid-789";
        
        QueryUserTagAction.Result result = ZStackClientWrapper.queryUserTagsAsync(
            platform, 
            hostUuid, 
            "HostVO"
        );
        
        // 处理结果...
        processHostTags(result);
        
    } catch (RuntimeException e) {
        log.error("查询主机标签失败: {}", e.getMessage(), e);
    }
});
```

### 使用自定义条件查询

```java
// 使用自定义条件查询用户标签
taskExecutor.execute(() -> {
    try {
        List<String> conditions = Arrays.asList(
            "resourceUuid=" + resourceUuid,
            "resourceType=VolumeVO",
            "tag.name=environment"  // 额外的标签名称过滤条件
        );
        
        QueryUserTagAction.Result result = ZStackClientWrapper.queryUserTagsWithConditionsAsync(
            platform, 
            conditions
        );
        
        // 处理结果...
        processTagsWithConditions(result);
        
    } catch (RuntimeException e) {
        log.error("条件查询用户标签失败: {}", e.getMessage(), e);
    }
});
```

## 认证方式支持

这些方法自动支持两种认证方式：

### SessionId认证（akType=0）
```java
// 当platform.getAkType() == 0时，自动使用sessionId认证
// 认证信息来源：platform.getZsTackPlatform().getToken()
```

### AccessKey认证（akType!=0）
```java
// 当platform.getAkType() != 0时，自动使用AccessKey认证
// 认证信息来源：
// - accessKeyId: platform.getUsername()
// - accessKeySecret: platform.getPassword()
```

## 线程安全特性

- **多平台隔离**：每个平台使用独立的配置和锁机制
- **原子性操作**：配置和API调用在同一个锁保护下执行
- **异步安全**：使用`executeWithClientAsync`确保异步环境下的线程安全

## 错误处理

```java
try {
    QueryUserTagAction.Result result = ZStackClientWrapper.queryUserTagsAsync(
        platform, resourceUuid, resourceType
    );
    // 处理成功结果
} catch (RuntimeException e) {
    // 处理各种异常情况：
    // - 认证失败
    // - 网络连接问题
    // - 平台配置错误
    // - API调用超时
    log.error("查询用户标签失败: 平台={}, 资源={}, 错误={}", 
             platform.getPlatformName(), resourceUuid, e.getMessage(), e);
}
```

## 迁移指南

### 迁移前（旧代码）
```java
QueryUserTagAction actionTag = new QueryUserTagAction();
actionTag.conditions = asList("resourceUuid=" + uuid,"resourceType=VolumeVO");
if (platform.getAkType()==0){
    actionTag.sessionId = platform.getZsTackPlatform().getToken();
}else {
    actionTag.accessKeyId = platform.getUsername();
    actionTag.accessKeySecret = platform.getPassword();
}
QueryUserTagAction.Result restag = actionTag.call();
```

### 迁移后（新代码）
```java
QueryUserTagAction.Result restag = ZStackClientWrapper.queryUserTagsAsync(
    platform, uuid, "VolumeVO"
);
```

## 性能优化建议

1. **批量查询**：如果需要查询多个资源的标签，考虑使用条件查询一次性获取
2. **结果缓存**：对于不经常变化的标签信息，可以考虑适当缓存
3. **异步处理**：在异步线程中调用，避免阻塞主线程

## 注意事项

1. **参数验证**：方法会自动处理null参数，但建议传入有效的resourceUuid和resourceType
2. **异常处理**：务必捕获RuntimeException并进行适当的错误处理
3. **平台配置**：确保Platform对象包含正确的认证信息和URL配置
