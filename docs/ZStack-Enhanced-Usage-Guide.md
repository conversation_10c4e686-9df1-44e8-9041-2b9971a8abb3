# ZStack多平台支持使用指南

## 概述

现在 `ZStackClientWrapper` 已经增强支持多平台并发访问，解决了多平台配置冲突问题。您可以选择使用原有方法或新的多平台方法。

## 核心改进

### 1. 向后兼容
- ✅ 所有原有方法保持不变，继续可用
- ✅ 新增多平台方法，方法名增加 "MultiPlatform" 后缀
- ✅ 可以在同一项目中混合使用新旧方法

### 2. 新增功能
- ✅ 真正的多平台并发支持
- ✅ 独立的平台实例管理
- ✅ 智能配置切换机制
- ✅ 平台实例监控和管理

## 使用方式对比

### 原有方法（继续可用）
```java
// 原有的单平台方法
QueryVmInstanceAction.Result result1 = ZStackClientWrapper.queryVmInstancesAsync(platform1);
QueryHostAction.Result result2 = ZStackClientWrapper.queryHostsAsync(platform2);
```

### 新的多平台方法（推荐）
```java
// 新的多平台方法 - 支持真正并发
QueryVmInstanceAction.Result result1 = ZStackClientWrapper.queryVmInstancesMultiPlatform(platform1);
QueryHostAction.Result result2 = ZStackClientWrapper.queryHostsMultiPlatform(platform2);
```

## 多平台并发示例

### 示例1：基础多平台并发
```java
// 现在可以真正并发访问不同平台，不会有配置冲突
CompletableFuture.allOf(
    CompletableFuture.runAsync(() -> {
        // 平台1的操作
        QueryVmInstanceAction.Result vms = ZStackClientWrapper.queryVmInstancesMultiPlatform(platform1);
        log.info("平台1 VM数量: {}", vms.inventories.size());
    }),
    CompletableFuture.runAsync(() -> {
        // 平台2的操作（同时进行，不会冲突）
        QueryHostAction.Result hosts = ZStackClientWrapper.queryHostsMultiPlatform(platform2);
        log.info("平台2 主机数量: {}", hosts.inventories.size());
    }),
    CompletableFuture.runAsync(() -> {
        // 平台3的操作（同时进行，不会冲突）
        QueryZoneAction.Result zones = ZStackClientWrapper.queryZonesMultiPlatform(platform3);
        log.info("平台3 区域数量: {}", zones.inventories.size());
    })
).join();
```

### 示例2：复杂的多平台数据收集
```java
public void collectDataFromMultiplePlatforms(List<Platform> platforms) {
    // 并发收集所有平台的基础数据
    CompletableFuture<Void>[] futures = platforms.stream()
        .map(platform -> CompletableFuture.runAsync(() -> {
            try {
                // 验证连接
                boolean isValid = ZStackClientWrapper.validateLoginMultiPlatform(platform);
                if (!isValid) {
                    log.error("平台 {} 连接失败", platform.getPlatformName());
                    return;
                }
                
                // 收集基础数据
                QueryVmInstanceAction.Result vms = ZStackClientWrapper.queryVmInstancesMultiPlatform(platform);
                QueryHostAction.Result hosts = ZStackClientWrapper.queryHostsMultiPlatform(platform);
                QueryZoneAction.Result zones = ZStackClientWrapper.queryZonesMultiPlatform(platform);
                
                log.info("平台 {} 数据收集完成: VM={}, 主机={}, 区域={}", 
                    platform.getPlatformName(),
                    vms.inventories.size(),
                    hosts.inventories.size(), 
                    zones.inventories.size());
                    
            } catch (Exception e) {
                log.error("平台 {} 数据收集失败: {}", platform.getPlatformName(), e.getMessage());
            }
        }))
        .toArray(CompletableFuture[]::new);
    
    // 等待所有任务完成
    CompletableFuture.allOf(futures).join();
    
    // 查看平台实例统计
    String stats = ZStackClientWrapper.getPlatformInstanceStats();
    log.info("平台实例统计:\n{}", stats);
}
```

## 新增的API方法

### 基础资源查询
```java
// 虚拟机查询
QueryVmInstanceAction.Result vms = ZStackClientWrapper.queryVmInstancesMultiPlatform(platform);

// 主机查询
QueryHostAction.Result hosts = ZStackClientWrapper.queryHostsMultiPlatform(platform);

// 区域查询
QueryZoneAction.Result zones = ZStackClientWrapper.queryZonesMultiPlatform(platform);

// 安全组查询
QuerySecurityGroupAction.Result securityGroups = ZStackClientWrapper.querySecurityGroupsMultiPlatform(platform);

// 集群查询
QueryClusterAction.Result clusters = ZStackClientWrapper.queryClustersMultiPlatform(platform);
```

### 存储资源查询
```java
// 存储卷查询
QueryVolumeAction.Result volumes = ZStackClientWrapper.queryVolumesMultiPlatform(platform);

// 主存储查询
QueryPrimaryStorageAction.Result primaryStorages = ZStackClientWrapper.queryPrimaryStoragesMultiPlatform(platform);

// 镜像查询
QueryImageAction.Result images = ZStackClientWrapper.queryImagesMultiPlatform(platform);
```

### 网络资源查询
```java
// L2网络查询
QueryL2NetworkAction.Result l2Networks = ZStackClientWrapper.queryL2NetworksMultiPlatform(platform);

// L3网络查询
QueryL3NetworkAction.Result l3Networks = ZStackClientWrapper.queryL3NetworksMultiPlatform(platform);
```

### 验证和管理
```java
// 验证登录状态
boolean isValid = ZStackClientWrapper.validateLoginMultiPlatform(platform);

// 获取平台实例统计
String stats = ZStackClientWrapper.getPlatformInstanceStats();

// 清理特定平台实例
ZStackClientWrapper.clearPlatformInstance(platform);

// 清理所有平台实例
ZStackClientWrapper.clearAllPlatformInstances();
```

## 迁移建议

### 渐进式迁移
1. **保持现有代码不变**：原有方法继续工作
2. **新功能使用多平台方法**：新开发的功能使用 `*MultiPlatform` 方法
3. **逐步替换关键路径**：对于需要多平台并发的场景，逐步替换为多平台方法

### 迁移示例
```java
// 原有代码（继续可用）
taskExecutor.execute(() -> {
    QueryVmInstanceAction.Result result = ZStackClientWrapper.queryVmInstancesAsync(platform);
    // 处理结果...
});

// 迁移后的代码（推荐用于多平台场景）
taskExecutor.execute(() -> {
    QueryVmInstanceAction.Result result = ZStackClientWrapper.queryVmInstancesMultiPlatform(platform);
    // 处理结果...
});
```

## 性能优化

### 1. 配置缓存
- 每个平台的配置只在首次访问或切换时进行
- 避免重复的 `ZSClient.configure()` 调用

### 2. 智能切换
- 只有当需要切换到不同平台时才重新配置
- 跟踪当前活跃的平台实例

### 3. 并发优化
- 不同平台可以真正并行执行
- 平台级别的锁机制，避免全局锁瓶颈

## 监控和管理

### 查看平台实例状态
```java
String stats = ZStackClientWrapper.getPlatformInstanceStats();
System.out.println(stats);

// 输出示例：
// ZStack多平台实例统计:
// 总实例数: 3
// - 平台: 1_http://*************:8080_0, 配置状态: true, 最后配置时间: 1706601234567
// - 平台: 2_http://*************:8080_1, 配置状态: true, 最后配置时间: 1706601234568
// - 平台: 3_https://192.168.1.300:443_1, 配置状态: true, 最后配置时间: 1706601234569
// 当前活跃实例: 1_http://*************:8080_0
```

### 清理实例缓存
```java
// 应用关闭时清理所有实例
@PreDestroy
public void cleanup() {
    ZStackClientWrapper.clearAllPlatformInstances();
}

// 或者清理特定平台
ZStackClientWrapper.clearPlatformInstance(platform);
```

## 最佳实践

### 1. 选择合适的方法
- **单平台场景**：可以继续使用原有方法
- **多平台并发场景**：使用新的 `*MultiPlatform` 方法
- **混合场景**：可以在同一项目中混合使用

### 2. 错误处理
```java
try {
    QueryVmInstanceAction.Result result = ZStackClientWrapper.queryVmInstancesMultiPlatform(platform);
    // 处理成功结果
} catch (RuntimeException e) {
    if (e.getMessage().contains("认证失败")) {
        // 处理认证错误
    } else if (e.getMessage().contains("网络")) {
        // 处理网络错误
    } else {
        // 处理其他错误
    }
}
```

### 3. 资源管理
- 定期检查平台实例统计
- 在应用关闭时清理实例缓存
- 监控内存使用情况

## 总结

通过在现有 `ZStackClientWrapper` 中增加多平台支持，您现在可以：

1. ✅ **继续使用原有代码**：完全向后兼容
2. ✅ **解决多平台冲突**：新方法支持真正的多平台并发
3. ✅ **渐进式迁移**：可以逐步迁移到新方法
4. ✅ **提高性能**：智能配置切换和并发优化
5. ✅ **便于管理**：实例监控和清理功能

这个解决方案既解决了多平台配置冲突的问题，又保持了完全的向后兼容性，让您可以根据实际需要选择使用原有方法或新的多平台方法。
