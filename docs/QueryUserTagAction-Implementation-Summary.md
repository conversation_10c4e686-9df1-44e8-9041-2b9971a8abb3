# QueryUserTagAction 异步方法实现总结

## 实现概述

已成功在ZStackClientWrapper类中为QueryUserTagAction SDK API实现了异步请求方法，完全符合用户要求。

## 新增方法

### 1. `queryUserTagsAsync(Platform platform, String resourceUuid, String resourceType)`

**功能**：根据资源UUID和资源类型查询用户标签的异步方法

**参数**：
- `platform`: 平台信息对象
- `resourceUuid`: 资源UUID（如卷、虚拟机、主机的UUID）
- `resourceType`: 资源类型（如"VolumeVO", "VmInstanceVO", "HostVO"等）

**返回值**：`QueryUserTagAction.Result`

### 2. `queryUserTagsWithConditionsAsync(Platform platform, List<String> conditions)`

**功能**：支持自定义查询条件的用户标签查询异步方法

**参数**：
- `platform`: 平台信息对象
- `conditions`: 查询条件列表

**返回值**：`QueryUserTagAction.Result`

## 实现特性

### ✅ 认证方式支持
- **自动认证选择**：根据`platform.getAkType()`值自动选择认证方式
- **SessionId认证**：当`akType=0`时，使用`platform.getZsTackPlatform().getToken()`
- **AccessKey认证**：当`akType!=0`时，使用`platform.getUsername()`和`platform.getPassword()`

### ✅ 线程安全性
- **使用`executeWithClientAsync`**：确保异步环境下的线程安全
- **多平台隔离**：每个平台使用独立的配置和锁机制
- **原子性操作**：配置和API调用在同一个锁保护下执行
- **基于之前的多线程竞态条件修复经验**：采用了与其他平台实现相同的同步机制

### ✅ 代码一致性
- **遵循现有模式**：与ZStackClientWrapper中其他SDK API的异步实现模式完全一致
- **统一错误处理**：使用相同的异常处理和超时处理机制
- **统一命名规范**：方法命名遵循现有的`*Async`后缀规范
- **统一认证处理**：使用`setAuthentication`方法统一处理认证信息

## 代码位置

**文件**：`src/main/java/cn/coder/zj/module/collector/collect/token/zstack/ZStackClientWrapper.java`

**行号**：591-641行（在"带条件查询的异步安全方法"部分）

## 使用示例

### 基础用法
```java
// 查询卷的用户标签
QueryUserTagAction.Result result = ZStackClientWrapper.queryUserTagsAsync(
    platform, "volume-uuid-123", "VolumeVO"
);

// 查询虚拟机的用户标签
QueryUserTagAction.Result vmResult = ZStackClientWrapper.queryUserTagsAsync(
    platform, "vm-uuid-456", "VmInstanceVO"
);

// 查询主机的用户标签
QueryUserTagAction.Result hostResult = ZStackClientWrapper.queryUserTagsAsync(
    platform, "host-uuid-789", "HostVO"
);
```

### 自定义条件查询
```java
List<String> conditions = Arrays.asList(
    "resourceUuid=" + resourceUuid,
    "resourceType=VolumeVO",
    "tag.name=environment"
);

QueryUserTagAction.Result result = ZStackClientWrapper.queryUserTagsWithConditionsAsync(
    platform, conditions
);
```

## 测试覆盖

**测试文件**：`src/test/java/cn/coder/zj/module/collector/collect/token/zstack/QueryUserTagActionTest.java`

**测试覆盖**：
- SessionId认证模式测试
- AccessKey认证模式测试
- 自定义条件查询测试
- 空参数处理测试
- 方法签名验证测试

## 文档支持

**使用指南**：`docs/QueryUserTagAction-Usage-Examples.md`
- 详细的使用示例
- 认证方式说明
- 迁移指南
- 性能优化建议
- 错误处理最佳实践

## 技术实现细节

### 线程安全机制
```java
// 使用executeWithClientAsync确保线程安全
return executeWithClientAsync(platform, () -> {
    QueryUserTagAction action = new QueryUserTagAction();
    // 设置条件和认证
    setAuthentication(action, platform);
    return action.call();
});
```

### 认证信息处理
```java
// 自动根据akType选择认证方式
setAuthentication(action, platform);
// 内部实现：
// if (platform.getAkType() == 0) {
//     action.sessionId = platform.getZsTackPlatform().getToken();
// } else {
//     action.accessKeyId = platform.getUsername();
//     action.accessKeySecret = platform.getPassword();
// }
```

### 条件设置
```java
// 基础方法：自动构建条件
if (resourceUuid != null && resourceType != null) {
    action.conditions = java.util.Arrays.asList(
        "resourceUuid=" + resourceUuid,
        "resourceType=" + resourceType
    );
}

// 高级方法：使用自定义条件
if (conditions != null && !conditions.isEmpty()) {
    action.conditions = conditions;
}
```

## 兼容性说明

- **向后兼容**：不影响现有代码
- **API一致性**：与现有异步方法保持一致的接口设计
- **错误处理**：使用相同的异常处理机制
- **性能影响**：无额外性能开销，复用现有的线程安全机制

## 质量保证

- **代码审查**：遵循现有代码风格和最佳实践
- **线程安全**：基于已验证的多线程安全机制
- **错误处理**：完整的异常处理和日志记录
- **文档完整**：提供完整的使用文档和示例

## 总结

本次实现完全满足用户的所有要求：
1. ✅ 参考了现有SDK API的异步实现模式和代码风格
2. ✅ 支持resourceUuid和resourceType参数
3. ✅ 根据platform.getAkType()自动选择认证方式
4. ✅ 返回QueryUserTagAction.Result类型
5. ✅ 确保了线程安全性，特别是token获取部分
6. ✅ 保持了与现有代码的完全一致性

新方法已经可以立即投入使用，并且提供了完整的测试和文档支持。
